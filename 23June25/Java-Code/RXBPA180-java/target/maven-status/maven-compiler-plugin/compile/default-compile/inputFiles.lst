/Users/<USER>/Projects/POCs/Wells-Fargo-wca4z-June-25/23June25/Java-Code/RXBPA180-java/src/main/java/com/ibm/wcaz/implementation/WsDate1.java
/Users/<USER>/Projects/POCs/Wells-Fargo-wca4z-June-25/23June25/Java-Code/RXBPA180-java/src/main/java/com/ibm/wcaz/implementation/SqlErrmsg.java
/Users/<USER>/Projects/POCs/Wells-Fargo-wca4z-June-25/23June25/Java-Code/RXBPA180-java/src/main/java/com/ibm/wcaz/implementation/WsTime.java
/Users/<USER>/Projects/POCs/Wells-Fargo-wca4z-June-25/23June25/Java-Code/RXBPA180-java/src/main/java/com/ibm/wcaz/implementation/WsCountFields.java
/Users/<USER>/Projects/POCs/Wells-Fargo-wca4z-June-25/23June25/Java-Code/RXBPA180-java/src/main/java/com/ibm/wcaz/implementation/Rxbpa180.java
/Users/<USER>/Projects/POCs/Wells-Fargo-wca4z-June-25/23June25/Java-Code/RXBPA180-java/src/main/java/com/ibm/wcaz/implementation/HRegHdrRec.java
/Users/<USER>/Projects/POCs/Wells-Fargo-wca4z-June-25/23June25/Java-Code/RXBPA180-java/src/main/java/com/ibm/wcaz/implementation/AbtTestFacilityArea.java
/Users/<USER>/Projects/POCs/Wells-Fargo-wca4z-June-25/23June25/Java-Code/RXBPA180-java/src/main/java/com/ibm/wcaz/implementation/Filler37.java
/Users/<USER>/Projects/POCs/Wells-Fargo-wca4z-June-25/23June25/Java-Code/RXBPA180-java/src/main/java/com/ibm/wcaz/implementation/WsSystemTime.java
/Users/<USER>/Projects/POCs/Wells-Fargo-wca4z-June-25/23June25/Java-Code/RXBPA180-java/src/main/java/com/ibm/wcaz/implementation/WsFlags.java
/Users/<USER>/Projects/POCs/Wells-Fargo-wca4z-June-25/23June25/Java-Code/RXBPA180-java/src/main/java/com/ibm/wcaz/implementation/DataAccessStatus.java
/Users/<USER>/Projects/POCs/Wells-Fargo-wca4z-June-25/23June25/Java-Code/RXBPA180-java/src/main/java/com/ibm/wcaz/implementation/WsDistNoChar.java
/Users/<USER>/Projects/POCs/Wells-Fargo-wca4z-June-25/23June25/Java-Code/RXBPA180-java/src/main/java/com/ibm/wcaz/implementation/Dclvwmtrli.java
/Users/<USER>/Projects/POCs/Wells-Fargo-wca4z-June-25/23June25/Java-Code/RXBPA180-java/src/main/java/com/ibm/wcaz/implementation/Filler35.java
/Users/<USER>/Projects/POCs/Wells-Fargo-wca4z-June-25/23June25/Java-Code/RXBPA180-java/src/main/java/com/ibm/wcaz/datamodel/Vwmtrli.java
/Users/<USER>/Projects/POCs/Wells-Fargo-wca4z-June-25/23June25/Java-Code/RXBPA180-java/src/main/java/com/ibm/wcaz/datamodel/Vwmcucp.java
/Users/<USER>/Projects/POCs/Wells-Fargo-wca4z-June-25/23June25/Java-Code/RXBPA180-java/src/main/java/com/ibm/wcaz/implementation/WsAaaDate.java
/Users/<USER>/Projects/POCs/Wells-Fargo-wca4z-June-25/23June25/Java-Code/RXBPA180-java/src/main/java/com/ibm/wcaz/implementation/LegalName.java
/Users/<USER>/Projects/POCs/Wells-Fargo-wca4z-June-25/23June25/Java-Code/RXBPA180-java/src/main/java/com/ibm/wcaz/implementation/WsSysDate.java
/Users/<USER>/Projects/POCs/Wells-Fargo-wca4z-June-25/23June25/Java-Code/RXBPA180-java/src/main/java/com/ibm/wcaz/implementation/AbtProgramFunction.java
/Users/<USER>/Projects/POCs/Wells-Fargo-wca4z-June-25/23June25/Java-Code/RXBPA180-java/src/main/java/com/ibm/wcaz/implementation/AbtDaFunctionDli.java
/Users/<USER>/Projects/POCs/Wells-Fargo-wca4z-June-25/23June25/Java-Code/RXBPA180-java/src/main/java/com/ibm/wcaz/implementation/Dclvwmcucp.java
/Users/<USER>/Projects/POCs/Wells-Fargo-wca4z-June-25/23June25/Java-Code/RXBPA180-java/src/main/java/com/ibm/wcaz/datamodel/Vwmcu00.java
/Users/<USER>/Projects/POCs/Wells-Fargo-wca4z-June-25/23June25/Java-Code/RXBPA180-java/src/main/java/com/ibm/wcaz/implementation/AbtControlInfo.java
/Users/<USER>/Projects/POCs/Wells-Fargo-wca4z-June-25/23June25/Java-Code/RXBPA180-java/src/main/java/com/ibm/wcaz/implementation/AaaFormatInRecord.java
/Users/<USER>/Projects/POCs/Wells-Fargo-wca4z-June-25/23June25/Java-Code/RXBPA180-java/src/main/java/com/ibm/wcaz/implementation/WsSaveArea.java
/Users/<USER>/Projects/POCs/Wells-Fargo-wca4z-June-25/23June25/Java-Code/RXBPA180-java/src/main/java/com/ibm/wcaz/implementation/AaaFormatOutRecord.java
/Users/<USER>/Projects/POCs/Wells-Fargo-wca4z-June-25/23June25/Java-Code/RXBPA180-java/src/main/java/com/ibm/wcaz/implementation/DRegDtlRec.java
/Users/<USER>/Projects/POCs/Wells-Fargo-wca4z-June-25/23June25/Java-Code/RXBPA180-java/src/main/java/com/ibm/wcaz/implementation/AaaInboundLayout.java
/Users/<USER>/Projects/POCs/Wells-Fargo-wca4z-June-25/23June25/Java-Code/RXBPA180-java/src/main/java/com/ibm/wcaz/implementation/Sqlca.java
/Users/<USER>/Projects/POCs/Wells-Fargo-wca4z-June-25/23June25/Java-Code/RXBPA180-java/src/main/java/com/ibm/wcaz/implementation/BatchErrorArea.java
/Users/<USER>/Projects/POCs/Wells-Fargo-wca4z-June-25/23June25/Java-Code/RXBPA180-java/src/main/java/com/ibm/wcaz/implementation/Filler39.java
/Users/<USER>/Projects/POCs/Wells-Fargo-wca4z-June-25/23June25/Java-Code/RXBPA180-java/src/main/java/com/ibm/wcaz/implementation/AbtErrorSection.java
/Users/<USER>/Projects/POCs/Wells-Fargo-wca4z-June-25/23June25/Java-Code/RXBPA180-java/src/main/java/com/ibm/wcaz/implementation/ZRegTrlRec.java
/Users/<USER>/Projects/POCs/Wells-Fargo-wca4z-June-25/23June25/Java-Code/RXBPA180-java/src/main/java/com/ibm/wcaz/implementation/AbtDataAccessInfo.java
/Users/<USER>/Projects/POCs/Wells-Fargo-wca4z-June-25/23June25/Java-Code/RXBPA180-java/src/main/java/com/ibm/wcaz/implementation/AbtPgmErrorData.java
/Users/<USER>/Projects/POCs/Wells-Fargo-wca4z-June-25/23June25/Java-Code/RXBPA180-java/src/main/java/com/ibm/wcaz/implementation/AbnormalTerminationArea.java
/Users/<USER>/Projects/POCs/Wells-Fargo-wca4z-June-25/23June25/Java-Code/RXBPA180-java/src/main/java/com/ibm/wcaz/implementation/Filler41.java
