package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class HRegHdrRec implements Cloneable, Comparable<HRegHdrRec> {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private static final String H_HDR_REC_VALUE = "URBH";
    private static final String H_DOC_DATA_VALUE = "UNIT REGISTRATION       ";
    
    private String hRecordType = "";
    private String hCdfCustId = "1133";
    private String hCdfCustCode = "0000";
    private String hCreateDate = "";
    private String hCreateTime = "";
    private String hDocType = "";
    private String hMfgName = "";
    
    /** Initialize fields to non-null default values */
    public HRegHdrRec() {
        initFiller();
    }
    
    /** Initialize all fields to provided values */
    public HRegHdrRec(String hRecordType, String hCdfCustId, String hCdfCustCode, String hCreateDate, String hCreateTime, String hDocType, String hMfgName) {
        this.hRecordType = hRecordType;
        this.hCdfCustId = hCdfCustId;
        this.hCdfCustCode = hCdfCustCode;
        this.hCreateDate = hCreateDate;
        this.hCreateTime = hCreateTime;
        this.hDocType = hDocType;
        this.hMfgName = hMfgName;
        initFiller();
    }
    
    @Override
    public HRegHdrRec clone() throws CloneNotSupportedException {
        HRegHdrRec cloned = (HRegHdrRec) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code HRegHdrRec} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected HRegHdrRec(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code HRegHdrRec} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected HRegHdrRec(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code HRegHdrRec} object
     * @see #setBytes(byte[], int)
     */
    public static HRegHdrRec fromBytes(byte[] bytes, int offset) {
        return new HRegHdrRec(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code HRegHdrRec} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static HRegHdrRec fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code HRegHdrRec} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static HRegHdrRec fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getHRecordType() {
        return this.hRecordType;
    }
    
    public void setHRecordType(String hRecordType) {
        this.hRecordType = hRecordType;
    }
    
    public boolean isHHdrRec() {
        return hRecordType.equals(H_HDR_REC_VALUE);
    }
    
    public void setHHdrRec() {
        hRecordType = H_HDR_REC_VALUE;
    }
    
    public String getHCdfCustId() {
        return this.hCdfCustId;
    }
    
    public void setHCdfCustId(String hCdfCustId) {
        this.hCdfCustId = hCdfCustId;
    }
    
    public String getHCdfCustCode() {
        return this.hCdfCustCode;
    }
    
    public void setHCdfCustCode(String hCdfCustCode) {
        this.hCdfCustCode = hCdfCustCode;
    }
    
    public String getHCreateDate() {
        return this.hCreateDate;
    }
    
    public void setHCreateDate(String hCreateDate) {
        this.hCreateDate = hCreateDate;
    }
    
    public String getHCreateTime() {
        return this.hCreateTime;
    }
    
    public void setHCreateTime(String hCreateTime) {
        this.hCreateTime = hCreateTime;
    }
    
    public String getHDocType() {
        return this.hDocType;
    }
    
    public void setHDocType(String hDocType) {
        this.hDocType = hDocType;
    }
    
    public boolean isHDocData() {
        return hDocType.equals(H_DOC_DATA_VALUE);
    }
    
    public void setHDocData() {
        hDocType = H_DOC_DATA_VALUE;
    }
    
    public String getHMfgName() {
        return this.hMfgName;
    }
    
    public void setHMfgName(String hMfgName) {
        this.hMfgName = hMfgName;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        hRecordType = "";
        hCdfCustId = "";
        hCdfCustCode = "";
        hCreateDate = "";
        hCreateTime = "";
        hDocType = "";
        hMfgName = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder();
        s.append("{ hRecordType=\"");
        s.append(getHRecordType());
        s.append("\"");
        s.append(", hCdfCustId=\"");
        s.append(getHCdfCustId());
        s.append("\"");
        s.append(", hCdfCustCode=\"");
        s.append(getHCdfCustCode());
        s.append("\"");
        s.append(", hCreateDate=\"");
        s.append(getHCreateDate());
        s.append("\"");
        s.append(", hCreateTime=\"");
        s.append(getHCreateTime());
        s.append("\"");
        s.append(", hDocType=\"");
        s.append(getHDocType());
        s.append("\"");
        s.append(", hMfgName=\"");
        s.append(getHMfgName());
        s.append("\"");
        s.append(", filler23=\"");
        s.append(new String(filler23, encoding));
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(HRegHdrRec that) {
        return this.hRecordType.equals(that.hRecordType) &&
            this.hCdfCustId.equals(that.hCdfCustId) &&
            this.hCdfCustCode.equals(that.hCdfCustCode) &&
            this.hCreateDate.equals(that.hCreateDate) &&
            this.hCreateTime.equals(that.hCreateTime) &&
            this.hDocType.equals(that.hDocType) &&
            this.hMfgName.equals(that.hMfgName) &&
            Arrays.equals(this.filler23, that.filler23);
    }
    
    /** Per-field equality comparison; only returns equal if argument is the same class */
    @Override
    public boolean equals(Object that) {
        return (that instanceof HRegHdrRec other) && other.canEqual(this) && this.equals(other);
    }
    
    /** Does {@code that} have the same runtime type as {@code this}? */
    public boolean canEqual(Object that) {
        return that instanceof HRegHdrRec;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * result + Objects.hashCode(hRecordType);
        result = 31 * result + Objects.hashCode(hCdfCustId);
        result = 31 * result + Objects.hashCode(hCdfCustCode);
        result = 31 * result + Objects.hashCode(hCreateDate);
        result = 31 * result + Objects.hashCode(hCreateTime);
        result = 31 * result + Objects.hashCode(hDocType);
        result = 31 * result + Objects.hashCode(hMfgName);
        result = 31 * result + Arrays.hashCode(filler23);
        return result;
    }
    
    @Override
    public int compareTo(HRegHdrRec that) {
        int c = 0;
        c = this.hRecordType.compareTo(that.hRecordType);
        if ( c != 0 ) return c;
        c = this.hCdfCustId.compareTo(that.hCdfCustId);
        if ( c != 0 ) return c;
        c = this.hCdfCustCode.compareTo(that.hCdfCustCode);
        if ( c != 0 ) return c;
        c = this.hCreateDate.compareTo(that.hCreateDate);
        if ( c != 0 ) return c;
        c = this.hCreateTime.compareTo(that.hCreateTime);
        if ( c != 0 ) return c;
        c = this.hDocType.compareTo(that.hDocType);
        if ( c != 0 ) return c;
        c = this.hMfgName.compareTo(that.hMfgName);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler23, that.filler23);
        if ( c == 0 && !(that.canEqual(this) && this.canEqual(that)) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
    }
    
    private static final StringField H_RECORD_TYPE = factory.getStringField(4);
    private static final StringField H_CDF_CUST_ID = factory.getStringField(4);
    private static final StringField H_CDF_CUST_CODE = factory.getStringField(4);
    private static final StringField H_CREATE_DATE = factory.getStringField(10);
    private static final StringField H_CREATE_TIME = factory.getStringField(5);
    private static final StringField H_DOC_TYPE = factory.getStringField(24);
    private static final StringField H_MFG_NAME = factory.getStringField(20);
    private static final ByteArrayField FILLER_23 = factory.getByteArrayField(129);
    private byte[] filler23 = new byte[129];
    private void initFiller() {
        new StringField(0, 129).putString("", filler23);
    }
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code HRegHdrRec} object
     * @param bytes  preallocated byte array to store the object serialization
     * @param offset offset in the byte array where serialization should begin
     * @return the byte array, updated with the newly added data formatted as in the {@code H-REG-HDR-REC} record
     * @see "H-REG-HDR-REC record at RXBPA180.cbl:56"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        H_RECORD_TYPE.putString(hRecordType, bytes, offset);
        H_CDF_CUST_ID.putString(hCdfCustId, bytes, offset);
        H_CDF_CUST_CODE.putString(hCdfCustCode, bytes, offset);
        H_CREATE_DATE.putString(hCreateDate, bytes, offset);
        H_CREATE_TIME.putString(hCreateTime, bytes, offset);
        H_DOC_TYPE.putString(hDocType, bytes, offset);
        H_MFG_NAME.putString(hMfgName, bytes, offset);
        FILLER_23.putByteArray(filler23, bytes, offset);
        return bytes;
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code HRegHdrRec} object,
     * starting at the beginning of the array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes(byte[] bytes) {
        return getBytes(bytes, 0);
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code HRegHdrRec} object,
     * allocating a new array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes() {
        return getBytes(new byte[numBytes()]);
    }
    
    /**
     * Retrieves a COBOL-format string representation of the {@code HRegHdrRec} object
     * @return the result of {@link #getBytes()} interpreted using the IBM-1047 EBCDIC encoding
     */
    public final String toByteString() {
        return new String(getBytes(), encoding);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array
     * @param bytes  byte array formatted as in the {@code H-REG-HDR-REC} record; will be space-padded to length if necessary
     * @param offset offset in the byte array where deserialization should begin
     * @see "H-REG-HDR-REC record at RXBPA180.cbl:56"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        hRecordType = H_RECORD_TYPE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        hCdfCustId = H_CDF_CUST_ID.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        hCdfCustCode = H_CDF_CUST_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        hCreateDate = H_CREATE_DATE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        hCreateTime = H_CREATE_TIME.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        hDocType = H_DOC_TYPE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        hMfgName = H_MFG_NAME.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_23.getByteArray(bytes, offset);
    }
    
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array,
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(byte[] bytes) {
        setBytes(bytes, 0);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format string.
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(String bytes) {
        setBytes(bytes.getBytes(encoding));
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
