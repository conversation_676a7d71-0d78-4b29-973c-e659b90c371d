package com.ibm.wcaz.implementation;

import com.ibm.jzos.ZFile;
import com.ibm.jzos.ZFileException;
import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class AaaFormatInRecord implements Cloneable, Comparable<AaaFormatInRecord> {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    /** Initialize fields to non-null default values */
    public AaaFormatInRecord() {}
    
    @Override
    public AaaFormatInRecord clone() throws CloneNotSupportedException {
        AaaFormatInRecord cloned = (AaaFormatInRecord) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code AaaFormatInRecord} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected AaaFormatInRecord(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code AaaFormatInRecord} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected AaaFormatInRecord(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code AaaFormatInRecord} object
     * @see #setBytes(byte[], int)
     */
    public static AaaFormatInRecord fromBytes(byte[] bytes, int offset) {
        return new AaaFormatInRecord(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code AaaFormatInRecord} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static AaaFormatInRecord fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code AaaFormatInRecord} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static AaaFormatInRecord fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public int readFrom(ZFile file) throws ZFileException {
        byte[] bytes = new byte[this.numBytes()];
        int ret = file.read(bytes);
        setBytes(bytes);
        return ret;
    }
    
    public void writeTo(ZFile file) throws ZFileException {
        file.write(getBytes());
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder();
        s.append("{ filler18=\"");
        s.append(new String(filler18, encoding));
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(AaaFormatInRecord that) {
        return Arrays.equals(this.filler18, that.filler18);
    }
    
    /** Per-field equality comparison; only returns equal if argument is the same class */
    @Override
    public boolean equals(Object that) {
        return (that instanceof AaaFormatInRecord other) && other.canEqual(this) && this.equals(other);
    }
    
    /** Does {@code that} have the same runtime type as {@code this}? */
    public boolean canEqual(Object that) {
        return that instanceof AaaFormatInRecord;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * result + Arrays.hashCode(filler18);
        return result;
    }
    
    @Override
    public int compareTo(AaaFormatInRecord that) {
        int c = 0;
        c = Arrays.compare(this.filler18, that.filler18);
        if ( c == 0 && !(that.canEqual(this) && this.canEqual(that)) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
    }
    
    private static final ByteArrayField FILLER_18 = factory.getByteArrayField(200);
    private byte[] filler18 = new byte[200];
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code AaaFormatInRecord} object
     * @param bytes  preallocated byte array to store the object serialization
     * @param offset offset in the byte array where serialization should begin
     * @return the byte array, updated with the newly added data formatted as in the {@code AAA-FORMAT-IN-RECORD} record
     * @see "AAA-FORMAT-IN-RECORD record at RXBPA180.cbl:33"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        FILLER_18.putByteArray(filler18, bytes, offset);
        return bytes;
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code AaaFormatInRecord} object,
     * starting at the beginning of the array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes(byte[] bytes) {
        return getBytes(bytes, 0);
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code AaaFormatInRecord} object,
     * allocating a new array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes() {
        return getBytes(new byte[numBytes()]);
    }
    
    /**
     * Retrieves a COBOL-format string representation of the {@code AaaFormatInRecord} object
     * @return the result of {@link #getBytes()} interpreted using the IBM-1047 EBCDIC encoding
     */
    public final String toByteString() {
        return new String(getBytes(), encoding);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array
     * @param bytes  byte array formatted as in the {@code AAA-FORMAT-IN-RECORD} record; will be space-padded to length if necessary
     * @param offset offset in the byte array where deserialization should begin
     * @see "AAA-FORMAT-IN-RECORD record at RXBPA180.cbl:33"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        FILLER_18.getByteArray(bytes, offset);
    }
    
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array,
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(byte[] bytes) {
        setBytes(bytes, 0);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format string.
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(String bytes) {
        setBytes(bytes.getBytes(encoding));
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
