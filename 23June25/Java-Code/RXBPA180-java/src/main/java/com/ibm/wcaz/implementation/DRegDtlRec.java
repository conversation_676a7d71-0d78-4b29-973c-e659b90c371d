package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class DRegDtlRec implements Cloneable, Comparable<DRegDtlRec> {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private static final String D_DTL_REC_VALUE = "URBD";
    
    private String dRecordType = "";
    private String dCdfCustId = "1133";
    private String dCdfCustCode = "0000";
    private String dCreateDate = "";
    private String dCreateTime = "";
    private String dVendDlrNo = "";
    private String dVendDlrName = "";
    private String dModelNbr = "";
    private String dModelDesc = "";
    private String dSerialNbr = "";
    private String dRegComplDate = "";
    private String dRegType = "";
    private String dDistNo = "";
    
    /** Initialize fields to non-null default values */
    public DRegDtlRec() {
        initFiller();
    }
    
    /** Initialize all fields to provided values */
    public DRegDtlRec(String dRecordType, String dCdfCustId, String dCdfCustCode, String dCreateDate, String dCreateTime, String dVendDlrNo, String dVendDlrName, String dModelNbr, String dModelDesc, String dSerialNbr, String dRegComplDate, String dRegType, String dDistNo) {
        this.dRecordType = dRecordType;
        this.dCdfCustId = dCdfCustId;
        this.dCdfCustCode = dCdfCustCode;
        this.dCreateDate = dCreateDate;
        this.dCreateTime = dCreateTime;
        this.dVendDlrNo = dVendDlrNo;
        this.dVendDlrName = dVendDlrName;
        this.dModelNbr = dModelNbr;
        this.dModelDesc = dModelDesc;
        this.dSerialNbr = dSerialNbr;
        this.dRegComplDate = dRegComplDate;
        this.dRegType = dRegType;
        this.dDistNo = dDistNo;
        initFiller();
    }
    
    @Override
    public DRegDtlRec clone() throws CloneNotSupportedException {
        DRegDtlRec cloned = (DRegDtlRec) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code DRegDtlRec} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected DRegDtlRec(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code DRegDtlRec} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected DRegDtlRec(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code DRegDtlRec} object
     * @see #setBytes(byte[], int)
     */
    public static DRegDtlRec fromBytes(byte[] bytes, int offset) {
        return new DRegDtlRec(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code DRegDtlRec} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static DRegDtlRec fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code DRegDtlRec} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static DRegDtlRec fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getDRecordType() {
        return this.dRecordType;
    }
    
    public void setDRecordType(String dRecordType) {
        this.dRecordType = dRecordType;
    }
    
    public boolean isDDtlRec() {
        return dRecordType.equals(D_DTL_REC_VALUE);
    }
    
    public void setDDtlRec() {
        dRecordType = D_DTL_REC_VALUE;
    }
    
    public String getDCdfCustId() {
        return this.dCdfCustId;
    }
    
    public void setDCdfCustId(String dCdfCustId) {
        this.dCdfCustId = dCdfCustId;
    }
    
    public String getDCdfCustCode() {
        return this.dCdfCustCode;
    }
    
    public void setDCdfCustCode(String dCdfCustCode) {
        this.dCdfCustCode = dCdfCustCode;
    }
    
    public String getDCreateDate() {
        return this.dCreateDate;
    }
    
    public void setDCreateDate(String dCreateDate) {
        this.dCreateDate = dCreateDate;
    }
    
    public String getDCreateTime() {
        return this.dCreateTime;
    }
    
    public void setDCreateTime(String dCreateTime) {
        this.dCreateTime = dCreateTime;
    }
    
    public String getDVendDlrNo() {
        return this.dVendDlrNo;
    }
    
    public void setDVendDlrNo(String dVendDlrNo) {
        this.dVendDlrNo = dVendDlrNo;
    }
    
    public String getDVendDlrName() {
        return this.dVendDlrName;
    }
    
    public void setDVendDlrName(String dVendDlrName) {
        this.dVendDlrName = dVendDlrName;
    }
    
    public String getDModelNbr() {
        return this.dModelNbr;
    }
    
    public void setDModelNbr(String dModelNbr) {
        this.dModelNbr = dModelNbr;
    }
    
    public String getDModelDesc() {
        return this.dModelDesc;
    }
    
    public void setDModelDesc(String dModelDesc) {
        this.dModelDesc = dModelDesc;
    }
    
    public String getDSerialNbr() {
        return this.dSerialNbr;
    }
    
    public void setDSerialNbr(String dSerialNbr) {
        this.dSerialNbr = dSerialNbr;
    }
    
    public String getDRegComplDate() {
        return this.dRegComplDate;
    }
    
    public void setDRegComplDate(String dRegComplDate) {
        this.dRegComplDate = dRegComplDate;
    }
    
    public String getDRegType() {
        return this.dRegType;
    }
    
    public void setDRegType(String dRegType) {
        this.dRegType = dRegType;
    }
    
    public String getDDistNo() {
        return this.dDistNo;
    }
    
    public void setDDistNo(String dDistNo) {
        this.dDistNo = dDistNo;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        dRecordType = "";
        dCdfCustId = "";
        dCdfCustCode = "";
        dCreateDate = "";
        dCreateTime = "";
        dVendDlrNo = "";
        dVendDlrName = "";
        dModelNbr = "";
        dModelDesc = "";
        dSerialNbr = "";
        dRegComplDate = "";
        dRegType = "";
        dDistNo = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder();
        s.append("{ dRecordType=\"");
        s.append(getDRecordType());
        s.append("\"");
        s.append(", dCdfCustId=\"");
        s.append(getDCdfCustId());
        s.append("\"");
        s.append(", dCdfCustCode=\"");
        s.append(getDCdfCustCode());
        s.append("\"");
        s.append(", dCreateDate=\"");
        s.append(getDCreateDate());
        s.append("\"");
        s.append(", dCreateTime=\"");
        s.append(getDCreateTime());
        s.append("\"");
        s.append(", dVendDlrNo=\"");
        s.append(getDVendDlrNo());
        s.append("\"");
        s.append(", dVendDlrName=\"");
        s.append(getDVendDlrName());
        s.append("\"");
        s.append(", dModelNbr=\"");
        s.append(getDModelNbr());
        s.append("\"");
        s.append(", dModelDesc=\"");
        s.append(getDModelDesc());
        s.append("\"");
        s.append(", dSerialNbr=\"");
        s.append(getDSerialNbr());
        s.append("\"");
        s.append(", dRegComplDate=\"");
        s.append(getDRegComplDate());
        s.append("\"");
        s.append(", dRegType=\"");
        s.append(getDRegType());
        s.append("\"");
        s.append(", dDistNo=\"");
        s.append(getDDistNo());
        s.append("\"");
        s.append(", filler24=\"");
        s.append(new String(filler24, encoding));
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(DRegDtlRec that) {
        return this.dRecordType.equals(that.dRecordType) &&
            this.dCdfCustId.equals(that.dCdfCustId) &&
            this.dCdfCustCode.equals(that.dCdfCustCode) &&
            this.dCreateDate.equals(that.dCreateDate) &&
            this.dCreateTime.equals(that.dCreateTime) &&
            this.dVendDlrNo.equals(that.dVendDlrNo) &&
            this.dVendDlrName.equals(that.dVendDlrName) &&
            this.dModelNbr.equals(that.dModelNbr) &&
            this.dModelDesc.equals(that.dModelDesc) &&
            this.dSerialNbr.equals(that.dSerialNbr) &&
            this.dRegComplDate.equals(that.dRegComplDate) &&
            this.dRegType.equals(that.dRegType) &&
            this.dDistNo.equals(that.dDistNo) &&
            Arrays.equals(this.filler24, that.filler24);
    }
    
    /** Per-field equality comparison; only returns equal if argument is the same class */
    @Override
    public boolean equals(Object that) {
        return (that instanceof DRegDtlRec other) && other.canEqual(this) && this.equals(other);
    }
    
    /** Does {@code that} have the same runtime type as {@code this}? */
    public boolean canEqual(Object that) {
        return that instanceof DRegDtlRec;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * result + Objects.hashCode(dRecordType);
        result = 31 * result + Objects.hashCode(dCdfCustId);
        result = 31 * result + Objects.hashCode(dCdfCustCode);
        result = 31 * result + Objects.hashCode(dCreateDate);
        result = 31 * result + Objects.hashCode(dCreateTime);
        result = 31 * result + Objects.hashCode(dVendDlrNo);
        result = 31 * result + Objects.hashCode(dVendDlrName);
        result = 31 * result + Objects.hashCode(dModelNbr);
        result = 31 * result + Objects.hashCode(dModelDesc);
        result = 31 * result + Objects.hashCode(dSerialNbr);
        result = 31 * result + Objects.hashCode(dRegComplDate);
        result = 31 * result + Objects.hashCode(dRegType);
        result = 31 * result + Objects.hashCode(dDistNo);
        result = 31 * result + Arrays.hashCode(filler24);
        return result;
    }
    
    @Override
    public int compareTo(DRegDtlRec that) {
        int c = 0;
        c = this.dRecordType.compareTo(that.dRecordType);
        if ( c != 0 ) return c;
        c = this.dCdfCustId.compareTo(that.dCdfCustId);
        if ( c != 0 ) return c;
        c = this.dCdfCustCode.compareTo(that.dCdfCustCode);
        if ( c != 0 ) return c;
        c = this.dCreateDate.compareTo(that.dCreateDate);
        if ( c != 0 ) return c;
        c = this.dCreateTime.compareTo(that.dCreateTime);
        if ( c != 0 ) return c;
        c = this.dVendDlrNo.compareTo(that.dVendDlrNo);
        if ( c != 0 ) return c;
        c = this.dVendDlrName.compareTo(that.dVendDlrName);
        if ( c != 0 ) return c;
        c = this.dModelNbr.compareTo(that.dModelNbr);
        if ( c != 0 ) return c;
        c = this.dModelDesc.compareTo(that.dModelDesc);
        if ( c != 0 ) return c;
        c = this.dSerialNbr.compareTo(that.dSerialNbr);
        if ( c != 0 ) return c;
        c = this.dRegComplDate.compareTo(that.dRegComplDate);
        if ( c != 0 ) return c;
        c = this.dRegType.compareTo(that.dRegType);
        if ( c != 0 ) return c;
        c = this.dDistNo.compareTo(that.dDistNo);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler24, that.filler24);
        if ( c == 0 && !(that.canEqual(this) && this.canEqual(that)) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
    }
    
    private static final StringField D_RECORD_TYPE = factory.getStringField(4);
    private static final StringField D_CDF_CUST_ID = factory.getStringField(4);
    private static final StringField D_CDF_CUST_CODE = factory.getStringField(4);
    private static final StringField D_CREATE_DATE = factory.getStringField(10);
    private static final StringField D_CREATE_TIME = factory.getStringField(5);
    private static final StringField D_VEND_DLR_NO = factory.getStringField(13);
    private static final StringField D_VEND_DLR_NAME = factory.getStringField(35);
    private static final StringField D_MODEL_NBR = factory.getStringField(12);
    private static final StringField D_MODEL_DESC = factory.getStringField(20);
    private static final StringField D_SERIAL_NBR = factory.getStringField(17);
    private static final StringField D_REG_COMPL_DATE = factory.getStringField(10);
    private static final StringField D_REG_TYPE = factory.getStringField(3);
    private static final StringField D_DIST_NO = factory.getStringField(6);
    private static final ByteArrayField FILLER_24 = factory.getByteArrayField(57);
    private byte[] filler24 = new byte[57];
    private void initFiller() {
        new StringField(0, 57).putString("", filler24);
    }
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code DRegDtlRec} object
     * @param bytes  preallocated byte array to store the object serialization
     * @param offset offset in the byte array where serialization should begin
     * @return the byte array, updated with the newly added data formatted as in the {@code D-REG-DTL-REC} record
     * @see "D-REG-DTL-REC record at RXBPA180.cbl:68"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        D_RECORD_TYPE.putString(dRecordType, bytes, offset);
        D_CDF_CUST_ID.putString(dCdfCustId, bytes, offset);
        D_CDF_CUST_CODE.putString(dCdfCustCode, bytes, offset);
        D_CREATE_DATE.putString(dCreateDate, bytes, offset);
        D_CREATE_TIME.putString(dCreateTime, bytes, offset);
        D_VEND_DLR_NO.putString(dVendDlrNo, bytes, offset);
        D_VEND_DLR_NAME.putString(dVendDlrName, bytes, offset);
        D_MODEL_NBR.putString(dModelNbr, bytes, offset);
        D_MODEL_DESC.putString(dModelDesc, bytes, offset);
        D_SERIAL_NBR.putString(dSerialNbr, bytes, offset);
        D_REG_COMPL_DATE.putString(dRegComplDate, bytes, offset);
        D_REG_TYPE.putString(dRegType, bytes, offset);
        D_DIST_NO.putString(dDistNo, bytes, offset);
        FILLER_24.putByteArray(filler24, bytes, offset);
        return bytes;
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code DRegDtlRec} object,
     * starting at the beginning of the array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes(byte[] bytes) {
        return getBytes(bytes, 0);
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code DRegDtlRec} object,
     * allocating a new array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes() {
        return getBytes(new byte[numBytes()]);
    }
    
    /**
     * Retrieves a COBOL-format string representation of the {@code DRegDtlRec} object
     * @return the result of {@link #getBytes()} interpreted using the IBM-1047 EBCDIC encoding
     */
    public final String toByteString() {
        return new String(getBytes(), encoding);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array
     * @param bytes  byte array formatted as in the {@code D-REG-DTL-REC} record; will be space-padded to length if necessary
     * @param offset offset in the byte array where deserialization should begin
     * @see "D-REG-DTL-REC record at RXBPA180.cbl:68"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        dRecordType = D_RECORD_TYPE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        dCdfCustId = D_CDF_CUST_ID.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        dCdfCustCode = D_CDF_CUST_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        dCreateDate = D_CREATE_DATE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        dCreateTime = D_CREATE_TIME.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        dVendDlrNo = D_VEND_DLR_NO.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        dVendDlrName = D_VEND_DLR_NAME.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        dModelNbr = D_MODEL_NBR.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        dModelDesc = D_MODEL_DESC.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        dSerialNbr = D_SERIAL_NBR.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        dRegComplDate = D_REG_COMPL_DATE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        dRegType = D_REG_TYPE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        dDistNo = D_DIST_NO.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_24.getByteArray(bytes, offset);
    }
    
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array,
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(byte[] bytes) {
        setBytes(bytes, 0);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format string.
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(String bytes) {
        setBytes(bytes.getBytes(encoding));
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
