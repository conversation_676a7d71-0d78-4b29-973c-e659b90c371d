package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.BinaryAsIntField;
import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class AbtControlInfo implements Cloneable, Comparable<AbtControlInfo> {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private static final char ABT_DO_ABEND_VALUE = 'A';
    private static final char ABT_DO_WRITE_VALUE = 'E';
    private static final char ABT_DO_TRANSFER_VALUE = 'R';
    private static final char ABT_CONTINUE_PROCESS_VALUE = ' ';
    private static final String ABT_PGM_IS_TSOPGM_VALUE = "TSO ";
    private static final String ABT_PGM_IS_IMSDYN_VALUE = "IDYN";
    private static final String ABT_PGM_IS_IMSSTAT_VALUE = "ISTA";
    private static final String ABT_PGM_IS_IMSDRVR_VALUE = "IDRV";
    private static final String ABT_PGM_IS_CICSPGM_VALUE = "CICS";
    private static final String ABT_PGM_IS_BATCHPGM_VALUE = "BATC";
    
    private char abtControlIndicator = 'E';
    private char abtInProgress = 'N';
    private String abtDynamicControlPgm = "MXBPCABT";
    private int abtDynamicControlRc = 0;
    private int abtNumberOfStdParms = 7;
    private int abtNumberOfUserParms = 0;
    private int abtNumberOfDaParms = 0;
    private String abtPgmGenType = "BATC";
    private String abtPgmGenRelLevel = "2.1A";
    private String abtPgmName = "";
    private String abtPgmTranCode = "";
    private String abtPgmMapName = "";
    private String abtNextProgramNameHdr = "";
    private String abtNextProgramNameId = "";
    private String abtErrorMessage = "";
    
    /** Initialize fields to non-null default values */
    public AbtControlInfo() {
        initFiller();
    }
    
    /** Initialize all fields to provided values */
    public AbtControlInfo(char abtControlIndicator, char abtInProgress, String abtDynamicControlPgm, int abtDynamicControlRc, int abtNumberOfStdParms, int abtNumberOfUserParms, int abtNumberOfDaParms, String abtPgmGenType, String abtPgmGenRelLevel, String abtPgmName, String abtPgmTranCode, String abtPgmMapName, String abtNextProgramNameHdr, String abtNextProgramNameId, String abtErrorMessage) {
        this.abtControlIndicator = abtControlIndicator;
        this.abtInProgress = abtInProgress;
        this.abtDynamicControlPgm = abtDynamicControlPgm;
        this.abtDynamicControlRc = abtDynamicControlRc;
        this.abtNumberOfStdParms = abtNumberOfStdParms;
        this.abtNumberOfUserParms = abtNumberOfUserParms;
        this.abtNumberOfDaParms = abtNumberOfDaParms;
        this.abtPgmGenType = abtPgmGenType;
        this.abtPgmGenRelLevel = abtPgmGenRelLevel;
        this.abtPgmName = abtPgmName;
        this.abtPgmTranCode = abtPgmTranCode;
        this.abtPgmMapName = abtPgmMapName;
        this.abtNextProgramNameHdr = abtNextProgramNameHdr;
        this.abtNextProgramNameId = abtNextProgramNameId;
        this.abtErrorMessage = abtErrorMessage;
        initFiller();
    }
    
    @Override
    public AbtControlInfo clone() throws CloneNotSupportedException {
        AbtControlInfo cloned = (AbtControlInfo) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code AbtControlInfo} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected AbtControlInfo(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code AbtControlInfo} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected AbtControlInfo(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code AbtControlInfo} object
     * @see #setBytes(byte[], int)
     */
    public static AbtControlInfo fromBytes(byte[] bytes, int offset) {
        return new AbtControlInfo(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code AbtControlInfo} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static AbtControlInfo fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code AbtControlInfo} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static AbtControlInfo fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public char getAbtControlIndicator() {
        return this.abtControlIndicator;
    }
    
    public void setAbtControlIndicator(char abtControlIndicator) {
        this.abtControlIndicator = abtControlIndicator;
    }
    
    public boolean isAbtDoAbend() {
        return abtControlIndicator == ABT_DO_ABEND_VALUE;
    }
    
    public void setAbtDoAbend() {
        abtControlIndicator = ABT_DO_ABEND_VALUE;
    }
    
    public boolean isAbtDoWrite() {
        return abtControlIndicator == ABT_DO_WRITE_VALUE;
    }
    
    public void setAbtDoWrite() {
        abtControlIndicator = ABT_DO_WRITE_VALUE;
    }
    
    public boolean isAbtDoTransfer() {
        return abtControlIndicator == ABT_DO_TRANSFER_VALUE;
    }
    
    public void setAbtDoTransfer() {
        abtControlIndicator = ABT_DO_TRANSFER_VALUE;
    }
    
    public boolean isAbtContinueProcess() {
        return abtControlIndicator == ABT_CONTINUE_PROCESS_VALUE;
    }
    
    public void setAbtContinueProcess() {
        abtControlIndicator = ABT_CONTINUE_PROCESS_VALUE;
    }
    
    public char getAbtInProgress() {
        return this.abtInProgress;
    }
    
    public void setAbtInProgress(char abtInProgress) {
        this.abtInProgress = abtInProgress;
    }
    
    public String getAbtDynamicControlPgm() {
        return this.abtDynamicControlPgm;
    }
    
    public void setAbtDynamicControlPgm(String abtDynamicControlPgm) {
        this.abtDynamicControlPgm = abtDynamicControlPgm;
    }
    
    public int getAbtDynamicControlRc() {
        return this.abtDynamicControlRc;
    }
    
    public void setAbtDynamicControlRc(int abtDynamicControlRc) {
        this.abtDynamicControlRc = abtDynamicControlRc;
    }
    
    public int getAbtNumberOfStdParms() {
        return this.abtNumberOfStdParms;
    }
    
    public void setAbtNumberOfStdParms(int abtNumberOfStdParms) {
        this.abtNumberOfStdParms = abtNumberOfStdParms;
    }
    
    public int getAbtNumberOfUserParms() {
        return this.abtNumberOfUserParms;
    }
    
    public void setAbtNumberOfUserParms(int abtNumberOfUserParms) {
        this.abtNumberOfUserParms = abtNumberOfUserParms;
    }
    
    public int getAbtNumberOfDaParms() {
        return this.abtNumberOfDaParms;
    }
    
    public void setAbtNumberOfDaParms(int abtNumberOfDaParms) {
        this.abtNumberOfDaParms = abtNumberOfDaParms;
    }
    
    public String getAbtPgmGenType() {
        return this.abtPgmGenType;
    }
    
    public void setAbtPgmGenType(String abtPgmGenType) {
        this.abtPgmGenType = abtPgmGenType;
    }
    
    public boolean isAbtPgmIsTsopgm() {
        return abtPgmGenType.equals(ABT_PGM_IS_TSOPGM_VALUE);
    }
    
    public void setAbtPgmIsTsopgm() {
        abtPgmGenType = ABT_PGM_IS_TSOPGM_VALUE;
    }
    
    public boolean isAbtPgmIsImsdyn() {
        return abtPgmGenType.equals(ABT_PGM_IS_IMSDYN_VALUE);
    }
    
    public void setAbtPgmIsImsdyn() {
        abtPgmGenType = ABT_PGM_IS_IMSDYN_VALUE;
    }
    
    public boolean isAbtPgmIsImsstat() {
        return abtPgmGenType.equals(ABT_PGM_IS_IMSSTAT_VALUE);
    }
    
    public void setAbtPgmIsImsstat() {
        abtPgmGenType = ABT_PGM_IS_IMSSTAT_VALUE;
    }
    
    public boolean isAbtPgmIsImsdrvr() {
        return abtPgmGenType.equals(ABT_PGM_IS_IMSDRVR_VALUE);
    }
    
    public void setAbtPgmIsImsdrvr() {
        abtPgmGenType = ABT_PGM_IS_IMSDRVR_VALUE;
    }
    
    public boolean isAbtPgmIsCicspgm() {
        return abtPgmGenType.equals(ABT_PGM_IS_CICSPGM_VALUE);
    }
    
    public void setAbtPgmIsCicspgm() {
        abtPgmGenType = ABT_PGM_IS_CICSPGM_VALUE;
    }
    
    public boolean isAbtPgmIsBatchpgm() {
        return abtPgmGenType.equals(ABT_PGM_IS_BATCHPGM_VALUE);
    }
    
    public void setAbtPgmIsBatchpgm() {
        abtPgmGenType = ABT_PGM_IS_BATCHPGM_VALUE;
    }
    
    public String getAbtPgmGenRelLevel() {
        return this.abtPgmGenRelLevel;
    }
    
    public void setAbtPgmGenRelLevel(String abtPgmGenRelLevel) {
        this.abtPgmGenRelLevel = abtPgmGenRelLevel;
    }
    
    public String getAbtPgmName() {
        return this.abtPgmName;
    }
    
    public void setAbtPgmName(String abtPgmName) {
        this.abtPgmName = abtPgmName;
    }
    
    public String getAbtPgmTranCode() {
        return this.abtPgmTranCode;
    }
    
    public void setAbtPgmTranCode(String abtPgmTranCode) {
        this.abtPgmTranCode = abtPgmTranCode;
    }
    
    public String getAbtPgmMapName() {
        return this.abtPgmMapName;
    }
    
    public void setAbtPgmMapName(String abtPgmMapName) {
        this.abtPgmMapName = abtPgmMapName;
    }
    
    public String getAbtNextProgramNameHdr() {
        return this.abtNextProgramNameHdr;
    }
    
    public void setAbtNextProgramNameHdr(String abtNextProgramNameHdr) {
        this.abtNextProgramNameHdr = abtNextProgramNameHdr;
    }
    
    public String getAbtNextProgramNameId() {
        return this.abtNextProgramNameId;
    }
    
    public void setAbtNextProgramNameId(String abtNextProgramNameId) {
        this.abtNextProgramNameId = abtNextProgramNameId;
    }
    
    public String getAbtErrorMessage() {
        return this.abtErrorMessage;
    }
    
    public void setAbtErrorMessage(String abtErrorMessage) {
        this.abtErrorMessage = abtErrorMessage;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        abtControlIndicator = ' ';
        abtInProgress = ' ';
        abtDynamicControlPgm = "";
        abtDynamicControlRc = 0;
        abtNumberOfStdParms = 0;
        abtNumberOfUserParms = 0;
        abtNumberOfDaParms = 0;
        abtPgmGenType = "";
        abtPgmGenRelLevel = "";
        abtPgmName = "";
        abtPgmTranCode = "";
        abtPgmMapName = "";
        abtNextProgramNameHdr = "";
        abtNextProgramNameId = "";
        abtErrorMessage = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder();
        s.append("{ abtControlIndicator=\"");
        s.append(getAbtControlIndicator());
        s.append("\"");
        s.append(", abtInProgress=\"");
        s.append(getAbtInProgress());
        s.append("\"");
        s.append(", abtDynamicControlPgm=\"");
        s.append(getAbtDynamicControlPgm());
        s.append("\"");
        s.append(", abtDynamicControlRc=\"");
        s.append(getAbtDynamicControlRc());
        s.append("\"");
        s.append(", abtNumberOfStdParms=\"");
        s.append(getAbtNumberOfStdParms());
        s.append("\"");
        s.append(", abtNumberOfUserParms=\"");
        s.append(getAbtNumberOfUserParms());
        s.append("\"");
        s.append(", abtNumberOfDaParms=\"");
        s.append(getAbtNumberOfDaParms());
        s.append("\"");
        s.append(", abtPgmGenType=\"");
        s.append(getAbtPgmGenType());
        s.append("\"");
        s.append(", abtPgmGenRelLevel=\"");
        s.append(getAbtPgmGenRelLevel());
        s.append("\"");
        s.append(", abtPgmName=\"");
        s.append(getAbtPgmName());
        s.append("\"");
        s.append(", abtPgmTranCode=\"");
        s.append(getAbtPgmTranCode());
        s.append("\"");
        s.append(", abtPgmMapName=\"");
        s.append(getAbtPgmMapName());
        s.append("\"");
        s.append(", abtNextProgramNameHdr=\"");
        s.append(getAbtNextProgramNameHdr());
        s.append("\"");
        s.append(", abtNextProgramNameId=\"");
        s.append(getAbtNextProgramNameId());
        s.append("\"");
        s.append(", filler30=\"");
        s.append(new String(filler30, encoding));
        s.append("\"");
        s.append(", abtErrorMessage=\"");
        s.append(getAbtErrorMessage());
        s.append("\"");
        s.append(", filler31=\"");
        s.append(new String(filler31, encoding));
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(AbtControlInfo that) {
        return this.abtControlIndicator == that.abtControlIndicator &&
            this.abtInProgress == that.abtInProgress &&
            this.abtDynamicControlPgm.equals(that.abtDynamicControlPgm) &&
            this.abtDynamicControlRc == that.abtDynamicControlRc &&
            this.abtNumberOfStdParms == that.abtNumberOfStdParms &&
            this.abtNumberOfUserParms == that.abtNumberOfUserParms &&
            this.abtNumberOfDaParms == that.abtNumberOfDaParms &&
            this.abtPgmGenType.equals(that.abtPgmGenType) &&
            this.abtPgmGenRelLevel.equals(that.abtPgmGenRelLevel) &&
            this.abtPgmName.equals(that.abtPgmName) &&
            this.abtPgmTranCode.equals(that.abtPgmTranCode) &&
            this.abtPgmMapName.equals(that.abtPgmMapName) &&
            this.abtNextProgramNameHdr.equals(that.abtNextProgramNameHdr) &&
            this.abtNextProgramNameId.equals(that.abtNextProgramNameId) &&
            Arrays.equals(this.filler30, that.filler30) &&
            this.abtErrorMessage.equals(that.abtErrorMessage) &&
            Arrays.equals(this.filler31, that.filler31);
    }
    
    /** Per-field equality comparison; only returns equal if argument is the same class */
    @Override
    public boolean equals(Object that) {
        return (that instanceof AbtControlInfo other) && other.canEqual(this) && this.equals(other);
    }
    
    /** Does {@code that} have the same runtime type as {@code this}? */
    public boolean canEqual(Object that) {
        return that instanceof AbtControlInfo;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * result + Character.hashCode(abtControlIndicator);
        result = 31 * result + Character.hashCode(abtInProgress);
        result = 31 * result + Objects.hashCode(abtDynamicControlPgm);
        result = 31 * result + Integer.hashCode(abtDynamicControlRc);
        result = 31 * result + Integer.hashCode(abtNumberOfStdParms);
        result = 31 * result + Integer.hashCode(abtNumberOfUserParms);
        result = 31 * result + Integer.hashCode(abtNumberOfDaParms);
        result = 31 * result + Objects.hashCode(abtPgmGenType);
        result = 31 * result + Objects.hashCode(abtPgmGenRelLevel);
        result = 31 * result + Objects.hashCode(abtPgmName);
        result = 31 * result + Objects.hashCode(abtPgmTranCode);
        result = 31 * result + Objects.hashCode(abtPgmMapName);
        result = 31 * result + Objects.hashCode(abtNextProgramNameHdr);
        result = 31 * result + Objects.hashCode(abtNextProgramNameId);
        result = 31 * result + Arrays.hashCode(filler30);
        result = 31 * result + Objects.hashCode(abtErrorMessage);
        result = 31 * result + Arrays.hashCode(filler31);
        return result;
    }
    
    @Override
    public int compareTo(AbtControlInfo that) {
        int c = 0;
        c = Character.compare(this.abtControlIndicator, that.abtControlIndicator);
        if ( c != 0 ) return c;
        c = Character.compare(this.abtInProgress, that.abtInProgress);
        if ( c != 0 ) return c;
        c = this.abtDynamicControlPgm.compareTo(that.abtDynamicControlPgm);
        if ( c != 0 ) return c;
        c = Integer.compare(this.abtDynamicControlRc, that.abtDynamicControlRc);
        if ( c != 0 ) return c;
        c = Integer.compare(this.abtNumberOfStdParms, that.abtNumberOfStdParms);
        if ( c != 0 ) return c;
        c = Integer.compare(this.abtNumberOfUserParms, that.abtNumberOfUserParms);
        if ( c != 0 ) return c;
        c = Integer.compare(this.abtNumberOfDaParms, that.abtNumberOfDaParms);
        if ( c != 0 ) return c;
        c = this.abtPgmGenType.compareTo(that.abtPgmGenType);
        if ( c != 0 ) return c;
        c = this.abtPgmGenRelLevel.compareTo(that.abtPgmGenRelLevel);
        if ( c != 0 ) return c;
        c = this.abtPgmName.compareTo(that.abtPgmName);
        if ( c != 0 ) return c;
        c = this.abtPgmTranCode.compareTo(that.abtPgmTranCode);
        if ( c != 0 ) return c;
        c = this.abtPgmMapName.compareTo(that.abtPgmMapName);
        if ( c != 0 ) return c;
        c = this.abtNextProgramNameHdr.compareTo(that.abtNextProgramNameHdr);
        if ( c != 0 ) return c;
        c = this.abtNextProgramNameId.compareTo(that.abtNextProgramNameId);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler30, that.filler30);
        if ( c != 0 ) return c;
        c = this.abtErrorMessage.compareTo(that.abtErrorMessage);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler31, that.filler31);
        if ( c == 0 && !(that.canEqual(this) && this.canEqual(that)) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
    }
    
    private static final StringField ABT_CONTROL_INDICATOR = factory.getStringField(1);
    private static final StringField ABT_IN_PROGRESS = factory.getStringField(1);
    private static final StringField ABT_DYNAMIC_CONTROL_PGM = factory.getStringField(8);
    private static final BinaryAsIntField ABT_DYNAMIC_CONTROL_RC = factory.getBinaryAsIntField(4, true);
    private static final BinaryAsIntField ABT_NUMBER_OF_STD_PARMS = factory.getBinaryAsIntField(2, true);
    private static final BinaryAsIntField ABT_NUMBER_OF_USER_PARMS = factory.getBinaryAsIntField(2, true);
    private static final BinaryAsIntField ABT_NUMBER_OF_DA_PARMS = factory.getBinaryAsIntField(2, true);
    private static final StringField ABT_PGM_GEN_TYPE = factory.getStringField(4);
    private static final StringField ABT_PGM_GEN_REL_LEVEL = factory.getStringField(4);
    private static final StringField ABT_PGM_NAME = factory.getStringField(8);
    private static final StringField ABT_PGM_TRAN_CODE = factory.getStringField(8);
    private static final StringField ABT_PGM_MAP_NAME = factory.getStringField(8);
    private static final StringField ABT_NEXT_PROGRAM_NAME_HDR = factory.getStringField(4);
    private static final StringField ABT_NEXT_PROGRAM_NAME_ID = factory.getStringField(4);
    private static final ByteArrayField FILLER_30 = factory.getByteArrayField(2);
    private byte[] filler30 = new byte[2];
    private static final StringField ABT_ERROR_MESSAGE = factory.getStringField(80);
    private static final ByteArrayField FILLER_31 = factory.getByteArrayField(8);
    private byte[] filler31 = new byte[8];
    private void initFiller() {
        new StringField(0, 2).putString("\0\0", filler30);
        new StringField(0, 8).putString("\0".repeat(8), filler31);
    }
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code AbtControlInfo} object
     * @param bytes  preallocated byte array to store the object serialization
     * @param offset offset in the byte array where serialization should begin
     * @return the byte array, updated with the newly added data formatted as in the {@code ABT-CONTROL-INFO} record
     * @see "ABT-CONTROL-INFO record at MXWW03.CPY:19"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        ABT_CONTROL_INDICATOR.putString(Character.toString(abtControlIndicator), bytes, offset);
        ABT_IN_PROGRESS.putString(Character.toString(abtInProgress), bytes, offset);
        ABT_DYNAMIC_CONTROL_PGM.putString(abtDynamicControlPgm, bytes, offset);
        ABT_DYNAMIC_CONTROL_RC.putInt(abtDynamicControlRc, bytes, offset);
        ABT_NUMBER_OF_STD_PARMS.putInt(abtNumberOfStdParms, bytes, offset);
        ABT_NUMBER_OF_USER_PARMS.putInt(abtNumberOfUserParms, bytes, offset);
        ABT_NUMBER_OF_DA_PARMS.putInt(abtNumberOfDaParms, bytes, offset);
        ABT_PGM_GEN_TYPE.putString(abtPgmGenType, bytes, offset);
        ABT_PGM_GEN_REL_LEVEL.putString(abtPgmGenRelLevel, bytes, offset);
        ABT_PGM_NAME.putString(abtPgmName, bytes, offset);
        ABT_PGM_TRAN_CODE.putString(abtPgmTranCode, bytes, offset);
        ABT_PGM_MAP_NAME.putString(abtPgmMapName, bytes, offset);
        ABT_NEXT_PROGRAM_NAME_HDR.putString(abtNextProgramNameHdr, bytes, offset);
        ABT_NEXT_PROGRAM_NAME_ID.putString(abtNextProgramNameId, bytes, offset);
        FILLER_30.putByteArray(filler30, bytes, offset);
        ABT_ERROR_MESSAGE.putString(abtErrorMessage, bytes, offset);
        FILLER_31.putByteArray(filler31, bytes, offset);
        return bytes;
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code AbtControlInfo} object,
     * starting at the beginning of the array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes(byte[] bytes) {
        return getBytes(bytes, 0);
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code AbtControlInfo} object,
     * allocating a new array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes() {
        return getBytes(new byte[numBytes()]);
    }
    
    /**
     * Retrieves a COBOL-format string representation of the {@code AbtControlInfo} object
     * @return the result of {@link #getBytes()} interpreted using the IBM-1047 EBCDIC encoding
     */
    public final String toByteString() {
        return new String(getBytes(), encoding);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array
     * @param bytes  byte array formatted as in the {@code ABT-CONTROL-INFO} record; will be space-padded to length if necessary
     * @param offset offset in the byte array where deserialization should begin
     * @see "ABT-CONTROL-INFO record at MXWW03.CPY:19"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        abtControlIndicator = ABT_CONTROL_INDICATOR.getString(bytes, offset).charAt(0);
        abtInProgress = ABT_IN_PROGRESS.getString(bytes, offset).charAt(0);
        abtDynamicControlPgm = ABT_DYNAMIC_CONTROL_PGM.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        abtDynamicControlRc = ABT_DYNAMIC_CONTROL_RC.getInt(bytes, offset);
        abtNumberOfStdParms = ABT_NUMBER_OF_STD_PARMS.getInt(bytes, offset);
        abtNumberOfUserParms = ABT_NUMBER_OF_USER_PARMS.getInt(bytes, offset);
        abtNumberOfDaParms = ABT_NUMBER_OF_DA_PARMS.getInt(bytes, offset);
        abtPgmGenType = ABT_PGM_GEN_TYPE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        abtPgmGenRelLevel = ABT_PGM_GEN_REL_LEVEL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        abtPgmName = ABT_PGM_NAME.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        abtPgmTranCode = ABT_PGM_TRAN_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        abtPgmMapName = ABT_PGM_MAP_NAME.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        abtNextProgramNameHdr = ABT_NEXT_PROGRAM_NAME_HDR.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        abtNextProgramNameId = ABT_NEXT_PROGRAM_NAME_ID.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_30.getByteArray(bytes, offset);
        abtErrorMessage = ABT_ERROR_MESSAGE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_31.getByteArray(bytes, offset);
    }
    
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array,
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(byte[] bytes) {
        setBytes(bytes, 0);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format string.
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(String bytes) {
        setBytes(bytes.getBytes(encoding));
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
