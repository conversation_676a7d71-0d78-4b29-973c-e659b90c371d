package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class Filler41 extends AbtDataAccessInfo {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private String abtBatchStatus = "";
    
    /** Initialize fields to non-null default values */
    public Filler41() {}
    
    /** Initialize all fields to provided values */
    public Filler41(String abtU100Sub, String abtDaAccessName, String abtDaGenericStatus, String abtBatchStatus) {
        super(abtU100Sub, abtDaAccessName, abtDaGenericStatus);
        this.abtBatchStatus = abtBatchStatus;
    }
    
    @Override
    public Filler41 clone() throws CloneNotSupportedException {
        Filler41 cloned = (Filler41) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code Filler41} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected Filler41(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code Filler41} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected Filler41(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code Filler41} object
     * @see #setBytes(byte[], int)
     */
    public static Filler41 fromBytes(byte[] bytes, int offset) {
        return new Filler41(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code Filler41} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static Filler41 fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code Filler41} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static Filler41 fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getAbtBatchStatus() {
        return this.abtBatchStatus;
    }
    
    public void setAbtBatchStatus(String abtBatchStatus) {
        this.abtBatchStatus = abtBatchStatus;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        super.reset();
        abtBatchStatus = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder(super.toString());
        s.append("{ abtBatchStatus=\"");
        s.append(getAbtBatchStatus());
        s.append("\"");
        s.append(", filler42=\"");
        s.append(new String(filler42, encoding));
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(Filler41 that) {
        return super.equals(that) &&
            this.abtBatchStatus.equals(that.abtBatchStatus) &&
            Arrays.equals(this.filler42, that.filler42);
    }
    
    @Override
    public boolean equals(Object that) {
        return (that instanceof Filler41 other) && other.canEqual(this) && this.equals(other);
    }
    
    @Override
    public boolean canEqual(Object that) {
        return that instanceof Filler41;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * super.hashCode();
        result = 31 * result + Objects.hashCode(abtBatchStatus);
        result = 31 * result + Arrays.hashCode(filler42);
        return result;
    }
    
    /** Per-field lexicographic comparison; only returns equal if argument is the same class */
    public int compareTo(Filler41 that) {
        int c = 0;
        c = super.compareTo(that);
        if ( c != 0 ) return c;
        c = this.abtBatchStatus.compareTo(that.abtBatchStatus);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler42, that.filler42);
        if ( c == 0 && !that.canEqual(this) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    @Override
    public int compareTo(AbtDataAccessInfo that) {
        if (that instanceof Filler41 other) {
            return this.compareTo(other);
        } else {
            return super.compareTo(that);
        }
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
        factory.incrementOffset(AbtDataAccessInfo.SIZE);
    }
    
    private static final StringField ABT_BATCH_STATUS = factory.getStringField(2);
    private static final ByteArrayField FILLER_42 = factory.getByteArrayField(4);
    private byte[] filler42 = new byte[4];
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * {@inheritDoc}.
     * Calls {@link AbtDataAccessInfo#getBytes(byte[], int)} to include any relevant information from the parent class.
     * @see "FILLER #41 record at MXWW03.CPY:104"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        super.getBytes(bytes, offset);
        ABT_BATCH_STATUS.putString(abtBatchStatus, bytes, offset);
        FILLER_42.putByteArray(filler42, bytes, offset);
        return bytes;
    }
    
    /**
     * {@inheritDoc}.
     * Calls {@link AbtDataAccessInfo#setBytes(byte[], int)} to set parent-class state.
     * @see "FILLER #41 record at MXWW03.CPY:104"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        super.setBytes(bytes, offset);
        abtBatchStatus = ABT_BATCH_STATUS.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_42.getByteArray(bytes, offset);
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
