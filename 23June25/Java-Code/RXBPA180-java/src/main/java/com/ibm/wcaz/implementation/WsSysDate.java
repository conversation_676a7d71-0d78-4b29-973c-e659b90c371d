package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class WsSysDate implements Cloneable, Comparable<WsSysDate> {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private String wsSysYyyy = "";
    private String wsSysMm = "";
    private String wsSysDd = "";
    
    /** Initialize fields to non-null default values */
    public WsSysDate() {}
    
    /** Initialize all fields to provided values */
    public WsSysDate(String wsSysYyyy, String wsSysMm, String wsSysDd) {
        this.wsSysYyyy = wsSysYyyy;
        this.wsSysMm = wsSysMm;
        this.wsSysDd = wsSysDd;
    }
    
    @Override
    public WsSysDate clone() throws CloneNotSupportedException {
        WsSysDate cloned = (WsSysDate) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code WsSysDate} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected WsSysDate(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code WsSysDate} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected WsSysDate(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code WsSysDate} object
     * @see #setBytes(byte[], int)
     */
    public static WsSysDate fromBytes(byte[] bytes, int offset) {
        return new WsSysDate(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code WsSysDate} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static WsSysDate fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code WsSysDate} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static WsSysDate fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getWsSysYyyy() {
        return this.wsSysYyyy;
    }
    
    public void setWsSysYyyy(String wsSysYyyy) {
        this.wsSysYyyy = wsSysYyyy;
    }
    
    public String getWsSysMm() {
        return this.wsSysMm;
    }
    
    public void setWsSysMm(String wsSysMm) {
        this.wsSysMm = wsSysMm;
    }
    
    public String getWsSysDd() {
        return this.wsSysDd;
    }
    
    public void setWsSysDd(String wsSysDd) {
        this.wsSysDd = wsSysDd;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        wsSysYyyy = "";
        wsSysMm = "";
        wsSysDd = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder();
        s.append("{ wsSysYyyy=\"");
        s.append(getWsSysYyyy());
        s.append("\"");
        s.append(", wsSysMm=\"");
        s.append(getWsSysMm());
        s.append("\"");
        s.append(", wsSysDd=\"");
        s.append(getWsSysDd());
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(WsSysDate that) {
        return this.wsSysYyyy.equals(that.wsSysYyyy) &&
            this.wsSysMm.equals(that.wsSysMm) &&
            this.wsSysDd.equals(that.wsSysDd);
    }
    
    /** Per-field equality comparison; only returns equal if argument is the same class */
    @Override
    public boolean equals(Object that) {
        return (that instanceof WsSysDate other) && other.canEqual(this) && this.equals(other);
    }
    
    /** Does {@code that} have the same runtime type as {@code this}? */
    public boolean canEqual(Object that) {
        return that instanceof WsSysDate;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * result + Objects.hashCode(wsSysYyyy);
        result = 31 * result + Objects.hashCode(wsSysMm);
        result = 31 * result + Objects.hashCode(wsSysDd);
        return result;
    }
    
    @Override
    public int compareTo(WsSysDate that) {
        int c = 0;
        c = this.wsSysYyyy.compareTo(that.wsSysYyyy);
        if ( c != 0 ) return c;
        c = this.wsSysMm.compareTo(that.wsSysMm);
        if ( c != 0 ) return c;
        c = this.wsSysDd.compareTo(that.wsSysDd);
        if ( c == 0 && !(that.canEqual(this) && this.canEqual(that)) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
    }
    
    private static final StringField WS_SYS_YYYY = factory.getStringField(4);
    private static final StringField WS_SYS_MM = factory.getStringField(2);
    private static final StringField WS_SYS_DD = factory.getStringField(2);
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code WsSysDate} object
     * @param bytes  preallocated byte array to store the object serialization
     * @param offset offset in the byte array where serialization should begin
     * @return the byte array, updated with the newly added data formatted as in the {@code WS-SYS-DATE} record
     * @see "WS-SYS-DATE record at RXBPA180.cbl:126"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        WS_SYS_YYYY.putString(wsSysYyyy, bytes, offset);
        WS_SYS_MM.putString(wsSysMm, bytes, offset);
        WS_SYS_DD.putString(wsSysDd, bytes, offset);
        return bytes;
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code WsSysDate} object,
     * starting at the beginning of the array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes(byte[] bytes) {
        return getBytes(bytes, 0);
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code WsSysDate} object,
     * allocating a new array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes() {
        return getBytes(new byte[numBytes()]);
    }
    
    /**
     * Retrieves a COBOL-format string representation of the {@code WsSysDate} object
     * @return the result of {@link #getBytes()} interpreted using the IBM-1047 EBCDIC encoding
     */
    public final String toByteString() {
        return new String(getBytes(), encoding);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array
     * @param bytes  byte array formatted as in the {@code WS-SYS-DATE} record; will be space-padded to length if necessary
     * @param offset offset in the byte array where deserialization should begin
     * @see "WS-SYS-DATE record at RXBPA180.cbl:126"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        wsSysYyyy = WS_SYS_YYYY.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        wsSysMm = WS_SYS_MM.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        wsSysDd = WS_SYS_DD.getString(bytes, offset).replaceFirst("\\s+\\z", "");
    }
    
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array,
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(byte[] bytes) {
        setBytes(bytes, 0);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format string.
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(String bytes) {
        setBytes(bytes.getBytes(encoding));
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
