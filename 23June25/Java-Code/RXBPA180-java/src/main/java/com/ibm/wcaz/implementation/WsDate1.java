package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.ExternalDecimalAsIntField;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;

public class WsDate1 implements Cloneable, Comparable<WsDate1> {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private int wsDate1Yyyy;
    private int wsDate1Mm;
    private int wsDate1Dd;
    
    /** Initialize fields to non-null default values */
    public WsDate1() {
        initFiller();
    }
    
    /** Initialize all fields to provided values */
    public WsDate1(int wsDate1Yyyy, int wsDate1Mm, int wsDate1Dd) {
        this.wsDate1Yyyy = wsDate1Yyyy;
        this.wsDate1Mm = wsDate1Mm;
        this.wsDate1Dd = wsDate1Dd;
        initFiller();
    }
    
    @Override
    public WsDate1 clone() throws CloneNotSupportedException {
        WsDate1 cloned = (WsDate1) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code WsDate1} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected WsDate1(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code WsDate1} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected WsDate1(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code WsDate1} object
     * @see #setBytes(byte[], int)
     */
    public static WsDate1 fromBytes(byte[] bytes, int offset) {
        return new WsDate1(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code WsDate1} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static WsDate1 fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code WsDate1} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static WsDate1 fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public int getWsDate1Yyyy() {
        return this.wsDate1Yyyy;
    }
    
    public void setWsDate1Yyyy(int wsDate1Yyyy) {
        this.wsDate1Yyyy = wsDate1Yyyy;
    }
    
    public int getWsDate1Mm() {
        return this.wsDate1Mm;
    }
    
    public void setWsDate1Mm(int wsDate1Mm) {
        this.wsDate1Mm = wsDate1Mm;
    }
    
    public int getWsDate1Dd() {
        return this.wsDate1Dd;
    }
    
    public void setWsDate1Dd(int wsDate1Dd) {
        this.wsDate1Dd = wsDate1Dd;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        wsDate1Yyyy = 0;
        wsDate1Mm = 0;
        wsDate1Dd = 0;
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder();
        s.append("{ wsDate1Yyyy=\"");
        s.append(getWsDate1Yyyy());
        s.append("\"");
        s.append(", filler26=\"");
        s.append(new String(filler26, encoding));
        s.append("\"");
        s.append(", wsDate1Mm=\"");
        s.append(getWsDate1Mm());
        s.append("\"");
        s.append(", filler27=\"");
        s.append(new String(filler27, encoding));
        s.append("\"");
        s.append(", wsDate1Dd=\"");
        s.append(getWsDate1Dd());
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(WsDate1 that) {
        return this.wsDate1Yyyy == that.wsDate1Yyyy &&
            Arrays.equals(this.filler26, that.filler26) &&
            this.wsDate1Mm == that.wsDate1Mm &&
            Arrays.equals(this.filler27, that.filler27) &&
            this.wsDate1Dd == that.wsDate1Dd;
    }
    
    /** Per-field equality comparison; only returns equal if argument is the same class */
    @Override
    public boolean equals(Object that) {
        return (that instanceof WsDate1 other) && other.canEqual(this) && this.equals(other);
    }
    
    /** Does {@code that} have the same runtime type as {@code this}? */
    public boolean canEqual(Object that) {
        return that instanceof WsDate1;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * result + Integer.hashCode(wsDate1Yyyy);
        result = 31 * result + Arrays.hashCode(filler26);
        result = 31 * result + Integer.hashCode(wsDate1Mm);
        result = 31 * result + Arrays.hashCode(filler27);
        result = 31 * result + Integer.hashCode(wsDate1Dd);
        return result;
    }
    
    @Override
    public int compareTo(WsDate1 that) {
        int c = 0;
        c = Integer.compare(this.wsDate1Yyyy, that.wsDate1Yyyy);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler26, that.filler26);
        if ( c != 0 ) return c;
        c = Integer.compare(this.wsDate1Mm, that.wsDate1Mm);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler27, that.filler27);
        if ( c != 0 ) return c;
        c = Integer.compare(this.wsDate1Dd, that.wsDate1Dd);
        if ( c == 0 && !(that.canEqual(this) && this.canEqual(that)) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
    }
    
    private static final ExternalDecimalAsIntField WS_DATE_1_YYYY = factory.getExternalDecimalAsIntField(4, true);
    private static final ByteArrayField FILLER_26 = factory.getByteArrayField(1);
    private byte[] filler26 = new byte[1];
    private static final ExternalDecimalAsIntField WS_DATE_1_MM = factory.getExternalDecimalAsIntField(2, true);
    private static final ByteArrayField FILLER_27 = factory.getByteArrayField(1);
    private byte[] filler27 = new byte[1];
    private static final ExternalDecimalAsIntField WS_DATE_1_DD = factory.getExternalDecimalAsIntField(2, true);
    private void initFiller() {
        new StringField(0, 1).putString(Character.toString('-'), filler26);
        new StringField(0, 1).putString(Character.toString('-'), filler27);
    }
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code WsDate1} object
     * @param bytes  preallocated byte array to store the object serialization
     * @param offset offset in the byte array where serialization should begin
     * @return the byte array, updated with the newly added data formatted as in the {@code WS-DATE-1} record
     * @see "WS-DATE-1 record at RXBPA180.cbl:120"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        WS_DATE_1_YYYY.putInt(wsDate1Yyyy, bytes, offset);
        FILLER_26.putByteArray(filler26, bytes, offset);
        WS_DATE_1_MM.putInt(wsDate1Mm, bytes, offset);
        FILLER_27.putByteArray(filler27, bytes, offset);
        WS_DATE_1_DD.putInt(wsDate1Dd, bytes, offset);
        return bytes;
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code WsDate1} object,
     * starting at the beginning of the array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes(byte[] bytes) {
        return getBytes(bytes, 0);
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code WsDate1} object,
     * allocating a new array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes() {
        return getBytes(new byte[numBytes()]);
    }
    
    /**
     * Retrieves a COBOL-format string representation of the {@code WsDate1} object
     * @return the result of {@link #getBytes()} interpreted using the IBM-1047 EBCDIC encoding
     */
    public final String toByteString() {
        return new String(getBytes(), encoding);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array
     * @param bytes  byte array formatted as in the {@code WS-DATE-1} record; will be space-padded to length if necessary
     * @param offset offset in the byte array where deserialization should begin
     * @see "WS-DATE-1 record at RXBPA180.cbl:120"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        wsDate1Yyyy = WS_DATE_1_YYYY.getInt(bytes, offset);
        FILLER_26.getByteArray(bytes, offset);
        wsDate1Mm = WS_DATE_1_MM.getInt(bytes, offset);
        FILLER_27.getByteArray(bytes, offset);
        wsDate1Dd = WS_DATE_1_DD.getInt(bytes, offset);
    }
    
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array,
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(byte[] bytes) {
        setBytes(bytes, 0);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format string.
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(String bytes) {
        setBytes(bytes.getBytes(encoding));
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
