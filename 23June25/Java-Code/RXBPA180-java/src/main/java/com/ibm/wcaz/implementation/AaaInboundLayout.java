package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class AaaInboundLayout implements Cloneable, Comparable<AaaInboundLayout> {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private String aaaInCreateDate = "";
    private String aaaInRegType = "";
    private String aaaInSerialNo = "";
    private String aaaInRegCompDate = "";
    
    /** Initialize fields to non-null default values */
    public AaaInboundLayout() {
        initFiller();
    }
    
    /** Initialize all fields to provided values */
    public AaaInboundLayout(String aaaInCreateDate, String aaaInRegType, String aaaInSerialNo, String aaaInRegCompDate) {
        this.aaaInCreateDate = aaaInCreateDate;
        this.aaaInRegType = aaaInRegType;
        this.aaaInSerialNo = aaaInSerialNo;
        this.aaaInRegCompDate = aaaInRegCompDate;
        initFiller();
    }
    
    @Override
    public AaaInboundLayout clone() throws CloneNotSupportedException {
        AaaInboundLayout cloned = (AaaInboundLayout) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code AaaInboundLayout} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected AaaInboundLayout(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code AaaInboundLayout} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected AaaInboundLayout(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code AaaInboundLayout} object
     * @see #setBytes(byte[], int)
     */
    public static AaaInboundLayout fromBytes(byte[] bytes, int offset) {
        return new AaaInboundLayout(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code AaaInboundLayout} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static AaaInboundLayout fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code AaaInboundLayout} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static AaaInboundLayout fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getAaaInCreateDate() {
        return this.aaaInCreateDate;
    }
    
    public void setAaaInCreateDate(String aaaInCreateDate) {
        this.aaaInCreateDate = aaaInCreateDate;
    }
    
    public String getAaaInRegType() {
        return this.aaaInRegType;
    }
    
    public void setAaaInRegType(String aaaInRegType) {
        this.aaaInRegType = aaaInRegType;
    }
    
    public String getAaaInSerialNo() {
        return this.aaaInSerialNo;
    }
    
    public void setAaaInSerialNo(String aaaInSerialNo) {
        this.aaaInSerialNo = aaaInSerialNo;
    }
    
    public String getAaaInRegCompDate() {
        return this.aaaInRegCompDate;
    }
    
    public void setAaaInRegCompDate(String aaaInRegCompDate) {
        this.aaaInRegCompDate = aaaInRegCompDate;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        aaaInCreateDate = "";
        aaaInRegType = "";
        aaaInSerialNo = "";
        aaaInRegCompDate = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder();
        s.append("{ aaaInCreateDate=\"");
        s.append(getAaaInCreateDate());
        s.append("\"");
        s.append(", filler20=\"");
        s.append(new String(filler20, encoding));
        s.append("\"");
        s.append(", aaaInRegType=\"");
        s.append(getAaaInRegType());
        s.append("\"");
        s.append(", filler21=\"");
        s.append(new String(filler21, encoding));
        s.append("\"");
        s.append(", aaaInSerialNo=\"");
        s.append(getAaaInSerialNo());
        s.append("\"");
        s.append(", aaaInRegCompDate=\"");
        s.append(getAaaInRegCompDate());
        s.append("\"");
        s.append(", filler22=\"");
        s.append(new String(filler22, encoding));
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(AaaInboundLayout that) {
        return this.aaaInCreateDate.equals(that.aaaInCreateDate) &&
            Arrays.equals(this.filler20, that.filler20) &&
            this.aaaInRegType.equals(that.aaaInRegType) &&
            Arrays.equals(this.filler21, that.filler21) &&
            this.aaaInSerialNo.equals(that.aaaInSerialNo) &&
            this.aaaInRegCompDate.equals(that.aaaInRegCompDate) &&
            Arrays.equals(this.filler22, that.filler22);
    }
    
    /** Per-field equality comparison; only returns equal if argument is the same class */
    @Override
    public boolean equals(Object that) {
        return (that instanceof AaaInboundLayout other) && other.canEqual(this) && this.equals(other);
    }
    
    /** Does {@code that} have the same runtime type as {@code this}? */
    public boolean canEqual(Object that) {
        return that instanceof AaaInboundLayout;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * result + Objects.hashCode(aaaInCreateDate);
        result = 31 * result + Arrays.hashCode(filler20);
        result = 31 * result + Objects.hashCode(aaaInRegType);
        result = 31 * result + Arrays.hashCode(filler21);
        result = 31 * result + Objects.hashCode(aaaInSerialNo);
        result = 31 * result + Objects.hashCode(aaaInRegCompDate);
        result = 31 * result + Arrays.hashCode(filler22);
        return result;
    }
    
    @Override
    public int compareTo(AaaInboundLayout that) {
        int c = 0;
        c = this.aaaInCreateDate.compareTo(that.aaaInCreateDate);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler20, that.filler20);
        if ( c != 0 ) return c;
        c = this.aaaInRegType.compareTo(that.aaaInRegType);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler21, that.filler21);
        if ( c != 0 ) return c;
        c = this.aaaInSerialNo.compareTo(that.aaaInSerialNo);
        if ( c != 0 ) return c;
        c = this.aaaInRegCompDate.compareTo(that.aaaInRegCompDate);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler22, that.filler22);
        if ( c == 0 && !(that.canEqual(this) && this.canEqual(that)) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
    }
    
    private static final StringField AAA_IN_CREATE_DATE = factory.getStringField(27);
    private static final ByteArrayField FILLER_20 = factory.getByteArrayField(15);
    private byte[] filler20 = new byte[15];
    private static final StringField AAA_IN_REG_TYPE = factory.getStringField(4);
    private static final ByteArrayField FILLER_21 = factory.getByteArrayField(12);
    private byte[] filler21 = new byte[12];
    private static final StringField AAA_IN_SERIAL_NO = factory.getStringField(17);
    private static final StringField AAA_IN_REG_COMP_DATE = factory.getStringField(8);
    private static final ByteArrayField FILLER_22 = factory.getByteArrayField(117);
    private byte[] filler22 = new byte[117];
    private void initFiller() {
        new StringField(0, 15).putString("", filler20);
        new StringField(0, 12).putString("", filler21);
        new StringField(0, 117).putString("", filler22);
    }
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code AaaInboundLayout} object
     * @param bytes  preallocated byte array to store the object serialization
     * @param offset offset in the byte array where serialization should begin
     * @return the byte array, updated with the newly added data formatted as in the {@code AAA-INBOUND-LAYOUT} record
     * @see "AAA-INBOUND-LAYOUT record at RXBPA180.cbl:47"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        AAA_IN_CREATE_DATE.putString(aaaInCreateDate, bytes, offset);
        FILLER_20.putByteArray(filler20, bytes, offset);
        AAA_IN_REG_TYPE.putString(aaaInRegType, bytes, offset);
        FILLER_21.putByteArray(filler21, bytes, offset);
        AAA_IN_SERIAL_NO.putString(aaaInSerialNo, bytes, offset);
        AAA_IN_REG_COMP_DATE.putString(aaaInRegCompDate, bytes, offset);
        FILLER_22.putByteArray(filler22, bytes, offset);
        return bytes;
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code AaaInboundLayout} object,
     * starting at the beginning of the array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes(byte[] bytes) {
        return getBytes(bytes, 0);
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code AaaInboundLayout} object,
     * allocating a new array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes() {
        return getBytes(new byte[numBytes()]);
    }
    
    /**
     * Retrieves a COBOL-format string representation of the {@code AaaInboundLayout} object
     * @return the result of {@link #getBytes()} interpreted using the IBM-1047 EBCDIC encoding
     */
    public final String toByteString() {
        return new String(getBytes(), encoding);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array
     * @param bytes  byte array formatted as in the {@code AAA-INBOUND-LAYOUT} record; will be space-padded to length if necessary
     * @param offset offset in the byte array where deserialization should begin
     * @see "AAA-INBOUND-LAYOUT record at RXBPA180.cbl:47"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        aaaInCreateDate = AAA_IN_CREATE_DATE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_20.getByteArray(bytes, offset);
        aaaInRegType = AAA_IN_REG_TYPE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_21.getByteArray(bytes, offset);
        aaaInSerialNo = AAA_IN_SERIAL_NO.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        aaaInRegCompDate = AAA_IN_REG_COMP_DATE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_22.getByteArray(bytes, offset);
    }
    
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array,
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(byte[] bytes) {
        setBytes(bytes, 0);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format string.
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(String bytes) {
        setBytes(bytes.getBytes(encoding));
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
