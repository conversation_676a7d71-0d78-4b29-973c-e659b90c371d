package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class WsSystemTime implements Cloneable, Comparable<WsSystemTime> {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private String wsSysHour = "";
    private String wsSysMin = "";
    private String wsSysSec = "";
    
    /** Initialize fields to non-null default values */
    public WsSystemTime() {}
    
    /** Initialize all fields to provided values */
    public WsSystemTime(String wsSysHour, String wsSysMin, String wsSysSec) {
        this.wsSysHour = wsSysHour;
        this.wsSysMin = wsSysMin;
        this.wsSysSec = wsSysSec;
    }
    
    @Override
    public WsSystemTime clone() throws CloneNotSupportedException {
        WsSystemTime cloned = (WsSystemTime) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code WsSystemTime} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected WsSystemTime(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code WsSystemTime} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected WsSystemTime(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code WsSystemTime} object
     * @see #setBytes(byte[], int)
     */
    public static WsSystemTime fromBytes(byte[] bytes, int offset) {
        return new WsSystemTime(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code WsSystemTime} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static WsSystemTime fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code WsSystemTime} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static WsSystemTime fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getWsSysHour() {
        return this.wsSysHour;
    }
    
    public void setWsSysHour(String wsSysHour) {
        this.wsSysHour = wsSysHour;
    }
    
    public String getWsSysMin() {
        return this.wsSysMin;
    }
    
    public void setWsSysMin(String wsSysMin) {
        this.wsSysMin = wsSysMin;
    }
    
    public String getWsSysSec() {
        return this.wsSysSec;
    }
    
    public void setWsSysSec(String wsSysSec) {
        this.wsSysSec = wsSysSec;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        wsSysHour = "";
        wsSysMin = "";
        wsSysSec = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder();
        s.append("{ wsSysHour=\"");
        s.append(getWsSysHour());
        s.append("\"");
        s.append(", wsSysMin=\"");
        s.append(getWsSysMin());
        s.append("\"");
        s.append(", wsSysSec=\"");
        s.append(getWsSysSec());
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(WsSystemTime that) {
        return this.wsSysHour.equals(that.wsSysHour) &&
            this.wsSysMin.equals(that.wsSysMin) &&
            this.wsSysSec.equals(that.wsSysSec);
    }
    
    /** Per-field equality comparison; only returns equal if argument is the same class */
    @Override
    public boolean equals(Object that) {
        return (that instanceof WsSystemTime other) && other.canEqual(this) && this.equals(other);
    }
    
    /** Does {@code that} have the same runtime type as {@code this}? */
    public boolean canEqual(Object that) {
        return that instanceof WsSystemTime;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * result + Objects.hashCode(wsSysHour);
        result = 31 * result + Objects.hashCode(wsSysMin);
        result = 31 * result + Objects.hashCode(wsSysSec);
        return result;
    }
    
    @Override
    public int compareTo(WsSystemTime that) {
        int c = 0;
        c = this.wsSysHour.compareTo(that.wsSysHour);
        if ( c != 0 ) return c;
        c = this.wsSysMin.compareTo(that.wsSysMin);
        if ( c != 0 ) return c;
        c = this.wsSysSec.compareTo(that.wsSysSec);
        if ( c == 0 && !(that.canEqual(this) && this.canEqual(that)) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
    }
    
    private static final StringField WS_SYS_HOUR = factory.getStringField(2);
    private static final StringField WS_SYS_MIN = factory.getStringField(2);
    private static final StringField WS_SYS_SEC = factory.getStringField(4);
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code WsSystemTime} object
     * @param bytes  preallocated byte array to store the object serialization
     * @param offset offset in the byte array where serialization should begin
     * @return the byte array, updated with the newly added data formatted as in the {@code WS-SYSTEM-TIME} record
     * @see "WS-SYSTEM-TIME record at RXBPA180.cbl:136"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        WS_SYS_HOUR.putString(wsSysHour, bytes, offset);
        WS_SYS_MIN.putString(wsSysMin, bytes, offset);
        WS_SYS_SEC.putString(wsSysSec, bytes, offset);
        return bytes;
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code WsSystemTime} object,
     * starting at the beginning of the array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes(byte[] bytes) {
        return getBytes(bytes, 0);
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code WsSystemTime} object,
     * allocating a new array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes() {
        return getBytes(new byte[numBytes()]);
    }
    
    /**
     * Retrieves a COBOL-format string representation of the {@code WsSystemTime} object
     * @return the result of {@link #getBytes()} interpreted using the IBM-1047 EBCDIC encoding
     */
    public final String toByteString() {
        return new String(getBytes(), encoding);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array
     * @param bytes  byte array formatted as in the {@code WS-SYSTEM-TIME} record; will be space-padded to length if necessary
     * @param offset offset in the byte array where deserialization should begin
     * @see "WS-SYSTEM-TIME record at RXBPA180.cbl:136"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        wsSysHour = WS_SYS_HOUR.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        wsSysMin = WS_SYS_MIN.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        wsSysSec = WS_SYS_SEC.getString(bytes, offset).replaceFirst("\\s+\\z", "");
    }
    
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array,
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(byte[] bytes) {
        setBytes(bytes, 0);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format string.
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(String bytes) {
        setBytes(bytes.getBytes(encoding));
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
