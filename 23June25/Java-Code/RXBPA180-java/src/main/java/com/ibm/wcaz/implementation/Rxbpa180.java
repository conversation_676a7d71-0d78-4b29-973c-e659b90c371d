package com.ibm.wcaz.implementation;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.SQLWarning;
import java.text.ParseException;
import java.text.SimpleDateFormat;

import org.h2.jdbc.JdbcConnection;

import com.ibm.jzos.ZFile;
import com.ibm.jzos.ZFileException;

public class Rxbpa180 {
    private ZFile aaaFormatInFile;
    private ZFile aaaFormatOutFile;
    private static Rxbpa180 rxbpa180 = new Rxbpa180();
    private String returnCode = "00000";

    /** Mode to open a file in */
    public static enum OpenMode {
        /** read-only */
        READ("r"),
        /** write-only, over-writing */
        WRITE("w"),
        /** write-only, appending */
        APPEND("a"),
        /** read-write */
        READ_WRITE("r+");

        private String str;

        OpenMode(String str) {
            this.str = str;
        }

        /** fopen() mode-string corresponding to this mode */
        public String modeStr() {
            return str;
        }
    }

    public void openAaaFormatInFile(OpenMode mode) throws ZFileException {
        aaaFormatInFile = new ZFile("//DD:MXPA180I", mode.modeStr() + "b,type=record");
    }

    public void closeAaaFormatInFile() throws ZFileException {
        aaaFormatInFile.close();
    }

    public void openAaaFormatOutFile(OpenMode mode) throws ZFileException {
        aaaFormatOutFile = new ZFile("//DD:MXPA180O", mode.modeStr() + "b,type=record");
    }

    public void closeAaaFormatOutFile() throws ZFileException {
        aaaFormatOutFile.close();
    }

    /** Initialize fields to non-null default values */
    public Rxbpa180() {
    }

    public ZFile getAaaFormatInFile() {
        return this.aaaFormatInFile;
    }

    public ZFile getAaaFormatOutFile() {
        return this.aaaFormatOutFile;
    }

    public String getReturnCode() {
        return this.returnCode;
    }

    public void setReturnCode(String returnCode) {
        this.returnCode = returnCode;
    }

    public static Rxbpa180 getInstance() {
        return rxbpa180;
    }

    public void initialization1000(AaaInboundLayout aaaInboundLayout, AbnormalTerminationArea abnormalTerminationArea,
            WsCountFields wsCountFields, WsFlags wsFlags) {
        String wsPgmName = "MXBPA180";
        System.out.println("********  MXBPA180 BEGINS  ********");
        System.out.println(" ");
        wsCountFields.setAbtPgmName(wsPgmName);
        openInoutFile1200(abnormalTerminationArea);
        readInputFile1300(abnormalTerminationArea, wsCountFields, wsFlags);
    }

    public void processTrailRec2400(WsCountFields wsCountFields, WsDate1 wsDate1, WsTime wsTime, ZRegTrlRec zRegTrlRec) {
        AaaFormatOutRecord aaaFormatOutRecord = new AaaFormatOutRecord();
        zRegTrlRec.setZRecordType("1");
        // zRegTrlRec.setZCreateTime(wsTime.getWsTime()); // edited by Ruman
        // zRegTrlRec.setZCreateDate(wsDate1.getWsDate1()); // edited by Ruman
        zRegTrlRec.setZCreateTime(wsTime.getWsHour() + wsTime.getWsTmSep() + wsTime.getWsMin());
        zRegTrlRec.setZCreateDate(String.format("%04d-%02d-%02d", wsDate1.getWsDate1Yyyy(), wsDate1.getWsDate1Mm(), wsDate1.getWsDate1Dd()));
        // aaaFormatOutRecord.setBytes(zRegTrlRec.getBytes()); // edited by Ruman
        aaaFormatOutRecord.setFromZRegTrlRec(zRegTrlRec);

        // aaaFormatOutRecord.writeTo(Rxbpa180.getInstance().getAaaFormatOutFile()); // edited by Ruman
        try {
            aaaFormatOutRecord.writeTo(Rxbpa180.getInstance().getAaaFormatOutFile());
        } catch (ZFileException e) {
            throw new RuntimeException("Failed to write to AAA format out file", e);
        }
    }

    public void processCpuDlr2150(AaaInboundLayout aaaInboundLayout, AbnormalTerminationArea abnormalTerminationArea,
            Dclvwmcucp dclvwmcucp, Dclvwmtrli dclvwmtrli, WsCountFields wsCountFields, WsFlags wsFlags) {
        String abtDaFunction = "";
        DRegDtlRec dRegDtlRec = new DRegDtlRec();
        dclvwmcucp.setCpuId(dRegDtlRec.getDCdfCustId());
        dclvwmcucp.setCpuCode(dRegDtlRec.getDCdfCustCode());
        // dclvwmcucp.setCpuDealerNo(dclvwmtrli.getDlrNo()); // edited by Ruman
        dclvwmcucp.setCpuDealerNo(String.valueOf(dclvwmtrli.getDlrNo()));
        try {
            String sql = "SELECT CPU_ID, CPU_CODE, CPU_DEALER_NO FROM VWMCUCP WHERE CPU_ID = ? AND CPU_CODE = ? AND CUST_NO = ?";
            // PreparedStatement ps = JdbcConnection.connection.prepareStatement(sql);
            // edited by Ruman
            Connection connection = DriverManager.getConnection("jdbc:example_database_url", "example_username", "example_password");
            PreparedStatement ps = connection.prepareStatement(sql);
            ps.setString(1, dRegDtlRec.getDCdfCustId());

            ps.setString(2, dRegDtlRec.getDCdfCustCode());

            ps.setInt(3, dclvwmtrli.getDlrNo());

            ResultSet rs = ps.executeQuery();
            if (rs.next()) {
                dclvwmcucp.setCpuId(rs.getString(1));
                dclvwmcucp.setCpuCode(rs.getString(2));
                dclvwmcucp.setCpuDealerNo(rs.getString(3));
            } else {
                wsFlags.setDlrXrefNotFound();
                System.out.println("DLR NOT FOUND IN CPU XREF TABLE");
                System.out.println("SERIAL NO IS:" + aaaInboundLayout.getAaaInSerialNo());
                System.out.println("DLR FROM TRUST LINE IS:" + dclvwmtrli.getDlrNo());
            }
            ps.close();
        } catch (SQLException exception) {
            abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
            // abnormalTerminationArea.getAbtPgmErrorData().setAbtDb2Status(exception.getErrorCode()); // edited by Ruman
            abnormalTerminationArea.getAbtPgmErrorData().setAbtErrorAbendCode(exception.getErrorCode());
            abtDaFunction = "SELECT";
            abnormalTerminationArea.getAbtPgmErrorData().setAbtErrorActivity("DB2");
            abnormalTerminationArea.getAbtDataAccessInfo().setAbtDaAccessName("VWMCUCP");
            abnormalTerminationArea.getAbtPgmErrorData().setAbtErrorSection("2150-");
            System.out.println("SERIAL NO IS:" + aaaInboundLayout.getAaaInSerialNo());
            System.out.println("DLR FROM TRUST LINE IS:" + dclvwmtrli.getDlrNo());
            z980AbnormalTerm(abnormalTerminationArea);
        }
    }

    public void mainlineProcessing0000() {
        AaaInboundLayout aaaInboundLayout = new AaaInboundLayout();
        AbnormalTerminationArea abnormalTerminationArea = new AbnormalTerminationArea();
        WsCountFields wsCountFields = new WsCountFields();
        WsDate1 wsDate1 = new WsDate1();
        WsFlags wsFlags = new WsFlags();
        WsTime wsTime = new WsTime();
        ZRegTrlRec zRegTrlRec = new ZRegTrlRec();
        initialization1000(aaaInboundLayout, abnormalTerminationArea, wsCountFields, wsFlags);
        while (!wsFlags.isWsEndOfFile()) {
            processAaaFile2000(aaaInboundLayout, abnormalTerminationArea, wsCountFields, wsDate1, wsFlags, wsTime,
                    zRegTrlRec);
        }
        if (wsCountFields.getWsTotHdrRecCnt() >= 1 && wsFlags.isWsEndOfFile()) {
            processTrailRec2400(wsCountFields, wsDate1, wsTime, zRegTrlRec);
        }
        termination9000(wsCountFields);
    }

    public void openInoutFile1200(AbnormalTerminationArea abnormalTerminationArea) {
        String abtDaFunction = "";
        String wsAaaInfileStatus = "";
        String wsAaaOutfileStatus = "";
        String wsGoodStatus = "20";
        try {
            // AaaFormatInFile.open(AaaFormatInFile.OpenMode.READ);
            this.openAaaFormatInFile(OpenMode.READ);
            if (wsAaaInfileStatus.compareTo(wsGoodStatus) > 0) {
                abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
                // abnormalTerminationArea.getAbtPgmErrorData().setAbtBatchStatus(wsAaaInfileStatus); // edited by Ruman
                abtDaFunction = "OPEN";
                abnormalTerminationArea.getAbtDataAccessInfo().setAbtDaAccessName("MXPA180I");
                abnormalTerminationArea.getAbtPgmErrorData().setAbtErrorActivity("SEQ");
                abnormalTerminationArea.getAbtPgmErrorData().setAbtErrorSection("1200-");
                z980AbnormalTerm(abnormalTerminationArea);
            }
        } catch (ZFileException e) {
            abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
            abnormalTerminationArea.getAbtPgmErrorData().setAbtBatchStatus(e.getErrnoMsg());
            abtDaFunction = "OPEN";
            abnormalTerminationArea.getAbtDataAccessInfo().setAbtDaAccessName("MXPA180I");
            abnormalTerminationArea.getAbtPgmErrorData().setAbtErrorActivity("SEQ");
            abnormalTerminationArea.getAbtPgmErrorData().setAbtErrorSection("1200-");
            z980AbnormalTerm(abnormalTerminationArea);
        }
        try {
            // AaaFormatOutFile.open(AaaFormatOutFile.OpenMode.WRITE);
            this.openAaaFormatOutFile(OpenMode.WRITE);
            if (wsAaaOutfileStatus.compareTo(wsGoodStatus) > 0) {
                abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
                abnormalTerminationArea.getAbtPgmErrorData().setAbtBatchStatus(wsAaaOutfileStatus);
                abtDaFunction = "OPEN";
                abnormalTerminationArea.getAbtDataAccessInfo().setAbtDaAccessName("MXPA180I");
                abnormalTerminationArea.getAbtPgmErrorData().setAbtErrorActivity("SEQ");
                abnormalTerminationArea.getAbtPgmErrorData().setAbtErrorSection("1200-");
                z980AbnormalTerm(abnormalTerminationArea);
            }
        } catch (ZFileException e) {
            abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
            abnormalTerminationArea.getAbtPgmErrorData().setAbtBatchStatus(e.getErrnoMsg());
            abtDaFunction = "OPEN";
            abnormalTerminationArea.getAbtDataAccessInfo().setAbtDaAccessName("MXPA180I");
            abnormalTerminationArea.getAbtPgmErrorData().setAbtErrorActivity("SEQ");
            abnormalTerminationArea.getAbtPgmErrorData().setAbtErrorSection("1200-");
            z980AbnormalTerm(abnormalTerminationArea);
        }
    }

    public void processHeaderRec2200(WsDate1 wsDate1, WsTime wsTime) {
        HRegHdrRec hRegHdrRec = new HRegHdrRec();
        WsSystemTime wsSystemTime = new WsSystemTime();
        hRegHdrRec.setHHdrRec();
        hRegHdrRec.setHDocData();
        hRegHdrRec.setHCreateDate(wsDate1.getBytes());
        try {
            Rxbpa180.getInstance().getAaaFormatOutFile().write(hRegHdrRec.getBytes());
        } catch (ZFileException e) {
            e.printStackTrace();
        }
        try {
            Rxbpa180.getInstance().getWsSystemTime().setBytes(new SimpleDateFormat("HHmmss").format(new Date()));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        wsTime.setWsHour(Rxbpa180.getInstance().getWsSystemTime().getWsHour());
        wsTime.setWsMin(Rxbpa180.getInstance().getWsSystemTime().getWsMin());
        hRegHdrRec.setHCreateTime(wsTime.getBytes());
        hRegHdrRec.setHMfgName("AAA");
        try {
            Rxbpa180.getInstance().getAaaFormatOutFile().write(hRegHdrRec.getBytes());
        } catch (ZFileException e) {
            e.printStackTrace();
        }
    }

    public void z970SetDaStatusBatch() {
        AbnormalTerminationArea abnormalTerminationArea = new AbnormalTerminationArea();
        String daDbmerrorLit = "DBM";
        String daDuplicateLit = "DUP";
        String daEndfileLit = "EOF";
        String daLogicerrLit = "LOG";
        String daNotavailLit = "NAV";
        String daNotfoundLit = "NFD";
        String daOkLit = "OK ";
        String daSecurityLit = "SEC";
        String daStatusFile = "";
        DataAccessStatus dataAccessStatus = new DataAccessStatus();
        final String datasetDupkey = "02";
        final String datasetDuprec = "22";
        final String datasetEndfile = "10";
        final String datasetIllogic = "92";
        final String datasetInvreq = "91";
        final String datasetNotfnd = "23";
        final String datasetNotopen = "95";
        final String datasetOk = "00";
        final String datasetVerified = "97";
        daStatusFile = ((Filler41) (abnormalTerminationArea.getAbtDataAccessInfo())).getAbtBatchStatus();
        // switch (daStatusFile) {
            // case daStatusFile.equals(datasetOk) || daStatusFile.equals(datasetVerified):
            //     dataAccessStatus.setDaStatus(daOkLit);
            //     break;
            // case daStatusFile.equals(datasetNotfnd):
            //     dataAccessStatus.setDaStatus(daNotfoundLit);
            //     break;
            // case daStatusFile.equals(datasetEndfile):
            //     dataAccessStatus.setDaStatus(daEndfileLit);
            //     break;
            // case daStatusFile.equals(datasetDuprec) || daStatusFile.equals(datasetDupkey):
            //     dataAccessStatus.setDaStatus(daDuplicateLit);
            //     break;
            // case daStatusFile.equals(datasetIllogic):
            //     dataAccessStatus.setDaStatus(daLogicerrLit);
            //     break;
            // case daStatusFile.equals(datasetInvreq):
            //     dataAccessStatus.setDaStatus(daSecurityLit);
            //     break;
            // case daStatusFile.equals(datasetNotopen):
            //     dataAccessStatus.setDaStatus(daNotavailLit);
            //     break;
            // default:
            //     dataAccessStatus.setDaStatus(daDbmerrorLit);
            //     break;
        // }
        switch (daStatusFile) {
            case datasetOk:
            case datasetVerified:
                dataAccessStatus.setDaStatus(daOkLit);
                break;
            case datasetNotfnd:
                dataAccessStatus.setDaStatus(daNotfoundLit);
                break;
            case datasetEndfile:
                dataAccessStatus.setDaStatus(daEndfileLit);
                break;
            case datasetDuprec:
            case datasetDupkey:
                dataAccessStatus.setDaStatus(daDuplicateLit);
                break;
            case datasetIllogic:
                dataAccessStatus.setDaStatus(daLogicerrLit);
                break;
            case datasetInvreq:
                dataAccessStatus.setDaStatus(daSecurityLit);
                break;
            case datasetNotopen:
                dataAccessStatus.setDaStatus(daNotavailLit);
                break;
            default:
                dataAccessStatus.setDaStatus(daDbmerrorLit);
                break;
        }
    }

    public void readInputFile1300(AbnormalTerminationArea abnormalTerminationArea, WsCountFields wsCountFields,
            WsFlags wsFlags) {
        AaaInboundLayout aaaInboundLayout = new AaaInboundLayout();
        String abtDaFunction = "";
        String wsAaaInfileStatus = "";
        String wsGoodStatus = "20";
        try {
            int jdeclNread = aaaFormatInRecord.readFrom(Rxbpa180.getInstance().getAaaFormatInFile());
            if (jdeclNread == -1) {
                wsFlags.setWsEofSw('Y');
            } else {
                wsCountFields.setWsTotalRecsRead(wsCountFields.getWsTotalRecsRead() + 1);
            }
        } catch (ZFileException e) {
            wsAaaInfileStatus = e.getErrnoMsg();
            abnormalTerminationArea.getAbtPgmErrorData().setAbtErrorActivity("READ");
            abnormalTerminationArea.getAbtPgmErrorData().setAbtDaFunction("SEQ");
            abnormalTerminationArea.getAbtDataAccessInfo().setAbtDaAccessName("MXPA180I");
            abnormalTerminationArea.getAbtErrorSection().setAbtErrorSection("1300-");
            z980AbnormalTerm(abnormalTerminationArea);
        }
    }

    public void z970SetDaStatusDb2() {
        AbnormalTerminationArea abnormalTerminationArea = new AbnormalTerminationArea();
        String daDbmerrorLit = "DBM";
        String daDuplicateLit = "DUP";
        String daLogicerrLit = "LOG";
        String daNotavailLit = "NAV";
        String daNotfoundLit = "NFD";
        String daOkLit = "OK ";
        String daSecurityLit = "SEC";
        DataAccessStatus dataAccessStatus = new DataAccessStatus();

        // edit by Ruman
        // dataAccessStatus.setDaStatus(abnormalTerminationArea.getAbtDataAccessInfo().getAbtDb2Status() == 0 ? daOkLit
        //         : abnormalTerminationArea.getAbtDataAccessInfo().getAbtDb2Status() == 100 ? daNotfoundLit
        //                 : abnormalTerminationArea.getAbtDataAccessInfo().getAbtDb2Status() == -803
        //                         || abnormalTerminationArea.getAbtDataAccessInfo().getAbtDb2Status() == -811
        //                                 ? daDuplicateLit
        //                                 : abnormalTerminationArea.getAbtDataAccessInfo().getAbtDb2Status() == -501
        //                                         || abnormalTerminationArea.getAbtDataAccessInfo()
        //                                                 .getAbtDb2Status() == -502
        //                                                         ? daLogicerrLit
        //                                                         : abnormalTerminationArea.getAbtDataAccessInfo()
        //                                                                 .getAbtDb2Status() == -922
        //                                                                         ? daSecurityLit
        //                                                                         : abnormalTerminationArea
        //                                                                                 .getAbtDataAccessInfo()
        //                                                                                 .getAbtDb2Status() == -911
        //                                                                                 || abnormalTerminationArea
        //                                                                                         .getAbtDataAccessInfo()
        //                                                                                         .getAbtDb2Status() == -913
        //                                                                                                 ? daNotavailLit
        //                                                                                                 : daDbmerrorLit);
        // replaced entire block with following
        dataAccessStatus.setDaStatus(abnormalTerminationArea.getAbtDataAccessInfo().isAbtDaOk() ? daOkLit
                : abnormalTerminationArea.getAbtDataAccessInfo().isAbtDaNotfound() ? daNotfoundLit
                : abnormalTerminationArea.getAbtDataAccessInfo().isAbtDaDuplicate() ? daDuplicateLit
                : abnormalTerminationArea.getAbtDataAccessInfo().isAbtDaLogicerr() ? daLogicerrLit
                : abnormalTerminationArea.getAbtDataAccessInfo().isAbtDaSecurity() ? daSecurityLit
                : abnormalTerminationArea.getAbtDataAccessInfo().isAbtDaNotavail() ? daNotavailLit
                : daDbmerrorLit);
        // edit end
    }


    public void processCustDtl2100(AaaInboundLayout aaaInboundLayout, AbnormalTerminationArea abnormalTerminationArea,
            Dclvwmtrli dclvwmtrli, LegalName legalName) {
        String abtDaFunction = "";
        legalName.setBytes(new byte[legalName.getBytes().length]);
        try {
            String sql = "SELECT LEGAL_NAME FROM VWMCU00 WHERE CUST_NO = ?";
            // PreparedStatement ps = JdbcConnection.connection.prepareStatement(sql);
            // edited by Ruman
            Connection connection = DriverManager.getConnection("jdbc:example_database_url", "example_username", "example_password");
            PreparedStatement ps = connection.prepareStatement(sql);
            ps.setInt(1, dclvwmtrli.getDlrNo());
            ResultSet rs = ps.executeQuery();
            rs.next();
            legalName.setBytes(rs.getBytes(1));
            ps.close();
        } catch (SQLException exception) {
            abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
            ((Filler37) (abnormalTerminationArea.getAbtDataAccessInfo())).setAbtDb2Status(exception.getErrorCode());
            abtDaFunction = "SELECT";
            abnormalTerminationArea.getAbtDataAccessInfo().setAbtDaAccessName("VWMCU00");
            abnormalTerminationArea.getAbtPgmErrorData().setAbtErrorActivity("DB2");
            abnormalTerminationArea.getAbtPgmErrorData().setAbtErrorSection("2100-");
            System.out.println("SERIAL NO IS:" + aaaInboundLayout.getAaaInSerialNo());
            System.out.println("DLR FROM TRUST LINE IS:" + dclvwmtrli.getDlrNo());
            z980AbnormalTerm(abnormalTerminationArea);
        }
    }

    public void validateSerinalNo2510(AaaInboundLayout aaaInboundLayout,
            AbnormalTerminationArea abnormalTerminationArea,
            Dclvwmtrli dclvwmtrli, WsFlags wsFlags) throws java.sql.SQLException, java.sql.SQLException,
            java.sql.SQLException, java.sql.SQLException, java.sql.SQLException, java.sql.SQLException,
            java.sql.SQLException, java.sql.SQLException, java.sql.SQLException, java.sql.SQLException,
            java.sql.SQLException, java.sql.SQLException, java.sql.SQLException, java.sql.SQLException,
            java.sql.SQLException, java.sql.SQLException, java.sql.SQLException, java.sql.SQLException {
        abtDaFunction = "";
        String sql = "SELECT DLR_NO, TRUST_NO, TRUST_LINE_NO, BAL_DUE_AMT, MODEL_NO, SERIAL_NO, DIST_NO FROM VWMTRLI WHERE SERIAL_NO = ?";
        // PreparedStatement ps = JdbcConnection.connection.prepareStatement(sql);
        // edited by Ruman
        Connection connection = DriverManager.getConnection("jdbc:example_database_url", "example_username", "example_password");
        PreparedStatement ps = connection.prepareStatement(sql);
        ps.setString(1, aaaInboundLayout.getAaaInSerialNo());
        ResultSet rs = ps.executeQuery();
        if (rs.next()) {
            dclvwmtrli.setDlrNo(rs.getInt(1));
            dclvwmtrli.setTrustNo(rs.getString(2));
            dclvwmtrli.setTrustLineNo(rs.getInt(3));
            dclvwmtrli.setBalDueAmt(rs.getLong(4));
            dclvwmtrli.setModelNo(rs.getString(5));
            dclvwmtrli.setSerialNo(rs.getString(6));
            dclvwmtrli.setDistNo(rs.getInt(7));
            wsFlags.setProcessInAims();
        } else {
            wsFlags.setNotProcessInAims();
        }
        ps.close();
        if (connection.getWarnings() != null) {
            for (Iterator<Throwable> iter = connection.getWarnings().iterator(); iter.hasNext();) {
                Throwable w = iter.next();
                if (w instanceof SQLWarning) {
                    SQLWarning sqlwarn = (SQLWarning) w;
                    abnormalTerminationArea.getAbtDataAccessInfo().setAbtDb2Status(sqlwarn.getErrorCode());
                    if (sqlwarn.getErrorCode() == -811) {
                        System.out.println("DUPLICATES IN AIMS:" + aaaInboundLayout.getAaaInSerialNo());
                        wsFlags.setNotProcessInAims();
                    } else {
                        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
                        abnormalTerminationArea.getAbtPgmErrorData().setAbtErrorActivity("DB2");
                        abnormalTerminationArea.getAbtPgmErrorData().setAbtDaAccessName("VWMTRLI");
                        abnormalTerminationArea.getAbtPgmErrorData().setAbtErrorSection("2510-");
                        System.out.println("3A SERAIL NO IS:" + aaaInboundLayout.getAaaInSerialNo());
                        z980AbnormalTerm(abnormalTerminationArea);
                    }
                }
            }
        }
        if (ps.getWarnings() != null) {
            for (Iterator<Throwable> iter = ps.getWarnings().iterator(); iter.hasNext();) {
                Throwable w = iter.next();
                if (w instanceof SQLWarning) {
                    SQLWarning sqlwarn = (SQLWarning) w;
                    abnormalTerminationArea.getAbtDataAccessInfo().setAbtDb2Status(sqlwarn.getErrorCode());
                    if (sqlwarn.getErrorCode() == -811) {
                        System.out.println("DUPLICATES IN AIMS:" + aaaInboundLayout.getAaaInSerialNo());
                        wsFlags.setNotProcessInAims();
                    } else {
                        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
                        abnormalTerminationArea.getAbtPgmErrorData().setAbtErrorActivity("DB2");
                        abnormalTerminationArea.getAbtPgmErrorData().setAbtDaAccessName("VWMTRLI");
                        abnormalTerminationArea.getAbtPgmErrorData().setAbtErrorSection("2510-");
                        System.out.println("3A SERAIL NO IS:" + aaaInboundLayout.getAaaInSerialNo());
                        z980AbnormalTerm(abnormalTerminationArea);
                    }
                }
            }
        }
        if (rs.getWarnings() != null) {
            for (Iterator<Throwable> iter = rs.getWarnings().iterator(); iter.hasNext();) {
                Throwable w = iter.next();
                if (w instanceof SQLWarning) {
                    SQLWarning sqlwarn = (SQLWarning) w;
                    abnormalTerminationArea.getAbtDataAccessInfo().setAbtDb2Status(sqlwarn.getErrorCode());
                    if (sqlwarn.getErrorCode() == -811) {
                        System.out.println("DUPLICATES IN AIMS:" + aaaInboundLayout.getAaaInSerialNo());
                        wsFlags.setNotProcessInAims();
                    } else {
                        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
                        abnormalTerminationArea.getAbtPgmErrorData().setAbtErrorActivity("DB2");
                        abnormalTerminationArea.getAbtPgmErrorData().setAbtDaAccessName("VWMTRLI");
                        abnormalTerminationArea.getAbtPgmErrorData().setAbtErrorSection("2510-");
                        System.out.println("3A SERAIL NO IS:" + aaaInboundLayout.getAaaInSerialNo());
                        z980AbnormalTerm(abnormalTerminationArea);
                    }
                }
            }
        }
    }
    public void processDetailRec2300(AaaInboundLayout aaaInboundLayout, Dclvwmcucp dclvwmcucp, Dclvwmtrli dclvwmtrli,
            LegalName legalName, WsCountFields wsCountFields, WsDate1 wsDate1, WsTime wsTime) {
        DRegDtlRec dRegDtlRec = new DRegDtlRec();
        WsAaaDate wsAaaDate = new WsAaaDate();
        WsDistNoChar wsDistNoChar = new WsDistNoChar();
        int wsSaveDistNo = 0;
        ZRegTrlRec zRegTrlRec = new ZRegTrlRec();
        dRegDtlRec.setDRecordType("3A");
        dRegDtlRec.setDCreateTime(wsTime.getWsTime());
        dRegDtlRec.setDCreateDate(wsDate1.getWsDate1());
        dRegDtlRec.setDVendDlrName(legalName.getLegalNameText().substring(0, legalName.getLegalNameLen()));
        dRegDtlRec.setDModelNbr(dclvwmtrli.getModelNo());
        wsSaveDistNo = dclvwmtrli.getDistNo();
        wsDistNoChar.setWsSaveDistNoLast6(
                Integer.toString(wsSaveDistNo).substring(Integer.toString(wsSaveDistNo).length() - 6));
        dRegDtlRec.setDDistNo(wsDistNoChar.getWsSaveDistNoLast6());
        dRegDtlRec.setDModelDesc("3A MODEL DATA");
        dRegDtlRec.setDSerialNbr(dclvwmtrli.getSerialNo());
        dRegDtlRec.setDVendDlrNo(dclvwmcucp.getCpuDealerNo());
        wsAaaDate.setWsAaaDate(aaaInboundLayout.getAaaInRegCompDate().substring(0, 4) + "-"
                + aaaInboundLayout.getAaaInRegCompDate().substring(4, 6) + "-"
                + aaaInboundLayout.getAaaInRegCompDate().substring(6, 8));
        dRegDtlRec.setDRegComplDate(wsAaaDate.getWsAaaDate());
        if (aaaInboundLayout.getAaaInRegType().equals("PRG") || aaaInboundLayout.getAaaInRegType().equals("DEM")) {
            dRegDtlRec.setDRegType(aaaInboundLayout.getAaaInRegType());
        } else {
            dRegDtlRec.setDRegType("OTH");
        }
        zRegTrlRec.setZCustDtltrlCount(zRegTrlRec.getZCustDtltrlCount() + 1);
        aaaFormatOutRecord.setBytes(dRegDtlRec.getBytes());
        aaaFormatOutRecord.writeTo(Rxbpa180.getInstance().getAaaFormatOutFile());
    }

// rocessDetailRec2300() method with IBM watsonx Code Assistant
//         // for Z
//     }

    public void processAaaFile2000(AaaInboundLayout aaaInboundLayout, AbnormalTerminationArea abnormalTerminationArea,
            WsCountFields wsCountFields, WsDate1 wsDate1, WsFlags wsFlags, WsTime wsTime, ZRegTrlRec zRegTrlRec) {
        Dclvwmcucp dclvwmcucp = new Dclvwmcucp();
        Dclvwmtrli dclvwmtrli = new Dclvwmtrli();
        LegalName legalName = new LegalName();
        validateCreateDate2210(aaaInboundLayout, wsDate1);
        validateSerinalNo2510(aaaInboundLayout, abnormalTerminationArea, dclvwmtrli, wsFlags);
        if (wsFlags.isProcessInAims()) {
            processCpuDlr2150(aaaInboundLayout, abnormalTerminationArea, dclvwmcucp, dclvwmtrli, wsCountFields,
                    wsFlags);
            if (wsFlags.isDlrXrefFound()) {
                processCustDtl2100(aaaInboundLayout, abnormalTerminationArea, dclvwmtrli, legalName);
                if (wsCountFields.getWsTotHdrRecCnt() == 1) {
                    processHeaderRec2200(wsDate1, wsTime);
                }
                processDetailRec2300(aaaInboundLayout, dclvwmcucp, dclvwmtrli, legalName, wsCountFields, wsDate1,
                        wsTime);
            }
        }
        readInputFile1300(abnormalTerminationArea, wsCountFields, wsFlags);
    }

    public void z980AbnormalTerm(AbnormalTerminationArea abnormalTerminationArea) {
        String abtDaFunction = "";
        BatchErrorArea batchErrorArea = new BatchErrorArea();
        DataAccessStatus dataAccessStatus = new DataAccessStatus();
        SqlErrmsg sqlErrmsg = new SqlErrmsg();
        Sqlca sqlca = new Sqlca();
        if (abnormalTerminationArea.getAbtControlInfo().getAbtInProgress() == 'N') {
            abnormalTerminationArea.getAbtControlInfo().setAbtInProgress('Y');
        }
        else {
            if (this.getReturnCode().equals("0")) {
                this.setReturnCode(Integer.parseInt("16"));
            }
            System.out.println(" ");
            System.out.println("** ABEND ROUTINE PERCOLATION, PROGRAM ENDED **");
            return;
        }
        abtDaFunction = abnormalTerminationArea.getAbtDataAccessInfo().getAbtDaGenericStatus();
        abnormalTerminationArea.getAbtControlInfo().setAbtDynamicControlPgm("ABENDRTN");
        abnormalTerminationArea.getAbtControlInfo().setAbtDoWrite();
        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
        abnormalTerminationArea.getAbtControlInfo().setAbtErrorMessage("Db2");   // edited by Ruman
        abnormalTerminationArea.getAbtControlInfo().setAbtContinueProcess();
        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
        abnormalTerminationArea.getAbtControlInfo().setAbtErrorMessage("Db2");   // edited by Ruman
        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
        abnormalTerminationArea.getAbtControlInfo().setAbtErrorMessage("Db2");   // edited by Ruman
        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
        abnormalTerminationArea.getAbtControlInfo().setAbtErrorMessage("Db2");   // edited by Ruman
        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
        abnormalTerminationArea.getAbtControlInfo().setAbtErrorMessage("Db2");   // edited by Ruman
        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
        abnormalTerminationArea.getAbtControlInfo().setAbtErrorMessage("Db2");   // edited by Ruman
        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
        abnormalTerminationArea.getAbtControlInfo().setAbtErrorMessage("Db2");   // edited by Ruman
        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
        abnormalTerminationArea.getAbtControlInfo().setAbtErrorMessage("Db2");   // edited by Ruman
        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
        abnormalTerminationArea.getAbtControlInfo().setAbtErrorMessage("Db2");   // edited by Ruman
        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
        abnormalTerminationArea.getAbtControlInfo().setAbtErrorMessage("Db2");   // edited by Ruman
        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
        abnormalTerminationArea.getAbtControlInfo().setAbtErrorMessage("Db2");   // edited by Ruman
        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
        abnormalTerminationArea.getAbtControlInfo().setAbtErrorMessage("Db2");   // edited by Ruman
        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
        abnormalTerminationArea.getAbtControlInfo().setAbtErrorMessage("Db2");   // edited by Ruman
        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
        abnormalTerminationArea.getAbtControlInfo().setAbtErrorMessage("Db2");   // edited by Ruman
        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
        abnormalTerminationArea.getAbtControlInfo().setAbtErrorMessage("Db2");   // edited by Ruman
        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
        abnormalTerminationArea.getAbtControlInfo().setAbtErrorMessage("Db2");   // edited by Ruman
        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
        abnormalTerminationArea.getAbtControlInfo().setAbtErrorMessage("Db2");   // edited by Ruman
        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
        abnormalTerminationArea.getAbtControlInfo().setAbtErrorMessage("Db2");   // edited by Ruman
        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
        abnormalTerminationArea.getAbtControlInfo().setAbtErrorMessage("Db2");   // edited by Ruman
        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
        abnormalTerminationArea.getAbtControlInfo().setAbtErrorMessage("Db2");   // edited by Ruman
        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
        abnormalTerminationArea.getAbtControlInfo().setAbtErrorMessage("Db2");   // edited by Ruman
        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
        abnormalTerminationArea.getAbtControlInfo().setAbtErrorMessage("Db2");   // edited by Ruman
        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
        abnormalTerminationArea.getAbtControlInfo().setAbtErrorMessage("Db2");   // edited by Ruman
        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
        abnormalTerminationArea.getAbtControlInfo().setAbtErrorMessage("Db2");   // edited by Ruman
        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
        abnormalTerminationArea.getAbtControlInfo().setAbtErrorMessage("Db2");   // edited by Ruman
        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
        abnormalTerminationArea.getAbtControlInfo().setAbtErrorMessage("Db2");   // edited by Ruman
        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
        abnormalTerminationArea.getAbtControlInfo().setAbtErrorMessage("Db2");   // edited by Ruman
        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
        abnormalTerminationArea.getAbtControlInfo().setAbtErrorMessage("Db2");   // edited by Ruman
        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
        abnormalTerminationArea.getAbtControlInfo().setAbtErrorMessage("Db2");   // edited by Ruman
        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
        abnormalTerminationArea.getAbtControlInfo().setAbtErrorMessage("Db2");   // edited by Ruman
        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
        abnormalTerminationArea.getAbtControlInfo().setAbtErrorMessage("Db2");   // edited by Ruman
        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
        abnormalTerminationArea.getAbtControlInfo().setAbtErrorMessage("Db2");   // edited by Ruman
        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
        abnormalTerminationArea.getAbtControlInfo().setAbtErrorMessage("Db2");   // edited by Ruman
        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
        abnormalTerminationArea.getAbtControlInfo().setAbtErrorMessage("Db2");   // edited by Ruman
        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
        abnormalTerminationArea.getAbtControlInfo().setAbtErrorMessage("Db2");   // edited by Ruman
        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
        abnormalTerminationArea.getAbtControlInfo().setAbtErrorMessage("Db2");   // edited by Ruman
        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
        abnormalTerminationArea.getAbtControlInfo().setAbtErrorMessage("Db2");   // edited by Ruman
        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
        abnormalTerminationArea.getAbtControlInfo().setAbtErrorMessage("Db2");   // edited by Ruman
        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
        abnormalTerminationArea.getAbtControlInfo().setAbtErrorMessage("Db2");   // edited by Ruman
        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
        abnormalTerminationArea.getAbtControlInfo().setAbtErrorMessage("Db2");   // edited by Ruman
        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
        abnormalTerminationArea.getAbtControlInfo().setAbtErrorMessage("Db2");   // edited by Ruman
        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
        abnormalTerminationArea.getAbtControlInfo().setAbtErrorMessage("Db2");   // edited by Ruman
        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
        abnormalTerminationArea.getAbtControlInfo().setAbtErrorMessage("Db2");   // edited by Ruman
        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
        abnormalTerminationArea.getAbtControlInfo().setAbtErrorMessage("Db2");   // edited by Ruman
        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
        abnormalTerminationArea.getAbtControlInfo().setAbtErrorMessage("Db2");   // edited by Ruman
        abnormalTerminationArea.getAbtControlInfo().setAbtDoAbend();
    }

    public void termination9000(WsCountFields wsCountFields) {
        try {
            aaaFormatInFile.close();
            aaaFormatOutFile.close();
        } catch (Exception exception) {
            System.out.println(exception);
            return;
        }
        System.out.println("***********************************");
        System.out.println("TOTAL AAA RECORDS READ :" + wsCountFields.getWsTotalRecsRead());
        System.out.println("TOTAL HEADER RECS WRITE:" + wsCountFields.getWsTotHdrRecCnt());
        System.out.println("TOTAL DETAIL RECS WRITE:" + wsCountFields.getWsTotDtlRecCnt());
        System.out.println("TOTAL TRAILER RECS WRITE:" + wsCountFields.getWsTotTrlRecCnt());
        System.out.println("***********************************");
        System.out.println("**** MXBPA180 SUCCESSFULLY ENDS ***");
        System.out.println("***********************************");
    }

    public void validateCreateDate2210(AaaInboundLayout aaaInboundLayout, WsDate1 wsDate1) {
        String wsFrenchMonth = "";
        int wsSplit1 = 0;
        String wsSplit2 = "";
        String wsSplit3 = "";
        int wsSplit4 = 0;
        int wsSplit5 = 0;
        String wsSplitx = "";
        String wsString1 = "";
        WsSysDate wsSysDate = new WsSysDate();
        wsString1 = aaaInboundLayout.getAaaInCreateDate();
        wsSplitx = wsString1.substring(0, 4);
        wsSplit2 = wsString1.substring(5, 9);
        wsSplit3 = wsString1.substring(10, 14);
        wsSplit4 = Integer.parseInt(wsString1.substring(15, 17));
        wsSplit5 = Integer.parseInt(wsString1.substring(18, 20));
        wsFrenchMonth = wsSplitx;
        switch (wsFrenchMonth) {
            case "Jan.":
                wsSplit1 = 1;
                break;
            case "Fï¿½v.":
                wsSplit1 = 2;
                break;
            case "Mars":
                wsSplit1 = 3;
                break;
            case "Avr.":
                wsSplit1 = 4;
                break;
            case "Mai":
                wsSplit1 = 5;
                break;
            case "Juin":
                wsSplit1 = 6;
                break;
            case "Jul.":
                wsSplit1 = 7;
                break;
            case "Aoï¿½t":
                wsSplit1 = 8;
                break;
            case "Sep.":
                wsSplit1 = 9;
                break;
            case "Oct.":
                wsSplit1 = 10;
                break;
            case "Nov.":
                wsSplit1 = 11;
                break;
            case "Dï¿½c.":
                wsSplit1 = 12;
                break;
            default:
                wsSysDate.setWsSysMm(aaaInboundLayout.getAaaInCreateDate().substring(5, 7));
                System.out.println("CREATE DATE :" + aaaInboundLayout.getAaaInCreateDate());
        }
        wsDate1.setWsDate1Yyyy(Integer.parseInt(wsSplit2));
        wsDate1.setWsDate1Mm(wsSplit1);
        wsDate1.setWsDate1Dd(wsSplit4);
    }

    public static void main(String[] args) {
        getInstance().mainlineProcessing0000();
    }
}
