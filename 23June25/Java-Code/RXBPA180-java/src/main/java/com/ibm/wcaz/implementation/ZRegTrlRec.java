package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.ExternalDecimalAsIntField;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class ZRegTrlRec implements Cloneable, Comparable<ZRegTrlRec> {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private static final String Z_TRL_REC_VALUE = "URBZ";
    
    private String zRecordType = "";
    private String zCdfCustId = "1133";
    private String zCdfCustCode = "0000";
    private String zCreateDate = "";
    private String zCreateTime = "";
    private int zCustDtltrlCount = 0;
    
    /** Initialize fields to non-null default values */
    public ZRegTrlRec() {
        initFiller();
    }
    
    /** Initialize all fields to provided values */
    public ZRegTrlRec(String zRecordType, String zCdfCustId, String zCdfCustCode, String zCreateDate, String zCreateTime, int zCustDtltrlCount) {
        this.zRecordType = zRecordType;
        this.zCdfCustId = zCdfCustId;
        this.zCdfCustCode = zCdfCustCode;
        this.zCreateDate = zCreateDate;
        this.zCreateTime = zCreateTime;
        this.zCustDtltrlCount = zCustDtltrlCount;
        initFiller();
    }
    
    @Override
    public ZRegTrlRec clone() throws CloneNotSupportedException {
        ZRegTrlRec cloned = (ZRegTrlRec) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code ZRegTrlRec} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected ZRegTrlRec(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code ZRegTrlRec} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected ZRegTrlRec(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code ZRegTrlRec} object
     * @see #setBytes(byte[], int)
     */
    public static ZRegTrlRec fromBytes(byte[] bytes, int offset) {
        return new ZRegTrlRec(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code ZRegTrlRec} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static ZRegTrlRec fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code ZRegTrlRec} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static ZRegTrlRec fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getZRecordType() {
        return this.zRecordType;
    }
    
    public void setZRecordType(String zRecordType) {
        this.zRecordType = zRecordType;
    }
    
    public boolean isZTrlRec() {
        return zRecordType.equals(Z_TRL_REC_VALUE);
    }
    
    public void setZTrlRec() {
        zRecordType = Z_TRL_REC_VALUE;
    }
    
    public String getZCdfCustId() {
        return this.zCdfCustId;
    }
    
    public void setZCdfCustId(String zCdfCustId) {
        this.zCdfCustId = zCdfCustId;
    }
    
    public String getZCdfCustCode() {
        return this.zCdfCustCode;
    }
    
    public void setZCdfCustCode(String zCdfCustCode) {
        this.zCdfCustCode = zCdfCustCode;
    }
    
    public String getZCreateDate() {
        return this.zCreateDate;
    }
    
    public void setZCreateDate(String zCreateDate) {
        this.zCreateDate = zCreateDate;
    }
    
    public String getZCreateTime() {
        return this.zCreateTime;
    }
    
    public void setZCreateTime(String zCreateTime) {
        this.zCreateTime = zCreateTime;
    }
    
    public int getZCustDtltrlCount() {
        return this.zCustDtltrlCount;
    }
    
    public void setZCustDtltrlCount(int zCustDtltrlCount) {
        this.zCustDtltrlCount = zCustDtltrlCount;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        zRecordType = "";
        zCdfCustId = "";
        zCdfCustCode = "";
        zCreateDate = "";
        zCreateTime = "";
        zCustDtltrlCount = 0;
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder();
        s.append("{ zRecordType=\"");
        s.append(getZRecordType());
        s.append("\"");
        s.append(", zCdfCustId=\"");
        s.append(getZCdfCustId());
        s.append("\"");
        s.append(", zCdfCustCode=\"");
        s.append(getZCdfCustCode());
        s.append("\"");
        s.append(", zCreateDate=\"");
        s.append(getZCreateDate());
        s.append("\"");
        s.append(", zCreateTime=\"");
        s.append(getZCreateTime());
        s.append("\"");
        s.append(", zCustDtltrlCount=\"");
        s.append(getZCustDtltrlCount());
        s.append("\"");
        s.append(", filler25=\"");
        s.append(new String(filler25, encoding));
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(ZRegTrlRec that) {
        return this.zRecordType.equals(that.zRecordType) &&
            this.zCdfCustId.equals(that.zCdfCustId) &&
            this.zCdfCustCode.equals(that.zCdfCustCode) &&
            this.zCreateDate.equals(that.zCreateDate) &&
            this.zCreateTime.equals(that.zCreateTime) &&
            this.zCustDtltrlCount == that.zCustDtltrlCount &&
            Arrays.equals(this.filler25, that.filler25);
    }
    
    /** Per-field equality comparison; only returns equal if argument is the same class */
    @Override
    public boolean equals(Object that) {
        return (that instanceof ZRegTrlRec other) && other.canEqual(this) && this.equals(other);
    }
    
    /** Does {@code that} have the same runtime type as {@code this}? */
    public boolean canEqual(Object that) {
        return that instanceof ZRegTrlRec;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * result + Objects.hashCode(zRecordType);
        result = 31 * result + Objects.hashCode(zCdfCustId);
        result = 31 * result + Objects.hashCode(zCdfCustCode);
        result = 31 * result + Objects.hashCode(zCreateDate);
        result = 31 * result + Objects.hashCode(zCreateTime);
        result = 31 * result + Integer.hashCode(zCustDtltrlCount);
        result = 31 * result + Arrays.hashCode(filler25);
        return result;
    }
    
    @Override
    public int compareTo(ZRegTrlRec that) {
        int c = 0;
        c = this.zRecordType.compareTo(that.zRecordType);
        if ( c != 0 ) return c;
        c = this.zCdfCustId.compareTo(that.zCdfCustId);
        if ( c != 0 ) return c;
        c = this.zCdfCustCode.compareTo(that.zCdfCustCode);
        if ( c != 0 ) return c;
        c = this.zCreateDate.compareTo(that.zCreateDate);
        if ( c != 0 ) return c;
        c = this.zCreateTime.compareTo(that.zCreateTime);
        if ( c != 0 ) return c;
        c = Integer.compare(this.zCustDtltrlCount, that.zCustDtltrlCount);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler25, that.filler25);
        if ( c == 0 && !(that.canEqual(this) && this.canEqual(that)) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
    }
    
    private static final StringField Z_RECORD_TYPE = factory.getStringField(4);
    private static final StringField Z_CDF_CUST_ID = factory.getStringField(4);
    private static final StringField Z_CDF_CUST_CODE = factory.getStringField(4);
    private static final StringField Z_CREATE_DATE = factory.getStringField(10);
    private static final StringField Z_CREATE_TIME = factory.getStringField(5);
    private static final ExternalDecimalAsIntField Z_CUST_DTLTRL_COUNT = factory.getExternalDecimalAsIntField(6, true);
    private static final ByteArrayField FILLER_25 = factory.getByteArrayField(167);
    private byte[] filler25 = new byte[167];
    private void initFiller() {
        new StringField(0, 167).putString("", filler25);
    }
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code ZRegTrlRec} object
     * @param bytes  preallocated byte array to store the object serialization
     * @param offset offset in the byte array where serialization should begin
     * @return the byte array, updated with the newly added data formatted as in the {@code Z-REG-TRL-REC} record
     * @see "Z-REG-TRL-REC record at RXBPA180.cbl:84"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        Z_RECORD_TYPE.putString(zRecordType, bytes, offset);
        Z_CDF_CUST_ID.putString(zCdfCustId, bytes, offset);
        Z_CDF_CUST_CODE.putString(zCdfCustCode, bytes, offset);
        Z_CREATE_DATE.putString(zCreateDate, bytes, offset);
        Z_CREATE_TIME.putString(zCreateTime, bytes, offset);
        Z_CUST_DTLTRL_COUNT.putInt(zCustDtltrlCount, bytes, offset);
        FILLER_25.putByteArray(filler25, bytes, offset);
        return bytes;
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code ZRegTrlRec} object,
     * starting at the beginning of the array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes(byte[] bytes) {
        return getBytes(bytes, 0);
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code ZRegTrlRec} object,
     * allocating a new array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes() {
        return getBytes(new byte[numBytes()]);
    }
    
    /**
     * Retrieves a COBOL-format string representation of the {@code ZRegTrlRec} object
     * @return the result of {@link #getBytes()} interpreted using the IBM-1047 EBCDIC encoding
     */
    public final String toByteString() {
        return new String(getBytes(), encoding);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array
     * @param bytes  byte array formatted as in the {@code Z-REG-TRL-REC} record; will be space-padded to length if necessary
     * @param offset offset in the byte array where deserialization should begin
     * @see "Z-REG-TRL-REC record at RXBPA180.cbl:84"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        zRecordType = Z_RECORD_TYPE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        zCdfCustId = Z_CDF_CUST_ID.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        zCdfCustCode = Z_CDF_CUST_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        zCreateDate = Z_CREATE_DATE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        zCreateTime = Z_CREATE_TIME.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        zCustDtltrlCount = Z_CUST_DTLTRL_COUNT.getInt(bytes, offset);
        FILLER_25.getByteArray(bytes, offset);
    }
    
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array,
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(byte[] bytes) {
        setBytes(bytes, 0);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format string.
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(String bytes) {
        setBytes(bytes.getBytes(encoding));
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
