package com.ibm.wcaz.implementation;

import java.nio.charset.Charset;
import java.util.Arrays;

import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.ExternalDecimalAsIntField;

public class WsCountFields implements Cloneable, Comparable<WsCountFields> {
    private static final Charset encoding = Charset.forName("IBM-1047");

    private int wsTotalRecsRead = 0;
    private int wsTotHdrRecCnt = 0;
    private int wsTotDtlRecCnt = 0;
    private int wsTotTrlRecCnt = 0;
    private String abtPgmName = "";

    /** Initialize fields to non-null default values */
    public WsCountFields() {}

    /** Initialize all fields to provided values */
    public WsCountFields(int wsTotalRecsRead, int wsTotHdrRecCnt, int wsTotDtlRecCnt, int wsTotTrlRecCnt) {
        this.wsTotalRecsRead = wsTotalRecsRead;
        this.wsTotHdrRecCnt = wsTotHdrRecCnt;
        this.wsTotDtlRecCnt = wsTotDtlRecCnt;
        this.wsTotTrlRecCnt = wsTotTrlRecCnt;
    }

    @Override
    public WsCountFields clone() throws CloneNotSupportedException {
        WsCountFields cloned = (WsCountFields) super.clone();
        return cloned;
    }

    /**
     * Initialize {@code WsCountFields} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected WsCountFields(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }

    /**
     * Initialize {@code WsCountFields} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected WsCountFields(byte[] bytes) {
        this(bytes, 0);
    }

    /**
     * Deserialize the COBOL-formatted byte array into a new {@code WsCountFields} object
     * @see #setBytes(byte[], int)
     */
    public static WsCountFields fromBytes(byte[] bytes, int offset) {
        return new WsCountFields(bytes, offset);
    }

    /**
     * Deserialize the COBOL-formatted byte array into a new {@code WsCountFields} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static WsCountFields fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }

    /**
     * Deserialize the COBOL-formatted string into a new {@code WsCountFields} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static WsCountFields fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }

    public int getWsTotalRecsRead() {
        return this.wsTotalRecsRead;
    }

    public void setWsTotalRecsRead(int wsTotalRecsRead) {
        this.wsTotalRecsRead = wsTotalRecsRead;
    }

    public int getWsTotHdrRecCnt() {
        return this.wsTotHdrRecCnt;
    }

    public void setWsTotHdrRecCnt(int wsTotHdrRecCnt) {
        this.wsTotHdrRecCnt = wsTotHdrRecCnt;
    }

    public int getWsTotDtlRecCnt() {
        return this.wsTotDtlRecCnt;
    }

    public void setWsTotDtlRecCnt(int wsTotDtlRecCnt) {
        this.wsTotDtlRecCnt = wsTotDtlRecCnt;
    }

    public int getWsTotTrlRecCnt() {
        return this.wsTotTrlRecCnt;
    }

    public void setWsTotTrlRecCnt(int wsTotTrlRecCnt) {
        this.wsTotTrlRecCnt = wsTotTrlRecCnt;
    }

    public void setAbtPgmName(String abtPgmName) {
        this.abtPgmName = abtPgmName;
    }

    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        wsTotalRecsRead = 0;
        wsTotHdrRecCnt = 0;
        wsTotDtlRecCnt = 0;
        wsTotTrlRecCnt = 0;
    }

    public String toString() {
        StringBuilder s = new StringBuilder();
        s.append("{ wsTotalRecsRead=\"");
        s.append(getWsTotalRecsRead());
        s.append("\"");
        s.append(", wsTotHdrRecCnt=\"");
        s.append(getWsTotHdrRecCnt());
        s.append("\"");
        s.append(", wsTotDtlRecCnt=\"");
        s.append(getWsTotDtlRecCnt());
        s.append("\"");
        s.append(", wsTotTrlRecCnt=\"");
        s.append(getWsTotTrlRecCnt());
        s.append("\"");
        s.append("}");
        return s.toString();
    }

    private boolean equals(WsCountFields that) {
        return this.wsTotalRecsRead == that.wsTotalRecsRead &&
            this.wsTotHdrRecCnt == that.wsTotHdrRecCnt &&
            this.wsTotDtlRecCnt == that.wsTotDtlRecCnt &&
            this.wsTotTrlRecCnt == that.wsTotTrlRecCnt;
    }

    /** Per-field equality comparison; only returns equal if argument is the same class */
    @Override
    public boolean equals(Object that) {
        return (that instanceof WsCountFields other) && other.canEqual(this) && this.equals(other);
    }

    /** Does {@code that} have the same runtime type as {@code this}? */
    public boolean canEqual(Object that) {
        return that instanceof WsCountFields;
    }

    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * result + Integer.hashCode(wsTotalRecsRead);
        result = 31 * result + Integer.hashCode(wsTotHdrRecCnt);
        result = 31 * result + Integer.hashCode(wsTotDtlRecCnt);
        result = 31 * result + Integer.hashCode(wsTotTrlRecCnt);
        return result;
    }

    @Override
    public int compareTo(WsCountFields that) {
        int c = 0;
        c = Integer.compare(this.wsTotalRecsRead, that.wsTotalRecsRead);
        if ( c != 0 ) return c;
        c = Integer.compare(this.wsTotHdrRecCnt, that.wsTotHdrRecCnt);
        if ( c != 0 ) return c;
        c = Integer.compare(this.wsTotDtlRecCnt, that.wsTotDtlRecCnt);
        if ( c != 0 ) return c;
        c = Integer.compare(this.wsTotTrlRecCnt, that.wsTotTrlRecCnt);
        if ( c == 0 && !(that.canEqual(this) && this.canEqual(that)) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }

    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
    }

    private static final ExternalDecimalAsIntField WS_TOTAL_RECS_READ = factory.getExternalDecimalAsIntField(9, true);
    private static final ExternalDecimalAsIntField WS_TOT_HDR_REC_CNT = factory.getExternalDecimalAsIntField(9, true);
    private static final ExternalDecimalAsIntField WS_TOT_DTL_REC_CNT = factory.getExternalDecimalAsIntField(9, true);
    private static final ExternalDecimalAsIntField WS_TOT_TRL_REC_CNT = factory.getExternalDecimalAsIntField(9, true);
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata

    /**
     * Retrieves a COBOL-format byte array representation of the {@code WsCountFields} object
     * @param bytes  preallocated byte array to store the object serialization
     * @param offset offset in the byte array where serialization should begin
     * @return the byte array, updated with the newly added data formatted as in the {@code WS-COUNT-FIELDS} record
     * @see "WS-COUNT-FIELDS record at RXBPA180.cbl:144"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        WS_TOTAL_RECS_READ.putInt(wsTotalRecsRead, bytes, offset);
        WS_TOT_HDR_REC_CNT.putInt(wsTotHdrRecCnt, bytes, offset);
        WS_TOT_DTL_REC_CNT.putInt(wsTotDtlRecCnt, bytes, offset);
        WS_TOT_TRL_REC_CNT.putInt(wsTotTrlRecCnt, bytes, offset);
        return bytes;
    }

    /**
     * Retrieves a COBOL-format byte array representation of the {@code WsCountFields} object,
     * starting at the beginning of the array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes(byte[] bytes) {
        return getBytes(bytes, 0);
    }

    /**
     * Retrieves a COBOL-format byte array representation of the {@code WsCountFields} object,
     * allocating a new array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes() {
        return getBytes(new byte[numBytes()]);
    }

    /**
     * Retrieves a COBOL-format string representation of the {@code WsCountFields} object
     * @return the result of {@link #getBytes()} interpreted using the IBM-1047 EBCDIC encoding
     */
    public final String toByteString() {
        return new String(getBytes(), encoding);
    }

    /**
     * Updates the fields of this instance from a COBOL-format byte array
     * @param bytes  byte array formatted as in the {@code WS-COUNT-FIELDS} record; will be space-padded to length if necessary
     * @param offset offset in the byte array where deserialization should begin
     * @see "WS-COUNT-FIELDS record at RXBPA180.cbl:144"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        wsTotalRecsRead = WS_TOTAL_RECS_READ.getInt(bytes, offset);
        wsTotHdrRecCnt = WS_TOT_HDR_REC_CNT.getInt(bytes, offset);
        wsTotDtlRecCnt = WS_TOT_DTL_REC_CNT.getInt(bytes, offset);
        wsTotTrlRecCnt = WS_TOT_TRL_REC_CNT.getInt(bytes, offset);
    }


    /**
     * Updates the fields of this instance from a COBOL-format byte array,
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(byte[] bytes) {
        setBytes(bytes, 0);
    }

    /**
     * Updates the fields of this instance from a COBOL-format string.
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(String bytes) {
        setBytes(bytes.getBytes(encoding));
    }

    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }

}
