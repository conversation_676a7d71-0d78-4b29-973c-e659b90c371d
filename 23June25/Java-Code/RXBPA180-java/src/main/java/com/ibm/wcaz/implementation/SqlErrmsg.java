package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.BinaryAsIntField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class SqlErrmsg implements Cloneable, Comparable<SqlErrmsg> {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private int sqlErrmsgLen = 960;
    private ArrayList<String> sqlErrmsgText = Stream.generate(() -> "").limit(8).collect(Collectors.toCollection(ArrayList::new));
    
    /** Initialize fields to non-null default values */
    public SqlErrmsg() {}
    
    /** Initialize all fields to provided values */
    public SqlErrmsg(int sqlErrmsgLen, ArrayList<String> sqlErrmsgText) {
        this.sqlErrmsgLen = sqlErrmsgLen;
        this.sqlErrmsgText.clear();
        for (int i = 0; i < sqlErrmsgText.size(); i++) {
            this.sqlErrmsgText.add(sqlErrmsgText.get(i));
        }
        for (int i = sqlErrmsgText.size(); i < 8; i++) {
            this.sqlErrmsgText.add("");
        }
    }
    
    @Override
    public SqlErrmsg clone() throws CloneNotSupportedException {
        SqlErrmsg cloned = (SqlErrmsg) super.clone();
        cloned.sqlErrmsgText = new ArrayList<String>(this.sqlErrmsgText);
        return cloned;
    }
    
    /**
     * Initialize {@code SqlErrmsg} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected SqlErrmsg(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code SqlErrmsg} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected SqlErrmsg(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code SqlErrmsg} object
     * @see #setBytes(byte[], int)
     */
    public static SqlErrmsg fromBytes(byte[] bytes, int offset) {
        return new SqlErrmsg(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code SqlErrmsg} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static SqlErrmsg fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code SqlErrmsg} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static SqlErrmsg fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public int getSqlErrmsgLen() {
        return this.sqlErrmsgLen;
    }
    
    public void setSqlErrmsgLen(int sqlErrmsgLen) {
        this.sqlErrmsgLen = sqlErrmsgLen;
    }
    
    public ArrayList<String> getSqlErrmsgText() {
        return this.sqlErrmsgText;
    }
    public String getSqlErrmsgText(int i) {
        return this.sqlErrmsgText.get(i);
    }
    
    public void setSqlErrmsgText(ArrayList<String> sqlErrmsgText) {
        this.sqlErrmsgText = sqlErrmsgText;
    }
    public void setSqlErrmsgText(int i, String sqlErrmsgText) {
        this.sqlErrmsgText.set(i, sqlErrmsgText);
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        sqlErrmsgLen = 0;
        sqlErrmsgText = Stream.generate(() -> "").limit(8).collect(Collectors.toCollection(ArrayList::new));
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder();
        s.append("{ sqlErrmsgLen=\"");
        s.append(getSqlErrmsgLen());
        s.append("\"");
        s.append(", sqlErrmsgText=[\"");
        for (int i=0; i<this.sqlErrmsgText.size(); i++) {
          s.append(this.sqlErrmsgText.get(i));
          if (i < (this.sqlErrmsgText.size()-1)) {
             s.append(", ");
          }
          else {
             s.append("\"]");
          }
        };
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(SqlErrmsg that) {
        return this.sqlErrmsgLen == that.sqlErrmsgLen &&
            this.sqlErrmsgText.equals(that.sqlErrmsgText);
    }
    
    /** Per-field equality comparison; only returns equal if argument is the same class */
    @Override
    public boolean equals(Object that) {
        return (that instanceof SqlErrmsg other) && other.canEqual(this) && this.equals(other);
    }
    
    /** Does {@code that} have the same runtime type as {@code this}? */
    public boolean canEqual(Object that) {
        return that instanceof SqlErrmsg;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * result + Integer.hashCode(sqlErrmsgLen);
        result = 31 * result + Objects.hashCode(sqlErrmsgText);
        return result;
    }
    
    @Override
    public int compareTo(SqlErrmsg that) {
        int c = 0;
        c = Integer.compare(this.sqlErrmsgLen, that.sqlErrmsgLen);
        if ( c != 0 ) return c;
        for (int i = 0; i < this.sqlErrmsgText.size() && i < that.sqlErrmsgText.size(); i++) {
            c = this.sqlErrmsgText.get(i).compareTo(that.sqlErrmsgText.get(i));
            if ( c != 0 ) return c;
        }
        c = Integer.compare(this.sqlErrmsgText.size(), that.sqlErrmsgText.size());
        if ( c == 0 && !(that.canEqual(this) && this.canEqual(that)) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
    }
    
    private static final BinaryAsIntField SQL_ERRMSG_LEN = factory.getBinaryAsIntField(4, true);
    static { factory.pushOffset(); }
    private static final StringField SQL_ERRMSG_TEXT = factory.getStringField(120);
    static { factory.popOffset(); }
    // include extra size from fields skipped in offset
    public static final int SIZE = factory.getOffset() +
        8 * SQL_ERRMSG_TEXT.getByteLength();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code SqlErrmsg} object
     * @param bytes  preallocated byte array to store the object serialization
     * @param offset offset in the byte array where serialization should begin
     * @return the byte array, updated with the newly added data formatted as in the {@code SQL-ERRMSG} record
     * @see "SQL-ERRMSG record at MXWW03.CPY:109"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        SQL_ERRMSG_LEN.putInt(sqlErrmsgLen, bytes, offset);
        for (int i = 0; i < 8; i++) {
            SQL_ERRMSG_TEXT.putString(sqlErrmsgText.get(i), bytes, offset);
            offset += SQL_ERRMSG_TEXT.getByteLength();
        }
        return bytes;
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code SqlErrmsg} object,
     * starting at the beginning of the array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes(byte[] bytes) {
        return getBytes(bytes, 0);
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code SqlErrmsg} object,
     * allocating a new array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes() {
        return getBytes(new byte[numBytes()]);
    }
    
    /**
     * Retrieves a COBOL-format string representation of the {@code SqlErrmsg} object
     * @return the result of {@link #getBytes()} interpreted using the IBM-1047 EBCDIC encoding
     */
    public final String toByteString() {
        return new String(getBytes(), encoding);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array
     * @param bytes  byte array formatted as in the {@code SQL-ERRMSG} record; will be space-padded to length if necessary
     * @param offset offset in the byte array where deserialization should begin
     * @see "SQL-ERRMSG record at MXWW03.CPY:109"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        sqlErrmsgLen = SQL_ERRMSG_LEN.getInt(bytes, offset);
        sqlErrmsgText.clear();
        for (int i = 0; i < 8; i++) {
            sqlErrmsgText.add(SQL_ERRMSG_TEXT.getString(bytes, offset).replaceFirst("\\s+\\z", ""));
            offset += SQL_ERRMSG_TEXT.getByteLength();
        }
    }
    
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array,
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(byte[] bytes) {
        setBytes(bytes, 0);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format string.
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(String bytes) {
        setBytes(bytes.getBytes(encoding));
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
