package com.ibm.wcaz.implementation;

import java.nio.charset.Charset;
import java.util.Arrays;

import com.ibm.jzos.ZFile;
import com.ibm.jzos.ZFileException;
import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;

public class AaaFormatOutRecord implements Cloneable, Comparable<AaaFormatOutRecord> {
    private static final Charset encoding = Charset.forName("IBM-1047");

    /** Initialize fields to non-null default values */
    public AaaFormatOutRecord() {}

    @Override
    public AaaFormatOutRecord clone() throws CloneNotSupportedException {
        AaaFormatOutRecord cloned = (AaaFormatOutRecord) super.clone();
        return cloned;
    }

    /**
     * Initialize {@code AaaFormatOutRecord} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected AaaFormatOutRecord(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }

    /**
     * Initialize {@code AaaFormatOutRecord} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected AaaFormatOutRecord(byte[] bytes) {
        this(bytes, 0);
    }

    /**
     * Deserialize the COBOL-formatted byte array into a new {@code AaaFormatOutRecord} object
     * @see #setBytes(byte[], int)
     */
    public static AaaFormatOutRecord fromBytes(byte[] bytes, int offset) {
        return new AaaFormatOutRecord(bytes, offset);
    }

    /**
     * Deserialize the COBOL-formatted byte array into a new {@code AaaFormatOutRecord} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static AaaFormatOutRecord fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }

    /**
     * Deserialize the COBOL-formatted string into a new {@code AaaFormatOutRecord} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static AaaFormatOutRecord fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }

    public int readFrom(ZFile file) throws ZFileException {
        byte[] bytes = new byte[this.numBytes()];
        int ret = file.read(bytes);
        setBytes(bytes);
        return ret;
    }

    public void writeTo(ZFile file) throws ZFileException {
        file.write(getBytes());
    }

    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
    }

    public String toString() {
        StringBuilder s = new StringBuilder();
        s.append("{ filler19=\"");
        s.append(new String(filler19, encoding));
        s.append("\"");
        s.append("}");
        return s.toString();
    }

    private boolean equals(AaaFormatOutRecord that) {
        return Arrays.equals(this.filler19, that.filler19);
    }

    /** Per-field equality comparison; only returns equal if argument is the same class */
    @Override
    public boolean equals(Object that) {
        return (that instanceof AaaFormatOutRecord other) && other.canEqual(this) && this.equals(other);
    }

    /** Does {@code that} have the same runtime type as {@code this}? */
    public boolean canEqual(Object that) {
        return that instanceof AaaFormatOutRecord;
    }

    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * result + Arrays.hashCode(filler19);
        return result;
    }

    @Override
    public int compareTo(AaaFormatOutRecord that) {
        int c = 0;
        c = Arrays.compare(this.filler19, that.filler19);
        if ( c == 0 && !(that.canEqual(this) && this.canEqual(that)) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }

    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
    }

    private static final ByteArrayField FILLER_19 = factory.getByteArrayField(200);
    private byte[] filler19 = new byte[200];
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata

    /**
     * Retrieves a COBOL-format byte array representation of the {@code AaaFormatOutRecord} object
     * @param bytes  preallocated byte array to store the object serialization
     * @param offset offset in the byte array where serialization should begin
     * @return the byte array, updated with the newly added data formatted as in the {@code AAA-FORMAT-OUT-RECORD} record
     * @see "AAA-FORMAT-OUT-RECORD record at RXBPA180.cbl:42"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        FILLER_19.putByteArray(filler19, bytes, offset);
        return bytes;
    }

    /**
     * Retrieves a COBOL-format byte array representation of the {@code AaaFormatOutRecord} object,
     * starting at the beginning of the array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes(byte[] bytes) {
        return getBytes(bytes, 0);
    }

    /**
     * Retrieves a COBOL-format byte array representation of the {@code AaaFormatOutRecord} object,
     * allocating a new array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes() {
        return getBytes(new byte[numBytes()]);
    }

    /**
     * Retrieves a COBOL-format string representation of the {@code AaaFormatOutRecord} object
     * @return the result of {@link #getBytes()} interpreted using the IBM-1047 EBCDIC encoding
     */
    public final String toByteString() {
        return new String(getBytes(), encoding);
    }

    /**
     * Updates the fields of this instance from a COBOL-format byte array
     * @param bytes  byte array formatted as in the {@code AAA-FORMAT-OUT-RECORD} record; will be space-padded to length if necessary
     * @param offset offset in the byte array where deserialization should begin
     * @see "AAA-FORMAT-OUT-RECORD record at RXBPA180.cbl:42"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        FILLER_19.getByteArray(bytes, offset);
    }


    /**
     * Updates the fields of this instance from a COBOL-format byte array,
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(byte[] bytes) {
        setBytes(bytes, 0);
    }

    /**
     * Updates the fields of this instance from a COBOL-format string.
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(String bytes) {
        setBytes(bytes.getBytes(encoding));
    }

    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }

    public void setFromZRegTrlRec(ZRegTrlRec zRegTrlRec) {
        byte[] src = zRegTrlRec.getBytes();
        System.arraycopy(src, 0, this.filler19, 0, Math.min(src.length, this.filler19.length));
    }

}
