package com.ibm.wcaz.implementation;

import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;

public class WsAaaDate implements Cloneable, Comparable<WsAaaDate> {
    private static final Charset encoding = Charset.forName("IBM-1047");

    private String wsAaaCcyy = "";
    private char wsDtSep1 = '-';
    private String wsAaaMm = "";
    private char wsDtSep2 = '-';
    private String wsAaaDd = "";

    /** Initialize fields to non-null default values */
    public WsAaaDate() {}

    /** Initialize all fields to provided values */
    public WsAaaDate(String wsAaaCcyy, char wsDtSep1, String wsAaaMm, char wsDtSep2, String wsAaaDd) {
        this.wsAaaCcyy = wsAaaCcyy;
        this.wsDtSep1 = wsDtSep1;
        this.wsAaaMm = wsAaaMm;
        this.wsDtSep2 = wsDtSep2;
        this.wsAaaDd = wsAaaDd;
    }

    @Override
    public WsAaaDate clone() throws CloneNotSupportedException {
        WsAaaDate cloned = (WsAaaDate) super.clone();
        return cloned;
    }

    /**
     * Initialize {@code WsAaaDate} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected WsAaaDate(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }

    /**
     * Initialize {@code WsAaaDate} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected WsAaaDate(byte[] bytes) {
        this(bytes, 0);
    }

    /**
     * Deserialize the COBOL-formatted byte array into a new {@code WsAaaDate} object
     * @see #setBytes(byte[], int)
     */
    public static WsAaaDate fromBytes(byte[] bytes, int offset) {
        return new WsAaaDate(bytes, offset);
    }

    /**
     * Deserialize the COBOL-formatted byte array into a new {@code WsAaaDate} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static WsAaaDate fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }

    /**
     * Deserialize the COBOL-formatted string into a new {@code WsAaaDate} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static WsAaaDate fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }

    public String getWsAaaCcyy() {
        return this.wsAaaCcyy;
    }

    public void setWsAaaCcyy(String wsAaaCcyy) {
        this.wsAaaCcyy = wsAaaCcyy;
    }

    public char getWsDtSep1() {
        return this.wsDtSep1;
    }

    public void setWsDtSep1(char wsDtSep1) {
        this.wsDtSep1 = wsDtSep1;
    }

    public String getWsAaaMm() {
        return this.wsAaaMm;
    }

    public void setWsAaaMm(String wsAaaMm) {
        this.wsAaaMm = wsAaaMm;
    }

    public char getWsDtSep2() {
        return this.wsDtSep2;
    }

    public void setWsDtSep2(char wsDtSep2) {
        this.wsDtSep2 = wsDtSep2;
    }

    public String getWsAaaDd() {
        return this.wsAaaDd;
    }

    public void setWsAaaDd(String wsAaaDd) {
        this.wsAaaDd = wsAaaDd;
    }

    public String getWsAaaDate() {
        return this.wsAaaCcyy + this.wsDtSep1 + this.wsAaaMm + this.wsDtSep2 + this.wsAaaDd;
    }

    public void setWsAaaDate(String wsAaaDate) {
        if (wsAaaDate != null && wsAaaDate.length() >= 10) {
            this.wsAaaCcyy = wsAaaDate.substring(0, 4);
            this.wsDtSep1 = wsAaaDate.charAt(4);
            this.wsAaaMm = wsAaaDate.substring(5, 7);
            this.wsDtSep2 = wsAaaDate.charAt(7);
            this.wsAaaDd = wsAaaDate.substring(8, 10);
        }
    }

    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        wsAaaCcyy = "";
        wsDtSep1 = ' ';
        wsAaaMm = "";
        wsDtSep2 = ' ';
        wsAaaDd = "";
    }

    public String toString() {
        StringBuilder s = new StringBuilder();
        s.append("{ wsAaaCcyy=\"");
        s.append(getWsAaaCcyy());
        s.append("\"");
        s.append(", wsDtSep1=\"");
        s.append(getWsDtSep1());
        s.append("\"");
        s.append(", wsAaaMm=\"");
        s.append(getWsAaaMm());
        s.append("\"");
        s.append(", wsDtSep2=\"");
        s.append(getWsDtSep2());
        s.append("\"");
        s.append(", wsAaaDd=\"");
        s.append(getWsAaaDd());
        s.append("\"");
        s.append("}");
        return s.toString();
    }

    private boolean equals(WsAaaDate that) {
        return this.wsAaaCcyy.equals(that.wsAaaCcyy) &&
            this.wsDtSep1 == that.wsDtSep1 &&
            this.wsAaaMm.equals(that.wsAaaMm) &&
            this.wsDtSep2 == that.wsDtSep2 &&
            this.wsAaaDd.equals(that.wsAaaDd);
    }

    /** Per-field equality comparison; only returns equal if argument is the same class */
    @Override
    public boolean equals(Object that) {
        return (that instanceof WsAaaDate other) && other.canEqual(this) && this.equals(other);
    }

    /** Does {@code that} have the same runtime type as {@code this}? */
    public boolean canEqual(Object that) {
        return that instanceof WsAaaDate;
    }

    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * result + Objects.hashCode(wsAaaCcyy);
        result = 31 * result + Character.hashCode(wsDtSep1);
        result = 31 * result + Objects.hashCode(wsAaaMm);
        result = 31 * result + Character.hashCode(wsDtSep2);
        result = 31 * result + Objects.hashCode(wsAaaDd);
        return result;
    }

    @Override
    public int compareTo(WsAaaDate that) {
        int c = 0;
        c = this.wsAaaCcyy.compareTo(that.wsAaaCcyy);
        if ( c != 0 ) return c;
        c = Character.compare(this.wsDtSep1, that.wsDtSep1);
        if ( c != 0 ) return c;
        c = this.wsAaaMm.compareTo(that.wsAaaMm);
        if ( c != 0 ) return c;
        c = Character.compare(this.wsDtSep2, that.wsDtSep2);
        if ( c != 0 ) return c;
        c = this.wsAaaDd.compareTo(that.wsAaaDd);
        if ( c == 0 && !(that.canEqual(this) && this.canEqual(that)) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }

    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
    }

    private static final StringField WS_AAA_CCYY = factory.getStringField(4);
    private static final StringField WS_DT_SEP_1 = factory.getStringField(1);
    private static final StringField WS_AAA_MM = factory.getStringField(2);
    private static final StringField WS_DT_SEP_2 = factory.getStringField(1);
    private static final StringField WS_AAA_DD = factory.getStringField(2);
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata

    /**
     * Retrieves a COBOL-format byte array representation of the {@code WsAaaDate} object
     * @param bytes  preallocated byte array to store the object serialization
     * @param offset offset in the byte array where serialization should begin
     * @return the byte array, updated with the newly added data formatted as in the {@code WS-AAA-DATE} record
     * @see "WS-AAA-DATE record at RXBPA180.cbl:130"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        WS_AAA_CCYY.putString(wsAaaCcyy, bytes, offset);
        WS_DT_SEP_1.putString(Character.toString(wsDtSep1), bytes, offset);
        WS_AAA_MM.putString(wsAaaMm, bytes, offset);
        WS_DT_SEP_2.putString(Character.toString(wsDtSep2), bytes, offset);
        WS_AAA_DD.putString(wsAaaDd, bytes, offset);
        return bytes;
    }

    /**
     * Retrieves a COBOL-format byte array representation of the {@code WsAaaDate} object,
     * starting at the beginning of the array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes(byte[] bytes) {
        return getBytes(bytes, 0);
    }

    /**
     * Retrieves a COBOL-format byte array representation of the {@code WsAaaDate} object,
     * allocating a new array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes() {
        return getBytes(new byte[numBytes()]);
    }

    /**
     * Retrieves a COBOL-format string representation of the {@code WsAaaDate} object
     * @return the result of {@link #getBytes()} interpreted using the IBM-1047 EBCDIC encoding
     */
    public final String toByteString() {
        return new String(getBytes(), encoding);
    }

    /**
     * Updates the fields of this instance from a COBOL-format byte array
     * @param bytes  byte array formatted as in the {@code WS-AAA-DATE} record; will be space-padded to length if necessary
     * @param offset offset in the byte array where deserialization should begin
     * @see "WS-AAA-DATE record at RXBPA180.cbl:130"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        wsAaaCcyy = WS_AAA_CCYY.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        wsDtSep1 = WS_DT_SEP_1.getString(bytes, offset).charAt(0);
        wsAaaMm = WS_AAA_MM.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        wsDtSep2 = WS_DT_SEP_2.getString(bytes, offset).charAt(0);
        wsAaaDd = WS_AAA_DD.getString(bytes, offset).replaceFirst("\\s+\\z", "");
    }


    /**
     * Updates the fields of this instance from a COBOL-format byte array,
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(byte[] bytes) {
        setBytes(bytes, 0);
    }

    /**
     * Updates the fields of this instance from a COBOL-format string.
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(String bytes) {
        setBytes(bytes.getBytes(encoding));
    }

    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }

}
