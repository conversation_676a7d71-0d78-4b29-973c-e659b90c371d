package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.BinaryAsIntField;
import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.PackedDecimalAsIntField;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class Dclvwmcucp implements Cloneable, Comparable<Dclvwmcucp> {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private int custNo;
    private String cpuId = "";
    private String cpuCode = "";
    private String cpuDealerNo = "";
    private int branchNo;
    private int buyGrpNo;
    private int distLocNo;
    private int dlrLocNo;
    private int mfgLocNo;
    private char explodeInd = ' ';
    private String holdCredDate = "";
    private String prodCode = "";
    private char specInvoiceFlag = ' ';
    private String titleCode = "";
    private int modtermCode;
    private int ovrdModtermCode;
    private char serviceFeeType = ' ';
    private int serviceFeeRate;
    private int serviceFeeAmt;
    private char masterDlrFlag = ' ';
    private String lastUpdLogonId = "";
    private String lastUpdDate = "";
    private String auditTs = "";
    private String auditLogonId = "";
    private int auditProcessTxLen;
    private String auditProcessTxText = "";
    private char auditDeleteFl = ' ';
    private char otbAuctionFl = ' ';
    
    /** Initialize fields to non-null default values */
    public Dclvwmcucp() {}
    
    /** Initialize all fields to provided values */
    public Dclvwmcucp(int custNo, String cpuId, String cpuCode, String cpuDealerNo, int branchNo, int buyGrpNo, int distLocNo, int dlrLocNo, int mfgLocNo, char explodeInd, String holdCredDate, String prodCode, char specInvoiceFlag, String titleCode, int modtermCode, int ovrdModtermCode, char serviceFeeType, int serviceFeeRate, int serviceFeeAmt, char masterDlrFlag, String lastUpdLogonId, String lastUpdDate, String auditTs, String auditLogonId, int auditProcessTxLen, String auditProcessTxText, char auditDeleteFl, char otbAuctionFl) {
        this.custNo = custNo;
        this.cpuId = cpuId;
        this.cpuCode = cpuCode;
        this.cpuDealerNo = cpuDealerNo;
        this.branchNo = branchNo;
        this.buyGrpNo = buyGrpNo;
        this.distLocNo = distLocNo;
        this.dlrLocNo = dlrLocNo;
        this.mfgLocNo = mfgLocNo;
        this.explodeInd = explodeInd;
        this.holdCredDate = holdCredDate;
        this.prodCode = prodCode;
        this.specInvoiceFlag = specInvoiceFlag;
        this.titleCode = titleCode;
        this.modtermCode = modtermCode;
        this.ovrdModtermCode = ovrdModtermCode;
        this.serviceFeeType = serviceFeeType;
        this.serviceFeeRate = serviceFeeRate;
        this.serviceFeeAmt = serviceFeeAmt;
        this.masterDlrFlag = masterDlrFlag;
        this.lastUpdLogonId = lastUpdLogonId;
        this.lastUpdDate = lastUpdDate;
        this.auditTs = auditTs;
        this.auditLogonId = auditLogonId;
        this.auditProcessTxLen = auditProcessTxLen;
        this.auditProcessTxText = auditProcessTxText;
        this.auditDeleteFl = auditDeleteFl;
        this.otbAuctionFl = otbAuctionFl;
    }
    
    @Override
    public Dclvwmcucp clone() throws CloneNotSupportedException {
        Dclvwmcucp cloned = (Dclvwmcucp) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code Dclvwmcucp} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected Dclvwmcucp(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code Dclvwmcucp} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected Dclvwmcucp(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code Dclvwmcucp} object
     * @see #setBytes(byte[], int)
     */
    public static Dclvwmcucp fromBytes(byte[] bytes, int offset) {
        return new Dclvwmcucp(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code Dclvwmcucp} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static Dclvwmcucp fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code Dclvwmcucp} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static Dclvwmcucp fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public int getCustNo() {
        return this.custNo;
    }
    
    public void setCustNo(int custNo) {
        this.custNo = custNo;
    }
    
    public String getCpuId() {
        return this.cpuId;
    }
    
    public void setCpuId(String cpuId) {
        this.cpuId = cpuId;
    }
    
    public String getCpuCode() {
        return this.cpuCode;
    }
    
    public void setCpuCode(String cpuCode) {
        this.cpuCode = cpuCode;
    }
    
    public String getCpuDealerNo() {
        return this.cpuDealerNo;
    }
    
    public void setCpuDealerNo(String cpuDealerNo) {
        this.cpuDealerNo = cpuDealerNo;
    }
    
    public int getBranchNo() {
        return this.branchNo;
    }
    
    public void setBranchNo(int branchNo) {
        this.branchNo = branchNo;
    }
    
    public int getBuyGrpNo() {
        return this.buyGrpNo;
    }
    
    public void setBuyGrpNo(int buyGrpNo) {
        this.buyGrpNo = buyGrpNo;
    }
    
    public int getDistLocNo() {
        return this.distLocNo;
    }
    
    public void setDistLocNo(int distLocNo) {
        this.distLocNo = distLocNo;
    }
    
    public int getDlrLocNo() {
        return this.dlrLocNo;
    }
    
    public void setDlrLocNo(int dlrLocNo) {
        this.dlrLocNo = dlrLocNo;
    }
    
    public int getMfgLocNo() {
        return this.mfgLocNo;
    }
    
    public void setMfgLocNo(int mfgLocNo) {
        this.mfgLocNo = mfgLocNo;
    }
    
    public char getExplodeInd() {
        return this.explodeInd;
    }
    
    public void setExplodeInd(char explodeInd) {
        this.explodeInd = explodeInd;
    }
    
    public String getHoldCredDate() {
        return this.holdCredDate;
    }
    
    public void setHoldCredDate(String holdCredDate) {
        this.holdCredDate = holdCredDate;
    }
    
    public String getProdCode() {
        return this.prodCode;
    }
    
    public void setProdCode(String prodCode) {
        this.prodCode = prodCode;
    }
    
    public char getSpecInvoiceFlag() {
        return this.specInvoiceFlag;
    }
    
    public void setSpecInvoiceFlag(char specInvoiceFlag) {
        this.specInvoiceFlag = specInvoiceFlag;
    }
    
    public String getTitleCode() {
        return this.titleCode;
    }
    
    public void setTitleCode(String titleCode) {
        this.titleCode = titleCode;
    }
    
    public int getModtermCode() {
        return this.modtermCode;
    }
    
    public void setModtermCode(int modtermCode) {
        this.modtermCode = modtermCode;
    }
    
    public int getOvrdModtermCode() {
        return this.ovrdModtermCode;
    }
    
    public void setOvrdModtermCode(int ovrdModtermCode) {
        this.ovrdModtermCode = ovrdModtermCode;
    }
    
    public char getServiceFeeType() {
        return this.serviceFeeType;
    }
    
    public void setServiceFeeType(char serviceFeeType) {
        this.serviceFeeType = serviceFeeType;
    }
    
    public int getServiceFeeRate() {
        return this.serviceFeeRate;
    }
    
    public void setServiceFeeRate(int serviceFeeRate) {
        this.serviceFeeRate = serviceFeeRate;
    }
    
    public int getServiceFeeAmt() {
        return this.serviceFeeAmt;
    }
    
    public void setServiceFeeAmt(int serviceFeeAmt) {
        this.serviceFeeAmt = serviceFeeAmt;
    }
    
    public char getMasterDlrFlag() {
        return this.masterDlrFlag;
    }
    
    public void setMasterDlrFlag(char masterDlrFlag) {
        this.masterDlrFlag = masterDlrFlag;
    }
    
    public String getLastUpdLogonId() {
        return this.lastUpdLogonId;
    }
    
    public void setLastUpdLogonId(String lastUpdLogonId) {
        this.lastUpdLogonId = lastUpdLogonId;
    }
    
    public String getLastUpdDate() {
        return this.lastUpdDate;
    }
    
    public void setLastUpdDate(String lastUpdDate) {
        this.lastUpdDate = lastUpdDate;
    }
    
    public String getAuditTs() {
        return this.auditTs;
    }
    
    public void setAuditTs(String auditTs) {
        this.auditTs = auditTs;
    }
    
    public String getAuditLogonId() {
        return this.auditLogonId;
    }
    
    public void setAuditLogonId(String auditLogonId) {
        this.auditLogonId = auditLogonId;
    }
    
    public int getAuditProcessTxLen() {
        return this.auditProcessTxLen;
    }
    
    public void setAuditProcessTxLen(int auditProcessTxLen) {
        this.auditProcessTxLen = auditProcessTxLen;
    }
    
    public String getAuditProcessTxText() {
        return this.auditProcessTxText;
    }
    
    public void setAuditProcessTxText(String auditProcessTxText) {
        this.auditProcessTxText = auditProcessTxText;
    }
    
    public char getAuditDeleteFl() {
        return this.auditDeleteFl;
    }
    
    public void setAuditDeleteFl(char auditDeleteFl) {
        this.auditDeleteFl = auditDeleteFl;
    }
    
    public char getOtbAuctionFl() {
        return this.otbAuctionFl;
    }
    
    public void setOtbAuctionFl(char otbAuctionFl) {
        this.otbAuctionFl = otbAuctionFl;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        custNo = 0;
        cpuId = "";
        cpuCode = "";
        cpuDealerNo = "";
        branchNo = 0;
        buyGrpNo = 0;
        distLocNo = 0;
        dlrLocNo = 0;
        mfgLocNo = 0;
        explodeInd = ' ';
        holdCredDate = "";
        prodCode = "";
        specInvoiceFlag = ' ';
        titleCode = "";
        modtermCode = 0;
        ovrdModtermCode = 0;
        serviceFeeType = ' ';
        serviceFeeRate = 0;
        serviceFeeAmt = 0;
        masterDlrFlag = ' ';
        lastUpdLogonId = "";
        lastUpdDate = "";
        auditTs = "";
        auditLogonId = "";
        auditProcessTxLen = 0;
        auditProcessTxText = "";
        auditDeleteFl = ' ';
        otbAuctionFl = ' ';
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder();
        s.append("{ custNo=\"");
        s.append(getCustNo());
        s.append("\"");
        s.append(", cpuId=\"");
        s.append(getCpuId());
        s.append("\"");
        s.append(", cpuCode=\"");
        s.append(getCpuCode());
        s.append("\"");
        s.append(", cpuDealerNo=\"");
        s.append(getCpuDealerNo());
        s.append("\"");
        s.append(", branchNo=\"");
        s.append(getBranchNo());
        s.append("\"");
        s.append(", buyGrpNo=\"");
        s.append(getBuyGrpNo());
        s.append("\"");
        s.append(", distLocNo=\"");
        s.append(getDistLocNo());
        s.append("\"");
        s.append(", dlrLocNo=\"");
        s.append(getDlrLocNo());
        s.append("\"");
        s.append(", mfgLocNo=\"");
        s.append(getMfgLocNo());
        s.append("\"");
        s.append(", explodeInd=\"");
        s.append(getExplodeInd());
        s.append("\"");
        s.append(", holdCredDate=\"");
        s.append(getHoldCredDate());
        s.append("\"");
        s.append(", prodCode=\"");
        s.append(getProdCode());
        s.append("\"");
        s.append(", specInvoiceFlag=\"");
        s.append(getSpecInvoiceFlag());
        s.append("\"");
        s.append(", titleCode=\"");
        s.append(getTitleCode());
        s.append("\"");
        s.append(", modtermCode=\"");
        s.append(getModtermCode());
        s.append("\"");
        s.append(", ovrdModtermCode=\"");
        s.append(getOvrdModtermCode());
        s.append("\"");
        s.append(", serviceFeeType=\"");
        s.append(getServiceFeeType());
        s.append("\"");
        s.append(", serviceFeeRate=\"");
        s.append(getServiceFeeRate());
        s.append("\"");
        s.append(", serviceFeeAmt=\"");
        s.append(getServiceFeeAmt());
        s.append("\"");
        s.append(", masterDlrFlag=\"");
        s.append(getMasterDlrFlag());
        s.append("\"");
        s.append(", lastUpdLogonId=\"");
        s.append(getLastUpdLogonId());
        s.append("\"");
        s.append(", lastUpdDate=\"");
        s.append(getLastUpdDate());
        s.append("\"");
        s.append(", auditTs=\"");
        s.append(getAuditTs());
        s.append("\"");
        s.append(", auditLogonId=\"");
        s.append(getAuditLogonId());
        s.append("\"");
        s.append(", auditProcessTxLen=\"");
        s.append(getAuditProcessTxLen());
        s.append("\"");
        s.append(", auditProcessTxText=\"");
        s.append(getAuditProcessTxText());
        s.append("\"");
        s.append(", auditDeleteFl=\"");
        s.append(getAuditDeleteFl());
        s.append("\"");
        s.append(", otbAuctionFl=\"");
        s.append(getOtbAuctionFl());
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(Dclvwmcucp that) {
        return this.custNo == that.custNo &&
            this.cpuId.equals(that.cpuId) &&
            this.cpuCode.equals(that.cpuCode) &&
            this.cpuDealerNo.equals(that.cpuDealerNo) &&
            this.branchNo == that.branchNo &&
            this.buyGrpNo == that.buyGrpNo &&
            this.distLocNo == that.distLocNo &&
            this.dlrLocNo == that.dlrLocNo &&
            this.mfgLocNo == that.mfgLocNo &&
            this.explodeInd == that.explodeInd &&
            this.holdCredDate.equals(that.holdCredDate) &&
            this.prodCode.equals(that.prodCode) &&
            this.specInvoiceFlag == that.specInvoiceFlag &&
            this.titleCode.equals(that.titleCode) &&
            this.modtermCode == that.modtermCode &&
            this.ovrdModtermCode == that.ovrdModtermCode &&
            this.serviceFeeType == that.serviceFeeType &&
            this.serviceFeeRate == that.serviceFeeRate &&
            this.serviceFeeAmt == that.serviceFeeAmt &&
            this.masterDlrFlag == that.masterDlrFlag &&
            this.lastUpdLogonId.equals(that.lastUpdLogonId) &&
            this.lastUpdDate.equals(that.lastUpdDate) &&
            this.auditTs.equals(that.auditTs) &&
            this.auditLogonId.equals(that.auditLogonId) &&
            this.auditProcessTxLen == that.auditProcessTxLen &&
            this.auditProcessTxText.equals(that.auditProcessTxText) &&
            this.auditDeleteFl == that.auditDeleteFl &&
            this.otbAuctionFl == that.otbAuctionFl;
    }
    
    /** Per-field equality comparison; only returns equal if argument is the same class */
    @Override
    public boolean equals(Object that) {
        return (that instanceof Dclvwmcucp other) && other.canEqual(this) && this.equals(other);
    }
    
    /** Does {@code that} have the same runtime type as {@code this}? */
    public boolean canEqual(Object that) {
        return that instanceof Dclvwmcucp;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * result + Integer.hashCode(custNo);
        result = 31 * result + Objects.hashCode(cpuId);
        result = 31 * result + Objects.hashCode(cpuCode);
        result = 31 * result + Objects.hashCode(cpuDealerNo);
        result = 31 * result + Integer.hashCode(branchNo);
        result = 31 * result + Integer.hashCode(buyGrpNo);
        result = 31 * result + Integer.hashCode(distLocNo);
        result = 31 * result + Integer.hashCode(dlrLocNo);
        result = 31 * result + Integer.hashCode(mfgLocNo);
        result = 31 * result + Character.hashCode(explodeInd);
        result = 31 * result + Objects.hashCode(holdCredDate);
        result = 31 * result + Objects.hashCode(prodCode);
        result = 31 * result + Character.hashCode(specInvoiceFlag);
        result = 31 * result + Objects.hashCode(titleCode);
        result = 31 * result + Integer.hashCode(modtermCode);
        result = 31 * result + Integer.hashCode(ovrdModtermCode);
        result = 31 * result + Character.hashCode(serviceFeeType);
        result = 31 * result + Integer.hashCode(serviceFeeRate);
        result = 31 * result + Integer.hashCode(serviceFeeAmt);
        result = 31 * result + Character.hashCode(masterDlrFlag);
        result = 31 * result + Objects.hashCode(lastUpdLogonId);
        result = 31 * result + Objects.hashCode(lastUpdDate);
        result = 31 * result + Objects.hashCode(auditTs);
        result = 31 * result + Objects.hashCode(auditLogonId);
        result = 31 * result + Integer.hashCode(auditProcessTxLen);
        result = 31 * result + Objects.hashCode(auditProcessTxText);
        result = 31 * result + Character.hashCode(auditDeleteFl);
        result = 31 * result + Character.hashCode(otbAuctionFl);
        return result;
    }
    
    @Override
    public int compareTo(Dclvwmcucp that) {
        int c = 0;
        c = Integer.compare(this.custNo, that.custNo);
        if ( c != 0 ) return c;
        c = this.cpuId.compareTo(that.cpuId);
        if ( c != 0 ) return c;
        c = this.cpuCode.compareTo(that.cpuCode);
        if ( c != 0 ) return c;
        c = this.cpuDealerNo.compareTo(that.cpuDealerNo);
        if ( c != 0 ) return c;
        c = Integer.compare(this.branchNo, that.branchNo);
        if ( c != 0 ) return c;
        c = Integer.compare(this.buyGrpNo, that.buyGrpNo);
        if ( c != 0 ) return c;
        c = Integer.compare(this.distLocNo, that.distLocNo);
        if ( c != 0 ) return c;
        c = Integer.compare(this.dlrLocNo, that.dlrLocNo);
        if ( c != 0 ) return c;
        c = Integer.compare(this.mfgLocNo, that.mfgLocNo);
        if ( c != 0 ) return c;
        c = Character.compare(this.explodeInd, that.explodeInd);
        if ( c != 0 ) return c;
        c = this.holdCredDate.compareTo(that.holdCredDate);
        if ( c != 0 ) return c;
        c = this.prodCode.compareTo(that.prodCode);
        if ( c != 0 ) return c;
        c = Character.compare(this.specInvoiceFlag, that.specInvoiceFlag);
        if ( c != 0 ) return c;
        c = this.titleCode.compareTo(that.titleCode);
        if ( c != 0 ) return c;
        c = Integer.compare(this.modtermCode, that.modtermCode);
        if ( c != 0 ) return c;
        c = Integer.compare(this.ovrdModtermCode, that.ovrdModtermCode);
        if ( c != 0 ) return c;
        c = Character.compare(this.serviceFeeType, that.serviceFeeType);
        if ( c != 0 ) return c;
        c = Integer.compare(this.serviceFeeRate, that.serviceFeeRate);
        if ( c != 0 ) return c;
        c = Integer.compare(this.serviceFeeAmt, that.serviceFeeAmt);
        if ( c != 0 ) return c;
        c = Character.compare(this.masterDlrFlag, that.masterDlrFlag);
        if ( c != 0 ) return c;
        c = this.lastUpdLogonId.compareTo(that.lastUpdLogonId);
        if ( c != 0 ) return c;
        c = this.lastUpdDate.compareTo(that.lastUpdDate);
        if ( c != 0 ) return c;
        c = this.auditTs.compareTo(that.auditTs);
        if ( c != 0 ) return c;
        c = this.auditLogonId.compareTo(that.auditLogonId);
        if ( c != 0 ) return c;
        c = Integer.compare(this.auditProcessTxLen, that.auditProcessTxLen);
        if ( c != 0 ) return c;
        c = this.auditProcessTxText.compareTo(that.auditProcessTxText);
        if ( c != 0 ) return c;
        c = Character.compare(this.auditDeleteFl, that.auditDeleteFl);
        if ( c != 0 ) return c;
        c = Character.compare(this.otbAuctionFl, that.otbAuctionFl);
        if ( c == 0 && !(that.canEqual(this) && this.canEqual(that)) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
    }
    
    private static final BinaryAsIntField CUST_NO = factory.getBinaryAsIntField(9, true);
    private static final StringField CPU_ID = factory.getStringField(4);
    private static final StringField CPU_CODE = factory.getStringField(4);
    private static final StringField CPU_DEALER_NO = factory.getStringField(13);
    private static final BinaryAsIntField BRANCH_NO = factory.getBinaryAsIntField(4, true);
    private static final BinaryAsIntField BUY_GRP_NO = factory.getBinaryAsIntField(9, true);
    private static final BinaryAsIntField DIST_LOC_NO = factory.getBinaryAsIntField(4, true);
    private static final BinaryAsIntField DLR_LOC_NO = factory.getBinaryAsIntField(4, true);
    private static final BinaryAsIntField MFG_LOC_NO = factory.getBinaryAsIntField(4, true);
    private static final StringField EXPLODE_IND = factory.getStringField(1);
    private static final StringField HOLD_CRED_DATE = factory.getStringField(10);
    private static final StringField PROD_CODE = factory.getStringField(4);
    private static final StringField SPEC_INVOICE_FLAG = factory.getStringField(1);
    private static final StringField TITLE_CODE = factory.getStringField(2);
    private static final BinaryAsIntField MODTERM_CODE = factory.getBinaryAsIntField(4, true);
    private static final BinaryAsIntField OVRD_MODTERM_CODE = factory.getBinaryAsIntField(4, true);
    private static final StringField SERVICE_FEE_TYPE = factory.getStringField(1);
    private static final PackedDecimalAsIntField SERVICE_FEE_RATE = factory.getPackedDecimalAsIntField(7, true);
    private static final PackedDecimalAsIntField SERVICE_FEE_AMT = factory.getPackedDecimalAsIntField(7, true);
    private static final StringField MASTER_DLR_FLAG = factory.getStringField(1);
    private static final StringField LAST_UPD_LOGON_ID = factory.getStringField(8);
    private static final StringField LAST_UPD_DATE = factory.getStringField(10);
    private static final StringField AUDIT_TS = factory.getStringField(26);
    private static final StringField AUDIT_LOGON_ID = factory.getStringField(8);
    private static final BinaryAsIntField AUDIT_PROCESS_TX_LEN = factory.getBinaryAsIntField(4, true);
    private static final StringField AUDIT_PROCESS_TX_TEXT = factory.getStringField(30);
    private static final StringField AUDIT_DELETE_FL = factory.getStringField(1);
    private static final StringField OTB_AUCTION_FL = factory.getStringField(1);
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code Dclvwmcucp} object
     * @param bytes  preallocated byte array to store the object serialization
     * @param offset offset in the byte array where serialization should begin
     * @return the byte array, updated with the newly added data formatted as in the {@code DCLVWMCUCP} record
     * @see "DCLVWMCUCP record at VWMCUCP.CPY:40"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        CUST_NO.putInt(custNo, bytes, offset);
        CPU_ID.putString(cpuId, bytes, offset);
        CPU_CODE.putString(cpuCode, bytes, offset);
        CPU_DEALER_NO.putString(cpuDealerNo, bytes, offset);
        BRANCH_NO.putInt(branchNo, bytes, offset);
        BUY_GRP_NO.putInt(buyGrpNo, bytes, offset);
        DIST_LOC_NO.putInt(distLocNo, bytes, offset);
        DLR_LOC_NO.putInt(dlrLocNo, bytes, offset);
        MFG_LOC_NO.putInt(mfgLocNo, bytes, offset);
        EXPLODE_IND.putString(Character.toString(explodeInd), bytes, offset);
        HOLD_CRED_DATE.putString(holdCredDate, bytes, offset);
        PROD_CODE.putString(prodCode, bytes, offset);
        SPEC_INVOICE_FLAG.putString(Character.toString(specInvoiceFlag), bytes, offset);
        TITLE_CODE.putString(titleCode, bytes, offset);
        MODTERM_CODE.putInt(modtermCode, bytes, offset);
        OVRD_MODTERM_CODE.putInt(ovrdModtermCode, bytes, offset);
        SERVICE_FEE_TYPE.putString(Character.toString(serviceFeeType), bytes, offset);
        SERVICE_FEE_RATE.putInt(serviceFeeRate, bytes, offset);
        SERVICE_FEE_AMT.putInt(serviceFeeAmt, bytes, offset);
        MASTER_DLR_FLAG.putString(Character.toString(masterDlrFlag), bytes, offset);
        LAST_UPD_LOGON_ID.putString(lastUpdLogonId, bytes, offset);
        LAST_UPD_DATE.putString(lastUpdDate, bytes, offset);
        AUDIT_TS.putString(auditTs, bytes, offset);
        AUDIT_LOGON_ID.putString(auditLogonId, bytes, offset);
        AUDIT_PROCESS_TX_LEN.putInt(auditProcessTxLen, bytes, offset);
        AUDIT_PROCESS_TX_TEXT.putString(auditProcessTxText, bytes, offset);
        AUDIT_DELETE_FL.putString(Character.toString(auditDeleteFl), bytes, offset);
        OTB_AUCTION_FL.putString(Character.toString(otbAuctionFl), bytes, offset);
        return bytes;
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code Dclvwmcucp} object,
     * starting at the beginning of the array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes(byte[] bytes) {
        return getBytes(bytes, 0);
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code Dclvwmcucp} object,
     * allocating a new array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes() {
        return getBytes(new byte[numBytes()]);
    }
    
    /**
     * Retrieves a COBOL-format string representation of the {@code Dclvwmcucp} object
     * @return the result of {@link #getBytes()} interpreted using the IBM-1047 EBCDIC encoding
     */
    public final String toByteString() {
        return new String(getBytes(), encoding);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array
     * @param bytes  byte array formatted as in the {@code DCLVWMCUCP} record; will be space-padded to length if necessary
     * @param offset offset in the byte array where deserialization should begin
     * @see "DCLVWMCUCP record at VWMCUCP.CPY:40"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        custNo = CUST_NO.getInt(bytes, offset);
        cpuId = CPU_ID.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        cpuCode = CPU_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        cpuDealerNo = CPU_DEALER_NO.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        branchNo = BRANCH_NO.getInt(bytes, offset);
        buyGrpNo = BUY_GRP_NO.getInt(bytes, offset);
        distLocNo = DIST_LOC_NO.getInt(bytes, offset);
        dlrLocNo = DLR_LOC_NO.getInt(bytes, offset);
        mfgLocNo = MFG_LOC_NO.getInt(bytes, offset);
        explodeInd = EXPLODE_IND.getString(bytes, offset).charAt(0);
        holdCredDate = HOLD_CRED_DATE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        prodCode = PROD_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        specInvoiceFlag = SPEC_INVOICE_FLAG.getString(bytes, offset).charAt(0);
        titleCode = TITLE_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        modtermCode = MODTERM_CODE.getInt(bytes, offset);
        ovrdModtermCode = OVRD_MODTERM_CODE.getInt(bytes, offset);
        serviceFeeType = SERVICE_FEE_TYPE.getString(bytes, offset).charAt(0);
        serviceFeeRate = SERVICE_FEE_RATE.getInt(bytes, offset);
        serviceFeeAmt = SERVICE_FEE_AMT.getInt(bytes, offset);
        masterDlrFlag = MASTER_DLR_FLAG.getString(bytes, offset).charAt(0);
        lastUpdLogonId = LAST_UPD_LOGON_ID.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        lastUpdDate = LAST_UPD_DATE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        auditTs = AUDIT_TS.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        auditLogonId = AUDIT_LOGON_ID.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        auditProcessTxLen = AUDIT_PROCESS_TX_LEN.getInt(bytes, offset);
        auditProcessTxText = AUDIT_PROCESS_TX_TEXT.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        auditDeleteFl = AUDIT_DELETE_FL.getString(bytes, offset).charAt(0);
        otbAuctionFl = OTB_AUCTION_FL.getString(bytes, offset).charAt(0);
    }
    
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array,
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(byte[] bytes) {
        setBytes(bytes, 0);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format string.
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(String bytes) {
        setBytes(bytes.getBytes(encoding));
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
