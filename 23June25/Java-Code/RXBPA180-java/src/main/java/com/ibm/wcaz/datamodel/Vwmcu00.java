package com.ibm.wcaz.datamodel;

import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.ExternalDecimalAsIntField;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.sql.Date;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Objects;

public class Vwmcu00 implements Cloneable, Comparable<Vwmcu00> {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private int custNo;
    private String custTypeCode = "";
    private String custStatusCode = "";
    private int affilCustNo;
    private int cntlEntNo;
    private String billAcctRepCode = "";
    private String busCode = "";
    private int collBranchNo;
    private int collDelayCnt;
    private String rate360Flag = "";
    private String advPayFlag = "";
    private String recourseFlag = "";
    private int shipAuthAmt;
    private String billCustFlag = "";
    private String slCycleNo = "";
    private String shipAuthFlag = "";
    private Date finanStmtDate = new Date(0);
    private String dunnSicCode = "";
    private String dunnNo = "";
    private int busEstabYearNo;
    private int commBeginDay1No;
    private int commEndDay1No;
    private int commDueDay1No;
    private int commBeginDay2No;
    private int commEndDay2No;
    private int commDueDay2No;
    private int commBeginDay3No;
    private int commEndDay3No;
    private int commDueDay3No;
    private int commBeginDay4No;
    private int commEndDay4No;
    private int commDueDay4No;
    private String repurFlag = "";
    private String stripPassFlag = "";
    private int revFreqCode;
    private String trustPrtformCode = "";
    private String trustPrtCode = "";
    private int taxIdUpdCnt;
    private String taxIdNo = "";
    private Date intCredExpDate = new Date(0);
    private int tcfcStartYearNo;
    private String apprTypeCode = "";
    private String billConsFlag = "";
    private String billSubDistFlag = "";
    private Date lastRevDate = new Date(0);
    private String insFlag = "";
    private int coverageInsAmt;
    private Date policyExpDate = new Date(0);
    private int ovrdInsRate;
    private int insRateCode;
    private Date sotUpdDate = new Date(0);
    private Date repoUpdDate = new Date(0);
    private int repoAmt;
    private int sotBalDueAmt;
    private int reserveFund2Amt;
    private int reserveFund1Amt;
    private int inctPayBranchNo;
    private String inctBasedOnCode = "";
    private String inctPayFreqCode = "";
    private Date inctPaidDate = new Date(0);
    private int billAddrNo;
    private int payAddrNo;
    private String phoneNo = "";
    private String faxNo = "";
    private int fcBranchNo;
    private int fcCycleNo;
    private String fcSortSeqCode = "";
    private String fcTypeCode = "";
    private Date fcLastDate = new Date(0);
    private String fcLastInitials = "";
    private String estLossInitials = "";
    private int estLossAmt;
    private int distMfgOsAmt;
    private String uccStFilingNo = "";
    private Date uccStExpDate = new Date(0);
    private String uccCntyFilingNo = "";
    private Date uccCntyExpDate = new Date(0);
    private String initChrgRptFlag = "";
    private String collDelayInd = "";
    private int slGraceDaysCnt;
    private int credScoreNo;
    private String industryCode = "";
    private String dlrRepCode = "";
    private Date bosAuditDate = new Date(0);
    private int bosCycleNo;
    private Date bankAuditDate = new Date(0);
    private int bankCycleNo;
    private int adminFlatAmt;
    private int adminRate;
    private int adminMinOsAmt;
    private int lossReserveRate;
    private int lossReserveAmt;
    private int cntlBranchNo;
    private String fsrRepCode = "";
    private String lastFsrRepCode = "";
    private String titleCode = "";
    private String shipCodeReqFlag = "";
    private int plbBranchNo;
    private int plbPayAddrNo;
    private int annualSalesAmt;
    private int cogsAmt;
    private String bankName = "";
    private String leadSourceCode = "";
    private int leadCustNo;
    private String projCode = "";
    private Date salesCogsDate = new Date(0);
    private String crRvReqstInd = "";
    private Date crRvReqstDate = new Date(0);
    private String crRvDlrRepCode = "";
    private int estCredLnAmt;
    private String finSource1Code = "";
    private String finSource2Code = "";
    private String finSource3Code = "";
    private String finSource4Code = "";
    private String computSystemInd = "";
    private String cntlInvnFlag = "";
    private String cntlAcctrecvFlag = "";
    private String cntlAcctPblFlag = "";
    private String modemFlag = "";
    private Date scfcRecvdDate = new Date(0);
    private int seasonalCode;
    private int fcJanCnt;
    private int fcFebCnt;
    private int fcMarCnt;
    private int fcAprCnt;
    private int fcMayCnt;
    private int fcJunCnt;
    private int fcJulCnt;
    private int fcAugCnt;
    private int fcSepCnt;
    private int fcOctCnt;
    private int fcNovCnt;
    private int fcDecCnt;
    private Date fcTypeEffDate = new Date(0);
    private String fcTypeLogonId = "";
    private int fcReqCnt;
    private int lastYearFcCnt;
    private String scfcPrtInd = "";
    private String scfcInclSlFlag = "";
    private Date nextSchedFcDate = new Date(0);
    private String prtAllTrustFlag = "";
    private Date createDate = new Date(0);
    private Date fcFreqEffDt = new Date(0);
    private String fcFreqLogonId = "";
    private int equityCycNo;
    private Date equityDate = new Date(0);
    private int invnCycNo;
    private Date invnDate = new Date(0);
    private int recclAmt;
    private String scfcTrkNonFlag = "";
    private Date scfcSent01Date = new Date(0);
    private Date scfcSent02Date = new Date(0);
    private String pblFundDayInd = "";
    private String pcsPurgeFlag = "";
    private String ovlnTolerFlag = "";
    private int apprPurgeMinAmt;
    private int apprPurgeMinPct;
    private int apprPurgeDayCnt;
    private Date credHoldDate = new Date(0);
    private String ovrdFundDateInd = "";
    private String noTrustDtlFlag = "";
    private String purchContactName = "";
    private String purchPhoneNo = "";
    private String purchFaxNo = "";
    private String adminLogonId = "";
    private String serialNoFlag = "";
    private Date purchUpdDate = new Date(0);
    private String purchUpdLogonId = "";
    private String explodeInd = "";
    private String filingTypeCode = "";
    private int epdDelayDaysCnt;
    private String salesZipCode = "";
    private Date finStEntryDate = new Date(0);
    private Date trstSpecPrtDate = new Date(0);
    private String trstSpecPrtInd = "";
    private String credPrtFlag = "";
    private String credPrtFreqCode = "";
    private String stProvCode = "";
    private String zipPostalCode = "";
    private String countryCode = "";
    private String addr1Name = "";
    private String addr2Name = "";
    private String cityName = "";
    private String countyName = "";
    private String legalName = "";
    private String dbaName = "";
    private String contactName = "";
    private String contactTitle = "";
    private String locDesc = "";
    private int cashPriorityNo;
    private String xcSourceFlag = "";
    private String xcEligibleFlag = "";
    private String xcCredStatCode = "";
    private String slWeekdayFlag = "";
    private String prtCredMemoInd = "";
    private String cmApplyInd = "";
    private String cmFundFreqCode = "";
    private String cmFundDayCode = "";
    private String prtCurrencyFlag = "";
    private String attrnOvrdFlag = "";
    private Date attrnExpDate = new Date(0);
    private String shortName = "";
    private String trstSpecPrtCode = "";
    private String emailAddrName = "";
    private String pdeSortSeqCode = "";
    private String mstrInvPrtCode = "";
    private String mstrInvReqInd = "";
    private String trustEdiCode = "";
    private String vatRgstrNo = "";
    private String vatElectionInd = "";
    private String languageCode = "";
    private String holdNegPblInd = "";
    private String faxStubsFlag = "";
    private String polCredMemoFlag = "";
    private int epdGraceDaysCnt;
    private String epdFundFreqCode = "";
    private int epdFundDaysCnt;
    private String credClaimFlag = "";
    private int credClaimDayCnt;
    private int rmbCmDiscPgmNo;
    private String skuIdFlag = "";
    private String prtCredNoteInd = "";
    private String uccOrgNo = "";
    private String uccOrgSt = "";
    private String systemCreateFlag = "";
    private String currencyCode = "";
    private String autoCashFlag = "";
    private String incomplCashFlag = "";
    private int podPresDayCnt;
    private String podPresDateFlag = "";
    private String trueUpEarlyFlag = "";
    private String trueUpLateFlag = "";
    private int tdfRiskPct;
    private int riskFeeRate;
    private String riskFeeFiuFlag = "";
    private String portTypeCode = "";
    private String servTrustFlag = "";
    private int lossReserve2Rate;
    private int grossupPct;
    private String adbDiscInd = "";
    private int distFundOsAmt;
    private String dlrChrgTdfFlag = "";
    private String miscId = "";
    private String servFeeFreqInd = "";
    private int collFeeRate;
    private int fundHoldPct;
    private int fundLimitAmt;
    private int servFeeBranchNo;
    private String trueUpIntFlag = "";
    private int curtGraceDayCnt;
    private int anrMonthsCnt;
    private Date mstrAgreeDate = new Date(0);
    private String cmApplyRuleCode = "";
    private String cmApplyExcpCode = "";
    private String scrtzEligCd = "";
    private String securedFl = "";
    private Date scrtzEffDate = new Date(0);
    private int scrtzParticRt;
    private String scrtzPoolId = "";
    private String adbBillMethCode = "";
    private String lateFeeBillInd = "";
    private String edocsInd = "";
    private Date edocsStartDate = new Date(0);
    private Date edocsPrtEndDate = new Date(0);
    private int auxCarrNo;
    private String curtPrepaidFlag = "";
    private String stmtSortCode = "";
    private String stmtPageBrkFlag = "";
    private String recvCmPrtFlag = "";
    private String slStmtPrtFl = "";
    private int epdCbgnDay1No;
    private int epdCbgnDay2No;
    private int epdCbgnDay3No;
    private int epdCbgnDay4No;
    private int epdCdueDay1No;
    private int epdCdueDay2No;
    private int epdCdueDay3No;
    private int epdCdueDay4No;
    private int epdCendDay1No;
    private int epdCendDay2No;
    private int epdCendDay3No;
    private int epdCendDay4No;
    private String refundDistFdFl = "";
    private int cashAlgCode;
    private String autoStlmntFl = "";
    private String tranStmtFrmtCd = "";
    private String defNoPayFlag = "";
    private String defComposRtFlag = "";
    private int defBankCode;
    private String defTypeInd = "";
    private int defMinprimeRate;
    private int defMaxprimeRate;
    private int straightRate;
    private String prtNoteStmtInd = "";
    private String segmentCd = "";
    private String highRiskFl = "";
    private String kmvId = "";
    private String extIdTypeCd = "";
    private String extIdNo = "";
    private String ofstTypeCd = "";
    private String lineDetailFl = "";
    private String fixpSkipMo01Fl = "";
    private String fixpSkipMo02Fl = "";
    private String fixpSkipMo03Fl = "";
    private String fixpSkipMo04Fl = "";
    private String fixpSkipMo05Fl = "";
    private String fixpSkipMo06Fl = "";
    private String fixpSkipMo07Fl = "";
    private String fixpSkipMo08Fl = "";
    private String fixpSkipMo09Fl = "";
    private String fixpSkipMo10Fl = "";
    private String fixpSkipMo11Fl = "";
    private String fixpSkipMo12Fl = "";
    private String distPrchEmailNm = "";
    private String rstrFl = "";
    private String starFl = "";
    private String bnkrpFl = "";
    private String regSweepFl = "";
    private String putReasonCd = "";
    private Date putReasonDt = new Date(0);
    private String bankWatchFl = "";
    private int ovlnTolerRt;
    private String otbHeldPayFl = "";
    private String otbUnapplCashFl = "";
    private String otbUnidCashFl = "";
    private String otbDefFl = "";
    private int pcsDefApprAmt;
    private String otbExpoExclInd = "";
    private Date finanStmtInrmDt = new Date(0);
    private int finanStmtInrmCt;
    private Date finanStmtExtnDt = new Date(0);
    private String publicPrivInd = "";
    private Date lastRevExtnDt = new Date(0);
    private String finanStmtQualCd = "";
    private String endUserNm = "";
    private String mraCustId = "";
    private String defOfstPrincFl = "";
    private String defActivPageFl = "";
    private String undwrGdlnExcpFl = "";
    private String dlrPreauthFl = "";
    private String preauthAllowFl = "";
    private String ediStmtTypeCd = "";
    private String ediMfgDistFlag = "";
    private String epdCmFl = "";
    private String mmtsCmprsFrmtFl = "";
    private String prpCrtWavRprFl = "";
    private int comsBigBufferCt;
    private String dlvVrfctnFl = "";
    private String rcpCrtTaxInvFl = "";
    private String overseasDistFl = "";
    private String mobileNo = "";
    private String servLvlCatCd = "";
    private String partyId = "";
    private int touchlessSyncId;
    private Date priorChrgOffDt = new Date(0);
    private int totChrgOffAmt;
    private String ddOvrdSweepFl = "";
    private String prevScrtzPoolId = "";
    private Date lastSpecRevDate = new Date(0);
    private String loanCommitFl = "";
    private Date loanCommitRenDt = new Date(0);
    private String ckDeliveryCd = "";
    private int wcisId;
    private String naicsCd = "";
    private String custNoteTx = "";
    private String naceCd = "";
    private String collatSegmentDs = "";
    private Date auditTs = new Date(0);
    private String auditLogonId = "";
    private String auditProcessTx = "";
    private String auditDeleteFl = "";
    private int netRelianceAmt;
    private Date lastAmlRevDt = new Date(0);
    private String ecrrCd = "";
    private String craCd = "";
    private Date craEffDt = new Date(0);
    private Date craEndDt = new Date(0);
    private int hardCredLnAmt;
    private String workoutFl = "";
    private String workoutResolveCd = "";
    private Date origEffDt = new Date(0);
    private Date acquireDt = new Date(0);
    private int rooftopCnt;
    private String cmrclDsclrFl = "";
    private Date distAgreeDt = new Date(0);
    private String wfbnaDocFl = "";
    private String custDescTx = "";
    private Date currEffDt = new Date(0);
    private String retailStlmntFl = "";
    private String governLawCd = "";
    private int totalEmplCnt;
    private int totalAssetAmt;
    private Date agreeRestateDt = new Date(0);
    private Date totalEmplDt = new Date(0);
    private Date totalAssetDt = new Date(0);
    private Date annualSalesDt = new Date(0);
    private int hardClPydownAmt;
    private int wcisCupId;
    private int wcisSupId;
    private int hardTempClAmt;
    private int hardTempPdwnAmt;
    private String stockExchMicId = "";
    private String stockTickerId = "";
    private String riskCountryCd = "";
    private int totRecovAmt;
    private String taxIdTypeCd = "";
    private Date bnkrpDt = new Date(0);
    private String apiHApprUpdFl = "";
    
    /** Initialize fields to non-null default values */
    public Vwmcu00() {}
    
    /** Initialize all fields to provided values */
    public Vwmcu00(int custNo, String custTypeCode, String custStatusCode, int affilCustNo, int cntlEntNo, String billAcctRepCode, String busCode, int collBranchNo, int collDelayCnt, String rate360Flag, String advPayFlag, String recourseFlag, int shipAuthAmt, String billCustFlag, String slCycleNo, String shipAuthFlag, Date finanStmtDate, String dunnSicCode, String dunnNo, int busEstabYearNo, int commBeginDay1No, int commEndDay1No, int commDueDay1No, int commBeginDay2No, int commEndDay2No, int commDueDay2No, int commBeginDay3No, int commEndDay3No, int commDueDay3No, int commBeginDay4No, int commEndDay4No, int commDueDay4No, String repurFlag, String stripPassFlag, int revFreqCode, String trustPrtformCode, String trustPrtCode, int taxIdUpdCnt, String taxIdNo, Date intCredExpDate, int tcfcStartYearNo, String apprTypeCode, String billConsFlag, String billSubDistFlag, Date lastRevDate, String insFlag, int coverageInsAmt, Date policyExpDate, int ovrdInsRate, int insRateCode, Date sotUpdDate, Date repoUpdDate, int repoAmt, int sotBalDueAmt, int reserveFund2Amt, int reserveFund1Amt, int inctPayBranchNo, String inctBasedOnCode, String inctPayFreqCode, Date inctPaidDate, int billAddrNo, int payAddrNo, String phoneNo, String faxNo, int fcBranchNo, int fcCycleNo, String fcSortSeqCode, String fcTypeCode, Date fcLastDate, String fcLastInitials, String estLossInitials, int estLossAmt, int distMfgOsAmt, String uccStFilingNo, Date uccStExpDate, String uccCntyFilingNo, Date uccCntyExpDate, String initChrgRptFlag, String collDelayInd, int slGraceDaysCnt, int credScoreNo, String industryCode, String dlrRepCode, Date bosAuditDate, int bosCycleNo, Date bankAuditDate, int bankCycleNo, int adminFlatAmt, int adminRate, int adminMinOsAmt, int lossReserveRate, int lossReserveAmt, int cntlBranchNo, String fsrRepCode, String lastFsrRepCode, String titleCode, String shipCodeReqFlag, int plbBranchNo, int plbPayAddrNo, int annualSalesAmt, int cogsAmt, String bankName, String leadSourceCode, int leadCustNo, String projCode, Date salesCogsDate, String crRvReqstInd, Date crRvReqstDate, String crRvDlrRepCode, int estCredLnAmt, String finSource1Code, String finSource2Code, String finSource3Code, String finSource4Code, String computSystemInd, String cntlInvnFlag, String cntlAcctrecvFlag, String cntlAcctPblFlag, String modemFlag, Date scfcRecvdDate, int seasonalCode, int fcJanCnt, int fcFebCnt, int fcMarCnt, int fcAprCnt, int fcMayCnt, int fcJunCnt, int fcJulCnt, int fcAugCnt, int fcSepCnt, int fcOctCnt, int fcNovCnt, int fcDecCnt, Date fcTypeEffDate, String fcTypeLogonId, int fcReqCnt, int lastYearFcCnt, String scfcPrtInd, String scfcInclSlFlag, Date nextSchedFcDate, String prtAllTrustFlag, Date createDate, Date fcFreqEffDt, String fcFreqLogonId, int equityCycNo, Date equityDate, int invnCycNo, Date invnDate, int recclAmt, String scfcTrkNonFlag, Date scfcSent01Date, Date scfcSent02Date, String pblFundDayInd, String pcsPurgeFlag, String ovlnTolerFlag, int apprPurgeMinAmt, int apprPurgeMinPct, int apprPurgeDayCnt, Date credHoldDate, String ovrdFundDateInd, String noTrustDtlFlag, String purchContactName, String purchPhoneNo, String purchFaxNo, String adminLogonId, String serialNoFlag, Date purchUpdDate, String purchUpdLogonId, String explodeInd, String filingTypeCode, int epdDelayDaysCnt, String salesZipCode, Date finStEntryDate, Date trstSpecPrtDate, String trstSpecPrtInd, String credPrtFlag, String credPrtFreqCode, String stProvCode, String zipPostalCode, String countryCode, String addr1Name, String addr2Name, String cityName, String countyName, String legalName, String dbaName, String contactName, String contactTitle, String locDesc, int cashPriorityNo, String xcSourceFlag, String xcEligibleFlag, String xcCredStatCode, String slWeekdayFlag, String prtCredMemoInd, String cmApplyInd, String cmFundFreqCode, String cmFundDayCode, String prtCurrencyFlag, String attrnOvrdFlag, Date attrnExpDate, String shortName, String trstSpecPrtCode, String emailAddrName, String pdeSortSeqCode, String mstrInvPrtCode, String mstrInvReqInd, String trustEdiCode, String vatRgstrNo, String vatElectionInd, String languageCode, String holdNegPblInd, String faxStubsFlag, String polCredMemoFlag, int epdGraceDaysCnt, String epdFundFreqCode, int epdFundDaysCnt, String credClaimFlag, int credClaimDayCnt, int rmbCmDiscPgmNo, String skuIdFlag, String prtCredNoteInd, String uccOrgNo, String uccOrgSt, String systemCreateFlag, String currencyCode, String autoCashFlag, String incomplCashFlag, int podPresDayCnt, String podPresDateFlag, String trueUpEarlyFlag, String trueUpLateFlag, int tdfRiskPct, int riskFeeRate, String riskFeeFiuFlag, String portTypeCode, String servTrustFlag, int lossReserve2Rate, int grossupPct, String adbDiscInd, int distFundOsAmt, String dlrChrgTdfFlag, String miscId, String servFeeFreqInd, int collFeeRate, int fundHoldPct, int fundLimitAmt, int servFeeBranchNo, String trueUpIntFlag, int curtGraceDayCnt, int anrMonthsCnt, Date mstrAgreeDate, String cmApplyRuleCode, String cmApplyExcpCode, String scrtzEligCd, String securedFl, Date scrtzEffDate, int scrtzParticRt, String scrtzPoolId, String adbBillMethCode, String lateFeeBillInd, String edocsInd, Date edocsStartDate, Date edocsPrtEndDate, int auxCarrNo, String curtPrepaidFlag, String stmtSortCode, String stmtPageBrkFlag, String recvCmPrtFlag, String slStmtPrtFl, int epdCbgnDay1No, int epdCbgnDay2No, int epdCbgnDay3No, int epdCbgnDay4No, int epdCdueDay1No, int epdCdueDay2No, int epdCdueDay3No, int epdCdueDay4No, int epdCendDay1No, int epdCendDay2No, int epdCendDay3No, int epdCendDay4No, String refundDistFdFl, int cashAlgCode, String autoStlmntFl, String tranStmtFrmtCd, String defNoPayFlag, String defComposRtFlag, int defBankCode, String defTypeInd, int defMinprimeRate, int defMaxprimeRate, int straightRate, String prtNoteStmtInd, String segmentCd, String highRiskFl, String kmvId, String extIdTypeCd, String extIdNo, String ofstTypeCd, String lineDetailFl, String fixpSkipMo01Fl, String fixpSkipMo02Fl, String fixpSkipMo03Fl, String fixpSkipMo04Fl, String fixpSkipMo05Fl, String fixpSkipMo06Fl, String fixpSkipMo07Fl, String fixpSkipMo08Fl, String fixpSkipMo09Fl, String fixpSkipMo10Fl, String fixpSkipMo11Fl, String fixpSkipMo12Fl, String distPrchEmailNm, String rstrFl, String starFl, String bnkrpFl, String regSweepFl, String putReasonCd, Date putReasonDt, String bankWatchFl, int ovlnTolerRt, String otbHeldPayFl, String otbUnapplCashFl, String otbUnidCashFl, String otbDefFl, int pcsDefApprAmt, String otbExpoExclInd, Date finanStmtInrmDt, int finanStmtInrmCt, Date finanStmtExtnDt, String publicPrivInd, Date lastRevExtnDt, String finanStmtQualCd, String endUserNm, String mraCustId, String defOfstPrincFl, String defActivPageFl, String undwrGdlnExcpFl, String dlrPreauthFl, String preauthAllowFl, String ediStmtTypeCd, String ediMfgDistFlag, String epdCmFl, String mmtsCmprsFrmtFl, String prpCrtWavRprFl, int comsBigBufferCt, String dlvVrfctnFl, String rcpCrtTaxInvFl, String overseasDistFl, String mobileNo, String servLvlCatCd, String partyId, int touchlessSyncId, Date priorChrgOffDt, int totChrgOffAmt, String ddOvrdSweepFl, String prevScrtzPoolId, Date lastSpecRevDate, String loanCommitFl, Date loanCommitRenDt, String ckDeliveryCd, int wcisId, String naicsCd, String custNoteTx, String naceCd, String collatSegmentDs, Date auditTs, String auditLogonId, String auditProcessTx, String auditDeleteFl, int netRelianceAmt, Date lastAmlRevDt, String ecrrCd, String craCd, Date craEffDt, Date craEndDt, int hardCredLnAmt, String workoutFl, String workoutResolveCd, Date origEffDt, Date acquireDt, int rooftopCnt, String cmrclDsclrFl, Date distAgreeDt, String wfbnaDocFl, String custDescTx, Date currEffDt, String retailStlmntFl, String governLawCd, int totalEmplCnt, int totalAssetAmt, Date agreeRestateDt, Date totalEmplDt, Date totalAssetDt, Date annualSalesDt, int hardClPydownAmt, int wcisCupId, int wcisSupId, int hardTempClAmt, int hardTempPdwnAmt, String stockExchMicId, String stockTickerId, String riskCountryCd, int totRecovAmt, String taxIdTypeCd, Date bnkrpDt, String apiHApprUpdFl) {
        this.custNo = custNo;
        this.custTypeCode = custTypeCode;
        this.custStatusCode = custStatusCode;
        this.affilCustNo = affilCustNo;
        this.cntlEntNo = cntlEntNo;
        this.billAcctRepCode = billAcctRepCode;
        this.busCode = busCode;
        this.collBranchNo = collBranchNo;
        this.collDelayCnt = collDelayCnt;
        this.rate360Flag = rate360Flag;
        this.advPayFlag = advPayFlag;
        this.recourseFlag = recourseFlag;
        this.shipAuthAmt = shipAuthAmt;
        this.billCustFlag = billCustFlag;
        this.slCycleNo = slCycleNo;
        this.shipAuthFlag = shipAuthFlag;
        this.finanStmtDate = finanStmtDate;
        this.dunnSicCode = dunnSicCode;
        this.dunnNo = dunnNo;
        this.busEstabYearNo = busEstabYearNo;
        this.commBeginDay1No = commBeginDay1No;
        this.commEndDay1No = commEndDay1No;
        this.commDueDay1No = commDueDay1No;
        this.commBeginDay2No = commBeginDay2No;
        this.commEndDay2No = commEndDay2No;
        this.commDueDay2No = commDueDay2No;
        this.commBeginDay3No = commBeginDay3No;
        this.commEndDay3No = commEndDay3No;
        this.commDueDay3No = commDueDay3No;
        this.commBeginDay4No = commBeginDay4No;
        this.commEndDay4No = commEndDay4No;
        this.commDueDay4No = commDueDay4No;
        this.repurFlag = repurFlag;
        this.stripPassFlag = stripPassFlag;
        this.revFreqCode = revFreqCode;
        this.trustPrtformCode = trustPrtformCode;
        this.trustPrtCode = trustPrtCode;
        this.taxIdUpdCnt = taxIdUpdCnt;
        this.taxIdNo = taxIdNo;
        this.intCredExpDate = intCredExpDate;
        this.tcfcStartYearNo = tcfcStartYearNo;
        this.apprTypeCode = apprTypeCode;
        this.billConsFlag = billConsFlag;
        this.billSubDistFlag = billSubDistFlag;
        this.lastRevDate = lastRevDate;
        this.insFlag = insFlag;
        this.coverageInsAmt = coverageInsAmt;
        this.policyExpDate = policyExpDate;
        this.ovrdInsRate = ovrdInsRate;
        this.insRateCode = insRateCode;
        this.sotUpdDate = sotUpdDate;
        this.repoUpdDate = repoUpdDate;
        this.repoAmt = repoAmt;
        this.sotBalDueAmt = sotBalDueAmt;
        this.reserveFund2Amt = reserveFund2Amt;
        this.reserveFund1Amt = reserveFund1Amt;
        this.inctPayBranchNo = inctPayBranchNo;
        this.inctBasedOnCode = inctBasedOnCode;
        this.inctPayFreqCode = inctPayFreqCode;
        this.inctPaidDate = inctPaidDate;
        this.billAddrNo = billAddrNo;
        this.payAddrNo = payAddrNo;
        this.phoneNo = phoneNo;
        this.faxNo = faxNo;
        this.fcBranchNo = fcBranchNo;
        this.fcCycleNo = fcCycleNo;
        this.fcSortSeqCode = fcSortSeqCode;
        this.fcTypeCode = fcTypeCode;
        this.fcLastDate = fcLastDate;
        this.fcLastInitials = fcLastInitials;
        this.estLossInitials = estLossInitials;
        this.estLossAmt = estLossAmt;
        this.distMfgOsAmt = distMfgOsAmt;
        this.uccStFilingNo = uccStFilingNo;
        this.uccStExpDate = uccStExpDate;
        this.uccCntyFilingNo = uccCntyFilingNo;
        this.uccCntyExpDate = uccCntyExpDate;
        this.initChrgRptFlag = initChrgRptFlag;
        this.collDelayInd = collDelayInd;
        this.slGraceDaysCnt = slGraceDaysCnt;
        this.credScoreNo = credScoreNo;
        this.industryCode = industryCode;
        this.dlrRepCode = dlrRepCode;
        this.bosAuditDate = bosAuditDate;
        this.bosCycleNo = bosCycleNo;
        this.bankAuditDate = bankAuditDate;
        this.bankCycleNo = bankCycleNo;
        this.adminFlatAmt = adminFlatAmt;
        this.adminRate = adminRate;
        this.adminMinOsAmt = adminMinOsAmt;
        this.lossReserveRate = lossReserveRate;
        this.lossReserveAmt = lossReserveAmt;
        this.cntlBranchNo = cntlBranchNo;
        this.fsrRepCode = fsrRepCode;
        this.lastFsrRepCode = lastFsrRepCode;
        this.titleCode = titleCode;
        this.shipCodeReqFlag = shipCodeReqFlag;
        this.plbBranchNo = plbBranchNo;
        this.plbPayAddrNo = plbPayAddrNo;
        this.annualSalesAmt = annualSalesAmt;
        this.cogsAmt = cogsAmt;
        this.bankName = bankName;
        this.leadSourceCode = leadSourceCode;
        this.leadCustNo = leadCustNo;
        this.projCode = projCode;
        this.salesCogsDate = salesCogsDate;
        this.crRvReqstInd = crRvReqstInd;
        this.crRvReqstDate = crRvReqstDate;
        this.crRvDlrRepCode = crRvDlrRepCode;
        this.estCredLnAmt = estCredLnAmt;
        this.finSource1Code = finSource1Code;
        this.finSource2Code = finSource2Code;
        this.finSource3Code = finSource3Code;
        this.finSource4Code = finSource4Code;
        this.computSystemInd = computSystemInd;
        this.cntlInvnFlag = cntlInvnFlag;
        this.cntlAcctrecvFlag = cntlAcctrecvFlag;
        this.cntlAcctPblFlag = cntlAcctPblFlag;
        this.modemFlag = modemFlag;
        this.scfcRecvdDate = scfcRecvdDate;
        this.seasonalCode = seasonalCode;
        this.fcJanCnt = fcJanCnt;
        this.fcFebCnt = fcFebCnt;
        this.fcMarCnt = fcMarCnt;
        this.fcAprCnt = fcAprCnt;
        this.fcMayCnt = fcMayCnt;
        this.fcJunCnt = fcJunCnt;
        this.fcJulCnt = fcJulCnt;
        this.fcAugCnt = fcAugCnt;
        this.fcSepCnt = fcSepCnt;
        this.fcOctCnt = fcOctCnt;
        this.fcNovCnt = fcNovCnt;
        this.fcDecCnt = fcDecCnt;
        this.fcTypeEffDate = fcTypeEffDate;
        this.fcTypeLogonId = fcTypeLogonId;
        this.fcReqCnt = fcReqCnt;
        this.lastYearFcCnt = lastYearFcCnt;
        this.scfcPrtInd = scfcPrtInd;
        this.scfcInclSlFlag = scfcInclSlFlag;
        this.nextSchedFcDate = nextSchedFcDate;
        this.prtAllTrustFlag = prtAllTrustFlag;
        this.createDate = createDate;
        this.fcFreqEffDt = fcFreqEffDt;
        this.fcFreqLogonId = fcFreqLogonId;
        this.equityCycNo = equityCycNo;
        this.equityDate = equityDate;
        this.invnCycNo = invnCycNo;
        this.invnDate = invnDate;
        this.recclAmt = recclAmt;
        this.scfcTrkNonFlag = scfcTrkNonFlag;
        this.scfcSent01Date = scfcSent01Date;
        this.scfcSent02Date = scfcSent02Date;
        this.pblFundDayInd = pblFundDayInd;
        this.pcsPurgeFlag = pcsPurgeFlag;
        this.ovlnTolerFlag = ovlnTolerFlag;
        this.apprPurgeMinAmt = apprPurgeMinAmt;
        this.apprPurgeMinPct = apprPurgeMinPct;
        this.apprPurgeDayCnt = apprPurgeDayCnt;
        this.credHoldDate = credHoldDate;
        this.ovrdFundDateInd = ovrdFundDateInd;
        this.noTrustDtlFlag = noTrustDtlFlag;
        this.purchContactName = purchContactName;
        this.purchPhoneNo = purchPhoneNo;
        this.purchFaxNo = purchFaxNo;
        this.adminLogonId = adminLogonId;
        this.serialNoFlag = serialNoFlag;
        this.purchUpdDate = purchUpdDate;
        this.purchUpdLogonId = purchUpdLogonId;
        this.explodeInd = explodeInd;
        this.filingTypeCode = filingTypeCode;
        this.epdDelayDaysCnt = epdDelayDaysCnt;
        this.salesZipCode = salesZipCode;
        this.finStEntryDate = finStEntryDate;
        this.trstSpecPrtDate = trstSpecPrtDate;
        this.trstSpecPrtInd = trstSpecPrtInd;
        this.credPrtFlag = credPrtFlag;
        this.credPrtFreqCode = credPrtFreqCode;
        this.stProvCode = stProvCode;
        this.zipPostalCode = zipPostalCode;
        this.countryCode = countryCode;
        this.addr1Name = addr1Name;
        this.addr2Name = addr2Name;
        this.cityName = cityName;
        this.countyName = countyName;
        this.legalName = legalName;
        this.dbaName = dbaName;
        this.contactName = contactName;
        this.contactTitle = contactTitle;
        this.locDesc = locDesc;
        this.cashPriorityNo = cashPriorityNo;
        this.xcSourceFlag = xcSourceFlag;
        this.xcEligibleFlag = xcEligibleFlag;
        this.xcCredStatCode = xcCredStatCode;
        this.slWeekdayFlag = slWeekdayFlag;
        this.prtCredMemoInd = prtCredMemoInd;
        this.cmApplyInd = cmApplyInd;
        this.cmFundFreqCode = cmFundFreqCode;
        this.cmFundDayCode = cmFundDayCode;
        this.prtCurrencyFlag = prtCurrencyFlag;
        this.attrnOvrdFlag = attrnOvrdFlag;
        this.attrnExpDate = attrnExpDate;
        this.shortName = shortName;
        this.trstSpecPrtCode = trstSpecPrtCode;
        this.emailAddrName = emailAddrName;
        this.pdeSortSeqCode = pdeSortSeqCode;
        this.mstrInvPrtCode = mstrInvPrtCode;
        this.mstrInvReqInd = mstrInvReqInd;
        this.trustEdiCode = trustEdiCode;
        this.vatRgstrNo = vatRgstrNo;
        this.vatElectionInd = vatElectionInd;
        this.languageCode = languageCode;
        this.holdNegPblInd = holdNegPblInd;
        this.faxStubsFlag = faxStubsFlag;
        this.polCredMemoFlag = polCredMemoFlag;
        this.epdGraceDaysCnt = epdGraceDaysCnt;
        this.epdFundFreqCode = epdFundFreqCode;
        this.epdFundDaysCnt = epdFundDaysCnt;
        this.credClaimFlag = credClaimFlag;
        this.credClaimDayCnt = credClaimDayCnt;
        this.rmbCmDiscPgmNo = rmbCmDiscPgmNo;
        this.skuIdFlag = skuIdFlag;
        this.prtCredNoteInd = prtCredNoteInd;
        this.uccOrgNo = uccOrgNo;
        this.uccOrgSt = uccOrgSt;
        this.systemCreateFlag = systemCreateFlag;
        this.currencyCode = currencyCode;
        this.autoCashFlag = autoCashFlag;
        this.incomplCashFlag = incomplCashFlag;
        this.podPresDayCnt = podPresDayCnt;
        this.podPresDateFlag = podPresDateFlag;
        this.trueUpEarlyFlag = trueUpEarlyFlag;
        this.trueUpLateFlag = trueUpLateFlag;
        this.tdfRiskPct = tdfRiskPct;
        this.riskFeeRate = riskFeeRate;
        this.riskFeeFiuFlag = riskFeeFiuFlag;
        this.portTypeCode = portTypeCode;
        this.servTrustFlag = servTrustFlag;
        this.lossReserve2Rate = lossReserve2Rate;
        this.grossupPct = grossupPct;
        this.adbDiscInd = adbDiscInd;
        this.distFundOsAmt = distFundOsAmt;
        this.dlrChrgTdfFlag = dlrChrgTdfFlag;
        this.miscId = miscId;
        this.servFeeFreqInd = servFeeFreqInd;
        this.collFeeRate = collFeeRate;
        this.fundHoldPct = fundHoldPct;
        this.fundLimitAmt = fundLimitAmt;
        this.servFeeBranchNo = servFeeBranchNo;
        this.trueUpIntFlag = trueUpIntFlag;
        this.curtGraceDayCnt = curtGraceDayCnt;
        this.anrMonthsCnt = anrMonthsCnt;
        this.mstrAgreeDate = mstrAgreeDate;
        this.cmApplyRuleCode = cmApplyRuleCode;
        this.cmApplyExcpCode = cmApplyExcpCode;
        this.scrtzEligCd = scrtzEligCd;
        this.securedFl = securedFl;
        this.scrtzEffDate = scrtzEffDate;
        this.scrtzParticRt = scrtzParticRt;
        this.scrtzPoolId = scrtzPoolId;
        this.adbBillMethCode = adbBillMethCode;
        this.lateFeeBillInd = lateFeeBillInd;
        this.edocsInd = edocsInd;
        this.edocsStartDate = edocsStartDate;
        this.edocsPrtEndDate = edocsPrtEndDate;
        this.auxCarrNo = auxCarrNo;
        this.curtPrepaidFlag = curtPrepaidFlag;
        this.stmtSortCode = stmtSortCode;
        this.stmtPageBrkFlag = stmtPageBrkFlag;
        this.recvCmPrtFlag = recvCmPrtFlag;
        this.slStmtPrtFl = slStmtPrtFl;
        this.epdCbgnDay1No = epdCbgnDay1No;
        this.epdCbgnDay2No = epdCbgnDay2No;
        this.epdCbgnDay3No = epdCbgnDay3No;
        this.epdCbgnDay4No = epdCbgnDay4No;
        this.epdCdueDay1No = epdCdueDay1No;
        this.epdCdueDay2No = epdCdueDay2No;
        this.epdCdueDay3No = epdCdueDay3No;
        this.epdCdueDay4No = epdCdueDay4No;
        this.epdCendDay1No = epdCendDay1No;
        this.epdCendDay2No = epdCendDay2No;
        this.epdCendDay3No = epdCendDay3No;
        this.epdCendDay4No = epdCendDay4No;
        this.refundDistFdFl = refundDistFdFl;
        this.cashAlgCode = cashAlgCode;
        this.autoStlmntFl = autoStlmntFl;
        this.tranStmtFrmtCd = tranStmtFrmtCd;
        this.defNoPayFlag = defNoPayFlag;
        this.defComposRtFlag = defComposRtFlag;
        this.defBankCode = defBankCode;
        this.defTypeInd = defTypeInd;
        this.defMinprimeRate = defMinprimeRate;
        this.defMaxprimeRate = defMaxprimeRate;
        this.straightRate = straightRate;
        this.prtNoteStmtInd = prtNoteStmtInd;
        this.segmentCd = segmentCd;
        this.highRiskFl = highRiskFl;
        this.kmvId = kmvId;
        this.extIdTypeCd = extIdTypeCd;
        this.extIdNo = extIdNo;
        this.ofstTypeCd = ofstTypeCd;
        this.lineDetailFl = lineDetailFl;
        this.fixpSkipMo01Fl = fixpSkipMo01Fl;
        this.fixpSkipMo02Fl = fixpSkipMo02Fl;
        this.fixpSkipMo03Fl = fixpSkipMo03Fl;
        this.fixpSkipMo04Fl = fixpSkipMo04Fl;
        this.fixpSkipMo05Fl = fixpSkipMo05Fl;
        this.fixpSkipMo06Fl = fixpSkipMo06Fl;
        this.fixpSkipMo07Fl = fixpSkipMo07Fl;
        this.fixpSkipMo08Fl = fixpSkipMo08Fl;
        this.fixpSkipMo09Fl = fixpSkipMo09Fl;
        this.fixpSkipMo10Fl = fixpSkipMo10Fl;
        this.fixpSkipMo11Fl = fixpSkipMo11Fl;
        this.fixpSkipMo12Fl = fixpSkipMo12Fl;
        this.distPrchEmailNm = distPrchEmailNm;
        this.rstrFl = rstrFl;
        this.starFl = starFl;
        this.bnkrpFl = bnkrpFl;
        this.regSweepFl = regSweepFl;
        this.putReasonCd = putReasonCd;
        this.putReasonDt = putReasonDt;
        this.bankWatchFl = bankWatchFl;
        this.ovlnTolerRt = ovlnTolerRt;
        this.otbHeldPayFl = otbHeldPayFl;
        this.otbUnapplCashFl = otbUnapplCashFl;
        this.otbUnidCashFl = otbUnidCashFl;
        this.otbDefFl = otbDefFl;
        this.pcsDefApprAmt = pcsDefApprAmt;
        this.otbExpoExclInd = otbExpoExclInd;
        this.finanStmtInrmDt = finanStmtInrmDt;
        this.finanStmtInrmCt = finanStmtInrmCt;
        this.finanStmtExtnDt = finanStmtExtnDt;
        this.publicPrivInd = publicPrivInd;
        this.lastRevExtnDt = lastRevExtnDt;
        this.finanStmtQualCd = finanStmtQualCd;
        this.endUserNm = endUserNm;
        this.mraCustId = mraCustId;
        this.defOfstPrincFl = defOfstPrincFl;
        this.defActivPageFl = defActivPageFl;
        this.undwrGdlnExcpFl = undwrGdlnExcpFl;
        this.dlrPreauthFl = dlrPreauthFl;
        this.preauthAllowFl = preauthAllowFl;
        this.ediStmtTypeCd = ediStmtTypeCd;
        this.ediMfgDistFlag = ediMfgDistFlag;
        this.epdCmFl = epdCmFl;
        this.mmtsCmprsFrmtFl = mmtsCmprsFrmtFl;
        this.prpCrtWavRprFl = prpCrtWavRprFl;
        this.comsBigBufferCt = comsBigBufferCt;
        this.dlvVrfctnFl = dlvVrfctnFl;
        this.rcpCrtTaxInvFl = rcpCrtTaxInvFl;
        this.overseasDistFl = overseasDistFl;
        this.mobileNo = mobileNo;
        this.servLvlCatCd = servLvlCatCd;
        this.partyId = partyId;
        this.touchlessSyncId = touchlessSyncId;
        this.priorChrgOffDt = priorChrgOffDt;
        this.totChrgOffAmt = totChrgOffAmt;
        this.ddOvrdSweepFl = ddOvrdSweepFl;
        this.prevScrtzPoolId = prevScrtzPoolId;
        this.lastSpecRevDate = lastSpecRevDate;
        this.loanCommitFl = loanCommitFl;
        this.loanCommitRenDt = loanCommitRenDt;
        this.ckDeliveryCd = ckDeliveryCd;
        this.wcisId = wcisId;
        this.naicsCd = naicsCd;
        this.custNoteTx = custNoteTx;
        this.naceCd = naceCd;
        this.collatSegmentDs = collatSegmentDs;
        this.auditTs = auditTs;
        this.auditLogonId = auditLogonId;
        this.auditProcessTx = auditProcessTx;
        this.auditDeleteFl = auditDeleteFl;
        this.netRelianceAmt = netRelianceAmt;
        this.lastAmlRevDt = lastAmlRevDt;
        this.ecrrCd = ecrrCd;
        this.craCd = craCd;
        this.craEffDt = craEffDt;
        this.craEndDt = craEndDt;
        this.hardCredLnAmt = hardCredLnAmt;
        this.workoutFl = workoutFl;
        this.workoutResolveCd = workoutResolveCd;
        this.origEffDt = origEffDt;
        this.acquireDt = acquireDt;
        this.rooftopCnt = rooftopCnt;
        this.cmrclDsclrFl = cmrclDsclrFl;
        this.distAgreeDt = distAgreeDt;
        this.wfbnaDocFl = wfbnaDocFl;
        this.custDescTx = custDescTx;
        this.currEffDt = currEffDt;
        this.retailStlmntFl = retailStlmntFl;
        this.governLawCd = governLawCd;
        this.totalEmplCnt = totalEmplCnt;
        this.totalAssetAmt = totalAssetAmt;
        this.agreeRestateDt = agreeRestateDt;
        this.totalEmplDt = totalEmplDt;
        this.totalAssetDt = totalAssetDt;
        this.annualSalesDt = annualSalesDt;
        this.hardClPydownAmt = hardClPydownAmt;
        this.wcisCupId = wcisCupId;
        this.wcisSupId = wcisSupId;
        this.hardTempClAmt = hardTempClAmt;
        this.hardTempPdwnAmt = hardTempPdwnAmt;
        this.stockExchMicId = stockExchMicId;
        this.stockTickerId = stockTickerId;
        this.riskCountryCd = riskCountryCd;
        this.totRecovAmt = totRecovAmt;
        this.taxIdTypeCd = taxIdTypeCd;
        this.bnkrpDt = bnkrpDt;
        this.apiHApprUpdFl = apiHApprUpdFl;
    }
    
    @Override
    public Vwmcu00 clone() throws CloneNotSupportedException {
        Vwmcu00 cloned = (Vwmcu00) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code Vwmcu00} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected Vwmcu00(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code Vwmcu00} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected Vwmcu00(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code Vwmcu00} object
     * @see #setBytes(byte[], int)
     */
    public static Vwmcu00 fromBytes(byte[] bytes, int offset) {
        return new Vwmcu00(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code Vwmcu00} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static Vwmcu00 fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code Vwmcu00} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static Vwmcu00 fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public int getCustNo() {
        return this.custNo;
    }
    
    public void setCustNo(int custNo) {
        this.custNo = custNo;
    }
    
    public String getCustTypeCode() {
        return this.custTypeCode;
    }
    
    public void setCustTypeCode(String custTypeCode) {
        this.custTypeCode = custTypeCode;
    }
    
    public String getCustStatusCode() {
        return this.custStatusCode;
    }
    
    public void setCustStatusCode(String custStatusCode) {
        this.custStatusCode = custStatusCode;
    }
    
    public int getAffilCustNo() {
        return this.affilCustNo;
    }
    
    public void setAffilCustNo(int affilCustNo) {
        this.affilCustNo = affilCustNo;
    }
    
    public int getCntlEntNo() {
        return this.cntlEntNo;
    }
    
    public void setCntlEntNo(int cntlEntNo) {
        this.cntlEntNo = cntlEntNo;
    }
    
    public String getBillAcctRepCode() {
        return this.billAcctRepCode;
    }
    
    public void setBillAcctRepCode(String billAcctRepCode) {
        this.billAcctRepCode = billAcctRepCode;
    }
    
    public String getBusCode() {
        return this.busCode;
    }
    
    public void setBusCode(String busCode) {
        this.busCode = busCode;
    }
    
    public int getCollBranchNo() {
        return this.collBranchNo;
    }
    
    public void setCollBranchNo(int collBranchNo) {
        this.collBranchNo = collBranchNo;
    }
    
    public int getCollDelayCnt() {
        return this.collDelayCnt;
    }
    
    public void setCollDelayCnt(int collDelayCnt) {
        this.collDelayCnt = collDelayCnt;
    }
    
    public String getRate360Flag() {
        return this.rate360Flag;
    }
    
    public void setRate360Flag(String rate360Flag) {
        this.rate360Flag = rate360Flag;
    }
    
    public String getAdvPayFlag() {
        return this.advPayFlag;
    }
    
    public void setAdvPayFlag(String advPayFlag) {
        this.advPayFlag = advPayFlag;
    }
    
    public String getRecourseFlag() {
        return this.recourseFlag;
    }
    
    public void setRecourseFlag(String recourseFlag) {
        this.recourseFlag = recourseFlag;
    }
    
    public int getShipAuthAmt() {
        return this.shipAuthAmt;
    }
    
    public void setShipAuthAmt(int shipAuthAmt) {
        this.shipAuthAmt = shipAuthAmt;
    }
    
    public String getBillCustFlag() {
        return this.billCustFlag;
    }
    
    public void setBillCustFlag(String billCustFlag) {
        this.billCustFlag = billCustFlag;
    }
    
    public String getSlCycleNo() {
        return this.slCycleNo;
    }
    
    public void setSlCycleNo(String slCycleNo) {
        this.slCycleNo = slCycleNo;
    }
    
    public String getShipAuthFlag() {
        return this.shipAuthFlag;
    }
    
    public void setShipAuthFlag(String shipAuthFlag) {
        this.shipAuthFlag = shipAuthFlag;
    }
    
    public Date getFinanStmtDate() {
        return this.finanStmtDate;
    }
    
    public void setFinanStmtDate(Date finanStmtDate) {
        this.finanStmtDate = finanStmtDate;
    }
    
    public String getDunnSicCode() {
        return this.dunnSicCode;
    }
    
    public void setDunnSicCode(String dunnSicCode) {
        this.dunnSicCode = dunnSicCode;
    }
    
    public String getDunnNo() {
        return this.dunnNo;
    }
    
    public void setDunnNo(String dunnNo) {
        this.dunnNo = dunnNo;
    }
    
    public int getBusEstabYearNo() {
        return this.busEstabYearNo;
    }
    
    public void setBusEstabYearNo(int busEstabYearNo) {
        this.busEstabYearNo = busEstabYearNo;
    }
    
    public int getCommBeginDay1No() {
        return this.commBeginDay1No;
    }
    
    public void setCommBeginDay1No(int commBeginDay1No) {
        this.commBeginDay1No = commBeginDay1No;
    }
    
    public int getCommEndDay1No() {
        return this.commEndDay1No;
    }
    
    public void setCommEndDay1No(int commEndDay1No) {
        this.commEndDay1No = commEndDay1No;
    }
    
    public int getCommDueDay1No() {
        return this.commDueDay1No;
    }
    
    public void setCommDueDay1No(int commDueDay1No) {
        this.commDueDay1No = commDueDay1No;
    }
    
    public int getCommBeginDay2No() {
        return this.commBeginDay2No;
    }
    
    public void setCommBeginDay2No(int commBeginDay2No) {
        this.commBeginDay2No = commBeginDay2No;
    }
    
    public int getCommEndDay2No() {
        return this.commEndDay2No;
    }
    
    public void setCommEndDay2No(int commEndDay2No) {
        this.commEndDay2No = commEndDay2No;
    }
    
    public int getCommDueDay2No() {
        return this.commDueDay2No;
    }
    
    public void setCommDueDay2No(int commDueDay2No) {
        this.commDueDay2No = commDueDay2No;
    }
    
    public int getCommBeginDay3No() {
        return this.commBeginDay3No;
    }
    
    public void setCommBeginDay3No(int commBeginDay3No) {
        this.commBeginDay3No = commBeginDay3No;
    }
    
    public int getCommEndDay3No() {
        return this.commEndDay3No;
    }
    
    public void setCommEndDay3No(int commEndDay3No) {
        this.commEndDay3No = commEndDay3No;
    }
    
    public int getCommDueDay3No() {
        return this.commDueDay3No;
    }
    
    public void setCommDueDay3No(int commDueDay3No) {
        this.commDueDay3No = commDueDay3No;
    }
    
    public int getCommBeginDay4No() {
        return this.commBeginDay4No;
    }
    
    public void setCommBeginDay4No(int commBeginDay4No) {
        this.commBeginDay4No = commBeginDay4No;
    }
    
    public int getCommEndDay4No() {
        return this.commEndDay4No;
    }
    
    public void setCommEndDay4No(int commEndDay4No) {
        this.commEndDay4No = commEndDay4No;
    }
    
    public int getCommDueDay4No() {
        return this.commDueDay4No;
    }
    
    public void setCommDueDay4No(int commDueDay4No) {
        this.commDueDay4No = commDueDay4No;
    }
    
    public String getRepurFlag() {
        return this.repurFlag;
    }
    
    public void setRepurFlag(String repurFlag) {
        this.repurFlag = repurFlag;
    }
    
    public String getStripPassFlag() {
        return this.stripPassFlag;
    }
    
    public void setStripPassFlag(String stripPassFlag) {
        this.stripPassFlag = stripPassFlag;
    }
    
    public int getRevFreqCode() {
        return this.revFreqCode;
    }
    
    public void setRevFreqCode(int revFreqCode) {
        this.revFreqCode = revFreqCode;
    }
    
    public String getTrustPrtformCode() {
        return this.trustPrtformCode;
    }
    
    public void setTrustPrtformCode(String trustPrtformCode) {
        this.trustPrtformCode = trustPrtformCode;
    }
    
    public String getTrustPrtCode() {
        return this.trustPrtCode;
    }
    
    public void setTrustPrtCode(String trustPrtCode) {
        this.trustPrtCode = trustPrtCode;
    }
    
    public int getTaxIdUpdCnt() {
        return this.taxIdUpdCnt;
    }
    
    public void setTaxIdUpdCnt(int taxIdUpdCnt) {
        this.taxIdUpdCnt = taxIdUpdCnt;
    }
    
    public String getTaxIdNo() {
        return this.taxIdNo;
    }
    
    public void setTaxIdNo(String taxIdNo) {
        this.taxIdNo = taxIdNo;
    }
    
    public Date getIntCredExpDate() {
        return this.intCredExpDate;
    }
    
    public void setIntCredExpDate(Date intCredExpDate) {
        this.intCredExpDate = intCredExpDate;
    }
    
    public int getTcfcStartYearNo() {
        return this.tcfcStartYearNo;
    }
    
    public void setTcfcStartYearNo(int tcfcStartYearNo) {
        this.tcfcStartYearNo = tcfcStartYearNo;
    }
    
    public String getApprTypeCode() {
        return this.apprTypeCode;
    }
    
    public void setApprTypeCode(String apprTypeCode) {
        this.apprTypeCode = apprTypeCode;
    }
    
    public String getBillConsFlag() {
        return this.billConsFlag;
    }
    
    public void setBillConsFlag(String billConsFlag) {
        this.billConsFlag = billConsFlag;
    }
    
    public String getBillSubDistFlag() {
        return this.billSubDistFlag;
    }
    
    public void setBillSubDistFlag(String billSubDistFlag) {
        this.billSubDistFlag = billSubDistFlag;
    }
    
    public Date getLastRevDate() {
        return this.lastRevDate;
    }
    
    public void setLastRevDate(Date lastRevDate) {
        this.lastRevDate = lastRevDate;
    }
    
    public String getInsFlag() {
        return this.insFlag;
    }
    
    public void setInsFlag(String insFlag) {
        this.insFlag = insFlag;
    }
    
    public int getCoverageInsAmt() {
        return this.coverageInsAmt;
    }
    
    public void setCoverageInsAmt(int coverageInsAmt) {
        this.coverageInsAmt = coverageInsAmt;
    }
    
    public Date getPolicyExpDate() {
        return this.policyExpDate;
    }
    
    public void setPolicyExpDate(Date policyExpDate) {
        this.policyExpDate = policyExpDate;
    }
    
    public int getOvrdInsRate() {
        return this.ovrdInsRate;
    }
    
    public void setOvrdInsRate(int ovrdInsRate) {
        this.ovrdInsRate = ovrdInsRate;
    }
    
    public int getInsRateCode() {
        return this.insRateCode;
    }
    
    public void setInsRateCode(int insRateCode) {
        this.insRateCode = insRateCode;
    }
    
    public Date getSotUpdDate() {
        return this.sotUpdDate;
    }
    
    public void setSotUpdDate(Date sotUpdDate) {
        this.sotUpdDate = sotUpdDate;
    }
    
    public Date getRepoUpdDate() {
        return this.repoUpdDate;
    }
    
    public void setRepoUpdDate(Date repoUpdDate) {
        this.repoUpdDate = repoUpdDate;
    }
    
    public int getRepoAmt() {
        return this.repoAmt;
    }
    
    public void setRepoAmt(int repoAmt) {
        this.repoAmt = repoAmt;
    }
    
    public int getSotBalDueAmt() {
        return this.sotBalDueAmt;
    }
    
    public void setSotBalDueAmt(int sotBalDueAmt) {
        this.sotBalDueAmt = sotBalDueAmt;
    }
    
    public int getReserveFund2Amt() {
        return this.reserveFund2Amt;
    }
    
    public void setReserveFund2Amt(int reserveFund2Amt) {
        this.reserveFund2Amt = reserveFund2Amt;
    }
    
    public int getReserveFund1Amt() {
        return this.reserveFund1Amt;
    }
    
    public void setReserveFund1Amt(int reserveFund1Amt) {
        this.reserveFund1Amt = reserveFund1Amt;
    }
    
    public int getInctPayBranchNo() {
        return this.inctPayBranchNo;
    }
    
    public void setInctPayBranchNo(int inctPayBranchNo) {
        this.inctPayBranchNo = inctPayBranchNo;
    }
    
    public String getInctBasedOnCode() {
        return this.inctBasedOnCode;
    }
    
    public void setInctBasedOnCode(String inctBasedOnCode) {
        this.inctBasedOnCode = inctBasedOnCode;
    }
    
    public String getInctPayFreqCode() {
        return this.inctPayFreqCode;
    }
    
    public void setInctPayFreqCode(String inctPayFreqCode) {
        this.inctPayFreqCode = inctPayFreqCode;
    }
    
    public Date getInctPaidDate() {
        return this.inctPaidDate;
    }
    
    public void setInctPaidDate(Date inctPaidDate) {
        this.inctPaidDate = inctPaidDate;
    }
    
    public int getBillAddrNo() {
        return this.billAddrNo;
    }
    
    public void setBillAddrNo(int billAddrNo) {
        this.billAddrNo = billAddrNo;
    }
    
    public int getPayAddrNo() {
        return this.payAddrNo;
    }
    
    public void setPayAddrNo(int payAddrNo) {
        this.payAddrNo = payAddrNo;
    }
    
    public String getPhoneNo() {
        return this.phoneNo;
    }
    
    public void setPhoneNo(String phoneNo) {
        this.phoneNo = phoneNo;
    }
    
    public String getFaxNo() {
        return this.faxNo;
    }
    
    public void setFaxNo(String faxNo) {
        this.faxNo = faxNo;
    }
    
    public int getFcBranchNo() {
        return this.fcBranchNo;
    }
    
    public void setFcBranchNo(int fcBranchNo) {
        this.fcBranchNo = fcBranchNo;
    }
    
    public int getFcCycleNo() {
        return this.fcCycleNo;
    }
    
    public void setFcCycleNo(int fcCycleNo) {
        this.fcCycleNo = fcCycleNo;
    }
    
    public String getFcSortSeqCode() {
        return this.fcSortSeqCode;
    }
    
    public void setFcSortSeqCode(String fcSortSeqCode) {
        this.fcSortSeqCode = fcSortSeqCode;
    }
    
    public String getFcTypeCode() {
        return this.fcTypeCode;
    }
    
    public void setFcTypeCode(String fcTypeCode) {
        this.fcTypeCode = fcTypeCode;
    }
    
    public Date getFcLastDate() {
        return this.fcLastDate;
    }
    
    public void setFcLastDate(Date fcLastDate) {
        this.fcLastDate = fcLastDate;
    }
    
    public String getFcLastInitials() {
        return this.fcLastInitials;
    }
    
    public void setFcLastInitials(String fcLastInitials) {
        this.fcLastInitials = fcLastInitials;
    }
    
    public String getEstLossInitials() {
        return this.estLossInitials;
    }
    
    public void setEstLossInitials(String estLossInitials) {
        this.estLossInitials = estLossInitials;
    }
    
    public int getEstLossAmt() {
        return this.estLossAmt;
    }
    
    public void setEstLossAmt(int estLossAmt) {
        this.estLossAmt = estLossAmt;
    }
    
    public int getDistMfgOsAmt() {
        return this.distMfgOsAmt;
    }
    
    public void setDistMfgOsAmt(int distMfgOsAmt) {
        this.distMfgOsAmt = distMfgOsAmt;
    }
    
    public String getUccStFilingNo() {
        return this.uccStFilingNo;
    }
    
    public void setUccStFilingNo(String uccStFilingNo) {
        this.uccStFilingNo = uccStFilingNo;
    }
    
    public Date getUccStExpDate() {
        return this.uccStExpDate;
    }
    
    public void setUccStExpDate(Date uccStExpDate) {
        this.uccStExpDate = uccStExpDate;
    }
    
    public String getUccCntyFilingNo() {
        return this.uccCntyFilingNo;
    }
    
    public void setUccCntyFilingNo(String uccCntyFilingNo) {
        this.uccCntyFilingNo = uccCntyFilingNo;
    }
    
    public Date getUccCntyExpDate() {
        return this.uccCntyExpDate;
    }
    
    public void setUccCntyExpDate(Date uccCntyExpDate) {
        this.uccCntyExpDate = uccCntyExpDate;
    }
    
    public String getInitChrgRptFlag() {
        return this.initChrgRptFlag;
    }
    
    public void setInitChrgRptFlag(String initChrgRptFlag) {
        this.initChrgRptFlag = initChrgRptFlag;
    }
    
    public String getCollDelayInd() {
        return this.collDelayInd;
    }
    
    public void setCollDelayInd(String collDelayInd) {
        this.collDelayInd = collDelayInd;
    }
    
    public int getSlGraceDaysCnt() {
        return this.slGraceDaysCnt;
    }
    
    public void setSlGraceDaysCnt(int slGraceDaysCnt) {
        this.slGraceDaysCnt = slGraceDaysCnt;
    }
    
    public int getCredScoreNo() {
        return this.credScoreNo;
    }
    
    public void setCredScoreNo(int credScoreNo) {
        this.credScoreNo = credScoreNo;
    }
    
    public String getIndustryCode() {
        return this.industryCode;
    }
    
    public void setIndustryCode(String industryCode) {
        this.industryCode = industryCode;
    }
    
    public String getDlrRepCode() {
        return this.dlrRepCode;
    }
    
    public void setDlrRepCode(String dlrRepCode) {
        this.dlrRepCode = dlrRepCode;
    }
    
    public Date getBosAuditDate() {
        return this.bosAuditDate;
    }
    
    public void setBosAuditDate(Date bosAuditDate) {
        this.bosAuditDate = bosAuditDate;
    }
    
    public int getBosCycleNo() {
        return this.bosCycleNo;
    }
    
    public void setBosCycleNo(int bosCycleNo) {
        this.bosCycleNo = bosCycleNo;
    }
    
    public Date getBankAuditDate() {
        return this.bankAuditDate;
    }
    
    public void setBankAuditDate(Date bankAuditDate) {
        this.bankAuditDate = bankAuditDate;
    }
    
    public int getBankCycleNo() {
        return this.bankCycleNo;
    }
    
    public void setBankCycleNo(int bankCycleNo) {
        this.bankCycleNo = bankCycleNo;
    }
    
    public int getAdminFlatAmt() {
        return this.adminFlatAmt;
    }
    
    public void setAdminFlatAmt(int adminFlatAmt) {
        this.adminFlatAmt = adminFlatAmt;
    }
    
    public int getAdminRate() {
        return this.adminRate;
    }
    
    public void setAdminRate(int adminRate) {
        this.adminRate = adminRate;
    }
    
    public int getAdminMinOsAmt() {
        return this.adminMinOsAmt;
    }
    
    public void setAdminMinOsAmt(int adminMinOsAmt) {
        this.adminMinOsAmt = adminMinOsAmt;
    }
    
    public int getLossReserveRate() {
        return this.lossReserveRate;
    }
    
    public void setLossReserveRate(int lossReserveRate) {
        this.lossReserveRate = lossReserveRate;
    }
    
    public int getLossReserveAmt() {
        return this.lossReserveAmt;
    }
    
    public void setLossReserveAmt(int lossReserveAmt) {
        this.lossReserveAmt = lossReserveAmt;
    }
    
    public int getCntlBranchNo() {
        return this.cntlBranchNo;
    }
    
    public void setCntlBranchNo(int cntlBranchNo) {
        this.cntlBranchNo = cntlBranchNo;
    }
    
    public String getFsrRepCode() {
        return this.fsrRepCode;
    }
    
    public void setFsrRepCode(String fsrRepCode) {
        this.fsrRepCode = fsrRepCode;
    }
    
    public String getLastFsrRepCode() {
        return this.lastFsrRepCode;
    }
    
    public void setLastFsrRepCode(String lastFsrRepCode) {
        this.lastFsrRepCode = lastFsrRepCode;
    }
    
    public String getTitleCode() {
        return this.titleCode;
    }
    
    public void setTitleCode(String titleCode) {
        this.titleCode = titleCode;
    }
    
    public String getShipCodeReqFlag() {
        return this.shipCodeReqFlag;
    }
    
    public void setShipCodeReqFlag(String shipCodeReqFlag) {
        this.shipCodeReqFlag = shipCodeReqFlag;
    }
    
    public int getPlbBranchNo() {
        return this.plbBranchNo;
    }
    
    public void setPlbBranchNo(int plbBranchNo) {
        this.plbBranchNo = plbBranchNo;
    }
    
    public int getPlbPayAddrNo() {
        return this.plbPayAddrNo;
    }
    
    public void setPlbPayAddrNo(int plbPayAddrNo) {
        this.plbPayAddrNo = plbPayAddrNo;
    }
    
    public int getAnnualSalesAmt() {
        return this.annualSalesAmt;
    }
    
    public void setAnnualSalesAmt(int annualSalesAmt) {
        this.annualSalesAmt = annualSalesAmt;
    }
    
    public int getCogsAmt() {
        return this.cogsAmt;
    }
    
    public void setCogsAmt(int cogsAmt) {
        this.cogsAmt = cogsAmt;
    }
    
    public String getBankName() {
        return this.bankName;
    }
    
    public void setBankName(String bankName) {
        this.bankName = bankName;
    }
    
    public String getLeadSourceCode() {
        return this.leadSourceCode;
    }
    
    public void setLeadSourceCode(String leadSourceCode) {
        this.leadSourceCode = leadSourceCode;
    }
    
    public int getLeadCustNo() {
        return this.leadCustNo;
    }
    
    public void setLeadCustNo(int leadCustNo) {
        this.leadCustNo = leadCustNo;
    }
    
    public String getProjCode() {
        return this.projCode;
    }
    
    public void setProjCode(String projCode) {
        this.projCode = projCode;
    }
    
    public Date getSalesCogsDate() {
        return this.salesCogsDate;
    }
    
    public void setSalesCogsDate(Date salesCogsDate) {
        this.salesCogsDate = salesCogsDate;
    }
    
    public String getCrRvReqstInd() {
        return this.crRvReqstInd;
    }
    
    public void setCrRvReqstInd(String crRvReqstInd) {
        this.crRvReqstInd = crRvReqstInd;
    }
    
    public Date getCrRvReqstDate() {
        return this.crRvReqstDate;
    }
    
    public void setCrRvReqstDate(Date crRvReqstDate) {
        this.crRvReqstDate = crRvReqstDate;
    }
    
    public String getCrRvDlrRepCode() {
        return this.crRvDlrRepCode;
    }
    
    public void setCrRvDlrRepCode(String crRvDlrRepCode) {
        this.crRvDlrRepCode = crRvDlrRepCode;
    }
    
    public int getEstCredLnAmt() {
        return this.estCredLnAmt;
    }
    
    public void setEstCredLnAmt(int estCredLnAmt) {
        this.estCredLnAmt = estCredLnAmt;
    }
    
    public String getFinSource1Code() {
        return this.finSource1Code;
    }
    
    public void setFinSource1Code(String finSource1Code) {
        this.finSource1Code = finSource1Code;
    }
    
    public String getFinSource2Code() {
        return this.finSource2Code;
    }
    
    public void setFinSource2Code(String finSource2Code) {
        this.finSource2Code = finSource2Code;
    }
    
    public String getFinSource3Code() {
        return this.finSource3Code;
    }
    
    public void setFinSource3Code(String finSource3Code) {
        this.finSource3Code = finSource3Code;
    }
    
    public String getFinSource4Code() {
        return this.finSource4Code;
    }
    
    public void setFinSource4Code(String finSource4Code) {
        this.finSource4Code = finSource4Code;
    }
    
    public String getComputSystemInd() {
        return this.computSystemInd;
    }
    
    public void setComputSystemInd(String computSystemInd) {
        this.computSystemInd = computSystemInd;
    }
    
    public String getCntlInvnFlag() {
        return this.cntlInvnFlag;
    }
    
    public void setCntlInvnFlag(String cntlInvnFlag) {
        this.cntlInvnFlag = cntlInvnFlag;
    }
    
    public String getCntlAcctrecvFlag() {
        return this.cntlAcctrecvFlag;
    }
    
    public void setCntlAcctrecvFlag(String cntlAcctrecvFlag) {
        this.cntlAcctrecvFlag = cntlAcctrecvFlag;
    }
    
    public String getCntlAcctPblFlag() {
        return this.cntlAcctPblFlag;
    }
    
    public void setCntlAcctPblFlag(String cntlAcctPblFlag) {
        this.cntlAcctPblFlag = cntlAcctPblFlag;
    }
    
    public String getModemFlag() {
        return this.modemFlag;
    }
    
    public void setModemFlag(String modemFlag) {
        this.modemFlag = modemFlag;
    }
    
    public Date getScfcRecvdDate() {
        return this.scfcRecvdDate;
    }
    
    public void setScfcRecvdDate(Date scfcRecvdDate) {
        this.scfcRecvdDate = scfcRecvdDate;
    }
    
    public int getSeasonalCode() {
        return this.seasonalCode;
    }
    
    public void setSeasonalCode(int seasonalCode) {
        this.seasonalCode = seasonalCode;
    }
    
    public int getFcJanCnt() {
        return this.fcJanCnt;
    }
    
    public void setFcJanCnt(int fcJanCnt) {
        this.fcJanCnt = fcJanCnt;
    }
    
    public int getFcFebCnt() {
        return this.fcFebCnt;
    }
    
    public void setFcFebCnt(int fcFebCnt) {
        this.fcFebCnt = fcFebCnt;
    }
    
    public int getFcMarCnt() {
        return this.fcMarCnt;
    }
    
    public void setFcMarCnt(int fcMarCnt) {
        this.fcMarCnt = fcMarCnt;
    }
    
    public int getFcAprCnt() {
        return this.fcAprCnt;
    }
    
    public void setFcAprCnt(int fcAprCnt) {
        this.fcAprCnt = fcAprCnt;
    }
    
    public int getFcMayCnt() {
        return this.fcMayCnt;
    }
    
    public void setFcMayCnt(int fcMayCnt) {
        this.fcMayCnt = fcMayCnt;
    }
    
    public int getFcJunCnt() {
        return this.fcJunCnt;
    }
    
    public void setFcJunCnt(int fcJunCnt) {
        this.fcJunCnt = fcJunCnt;
    }
    
    public int getFcJulCnt() {
        return this.fcJulCnt;
    }
    
    public void setFcJulCnt(int fcJulCnt) {
        this.fcJulCnt = fcJulCnt;
    }
    
    public int getFcAugCnt() {
        return this.fcAugCnt;
    }
    
    public void setFcAugCnt(int fcAugCnt) {
        this.fcAugCnt = fcAugCnt;
    }
    
    public int getFcSepCnt() {
        return this.fcSepCnt;
    }
    
    public void setFcSepCnt(int fcSepCnt) {
        this.fcSepCnt = fcSepCnt;
    }
    
    public int getFcOctCnt() {
        return this.fcOctCnt;
    }
    
    public void setFcOctCnt(int fcOctCnt) {
        this.fcOctCnt = fcOctCnt;
    }
    
    public int getFcNovCnt() {
        return this.fcNovCnt;
    }
    
    public void setFcNovCnt(int fcNovCnt) {
        this.fcNovCnt = fcNovCnt;
    }
    
    public int getFcDecCnt() {
        return this.fcDecCnt;
    }
    
    public void setFcDecCnt(int fcDecCnt) {
        this.fcDecCnt = fcDecCnt;
    }
    
    public Date getFcTypeEffDate() {
        return this.fcTypeEffDate;
    }
    
    public void setFcTypeEffDate(Date fcTypeEffDate) {
        this.fcTypeEffDate = fcTypeEffDate;
    }
    
    public String getFcTypeLogonId() {
        return this.fcTypeLogonId;
    }
    
    public void setFcTypeLogonId(String fcTypeLogonId) {
        this.fcTypeLogonId = fcTypeLogonId;
    }
    
    public int getFcReqCnt() {
        return this.fcReqCnt;
    }
    
    public void setFcReqCnt(int fcReqCnt) {
        this.fcReqCnt = fcReqCnt;
    }
    
    public int getLastYearFcCnt() {
        return this.lastYearFcCnt;
    }
    
    public void setLastYearFcCnt(int lastYearFcCnt) {
        this.lastYearFcCnt = lastYearFcCnt;
    }
    
    public String getScfcPrtInd() {
        return this.scfcPrtInd;
    }
    
    public void setScfcPrtInd(String scfcPrtInd) {
        this.scfcPrtInd = scfcPrtInd;
    }
    
    public String getScfcInclSlFlag() {
        return this.scfcInclSlFlag;
    }
    
    public void setScfcInclSlFlag(String scfcInclSlFlag) {
        this.scfcInclSlFlag = scfcInclSlFlag;
    }
    
    public Date getNextSchedFcDate() {
        return this.nextSchedFcDate;
    }
    
    public void setNextSchedFcDate(Date nextSchedFcDate) {
        this.nextSchedFcDate = nextSchedFcDate;
    }
    
    public String getPrtAllTrustFlag() {
        return this.prtAllTrustFlag;
    }
    
    public void setPrtAllTrustFlag(String prtAllTrustFlag) {
        this.prtAllTrustFlag = prtAllTrustFlag;
    }
    
    public Date getCreateDate() {
        return this.createDate;
    }
    
    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }
    
    public Date getFcFreqEffDt() {
        return this.fcFreqEffDt;
    }
    
    public void setFcFreqEffDt(Date fcFreqEffDt) {
        this.fcFreqEffDt = fcFreqEffDt;
    }
    
    public String getFcFreqLogonId() {
        return this.fcFreqLogonId;
    }
    
    public void setFcFreqLogonId(String fcFreqLogonId) {
        this.fcFreqLogonId = fcFreqLogonId;
    }
    
    public int getEquityCycNo() {
        return this.equityCycNo;
    }
    
    public void setEquityCycNo(int equityCycNo) {
        this.equityCycNo = equityCycNo;
    }
    
    public Date getEquityDate() {
        return this.equityDate;
    }
    
    public void setEquityDate(Date equityDate) {
        this.equityDate = equityDate;
    }
    
    public int getInvnCycNo() {
        return this.invnCycNo;
    }
    
    public void setInvnCycNo(int invnCycNo) {
        this.invnCycNo = invnCycNo;
    }
    
    public Date getInvnDate() {
        return this.invnDate;
    }
    
    public void setInvnDate(Date invnDate) {
        this.invnDate = invnDate;
    }
    
    public int getRecclAmt() {
        return this.recclAmt;
    }
    
    public void setRecclAmt(int recclAmt) {
        this.recclAmt = recclAmt;
    }
    
    public String getScfcTrkNonFlag() {
        return this.scfcTrkNonFlag;
    }
    
    public void setScfcTrkNonFlag(String scfcTrkNonFlag) {
        this.scfcTrkNonFlag = scfcTrkNonFlag;
    }
    
    public Date getScfcSent01Date() {
        return this.scfcSent01Date;
    }
    
    public void setScfcSent01Date(Date scfcSent01Date) {
        this.scfcSent01Date = scfcSent01Date;
    }
    
    public Date getScfcSent02Date() {
        return this.scfcSent02Date;
    }
    
    public void setScfcSent02Date(Date scfcSent02Date) {
        this.scfcSent02Date = scfcSent02Date;
    }
    
    public String getPblFundDayInd() {
        return this.pblFundDayInd;
    }
    
    public void setPblFundDayInd(String pblFundDayInd) {
        this.pblFundDayInd = pblFundDayInd;
    }
    
    public String getPcsPurgeFlag() {
        return this.pcsPurgeFlag;
    }
    
    public void setPcsPurgeFlag(String pcsPurgeFlag) {
        this.pcsPurgeFlag = pcsPurgeFlag;
    }
    
    public String getOvlnTolerFlag() {
        return this.ovlnTolerFlag;
    }
    
    public void setOvlnTolerFlag(String ovlnTolerFlag) {
        this.ovlnTolerFlag = ovlnTolerFlag;
    }
    
    public int getApprPurgeMinAmt() {
        return this.apprPurgeMinAmt;
    }
    
    public void setApprPurgeMinAmt(int apprPurgeMinAmt) {
        this.apprPurgeMinAmt = apprPurgeMinAmt;
    }
    
    public int getApprPurgeMinPct() {
        return this.apprPurgeMinPct;
    }
    
    public void setApprPurgeMinPct(int apprPurgeMinPct) {
        this.apprPurgeMinPct = apprPurgeMinPct;
    }
    
    public int getApprPurgeDayCnt() {
        return this.apprPurgeDayCnt;
    }
    
    public void setApprPurgeDayCnt(int apprPurgeDayCnt) {
        this.apprPurgeDayCnt = apprPurgeDayCnt;
    }
    
    public Date getCredHoldDate() {
        return this.credHoldDate;
    }
    
    public void setCredHoldDate(Date credHoldDate) {
        this.credHoldDate = credHoldDate;
    }
    
    public String getOvrdFundDateInd() {
        return this.ovrdFundDateInd;
    }
    
    public void setOvrdFundDateInd(String ovrdFundDateInd) {
        this.ovrdFundDateInd = ovrdFundDateInd;
    }
    
    public String getNoTrustDtlFlag() {
        return this.noTrustDtlFlag;
    }
    
    public void setNoTrustDtlFlag(String noTrustDtlFlag) {
        this.noTrustDtlFlag = noTrustDtlFlag;
    }
    
    public String getPurchContactName() {
        return this.purchContactName;
    }
    
    public void setPurchContactName(String purchContactName) {
        this.purchContactName = purchContactName;
    }
    
    public String getPurchPhoneNo() {
        return this.purchPhoneNo;
    }
    
    public void setPurchPhoneNo(String purchPhoneNo) {
        this.purchPhoneNo = purchPhoneNo;
    }
    
    public String getPurchFaxNo() {
        return this.purchFaxNo;
    }
    
    public void setPurchFaxNo(String purchFaxNo) {
        this.purchFaxNo = purchFaxNo;
    }
    
    public String getAdminLogonId() {
        return this.adminLogonId;
    }
    
    public void setAdminLogonId(String adminLogonId) {
        this.adminLogonId = adminLogonId;
    }
    
    public String getSerialNoFlag() {
        return this.serialNoFlag;
    }
    
    public void setSerialNoFlag(String serialNoFlag) {
        this.serialNoFlag = serialNoFlag;
    }
    
    public Date getPurchUpdDate() {
        return this.purchUpdDate;
    }
    
    public void setPurchUpdDate(Date purchUpdDate) {
        this.purchUpdDate = purchUpdDate;
    }
    
    public String getPurchUpdLogonId() {
        return this.purchUpdLogonId;
    }
    
    public void setPurchUpdLogonId(String purchUpdLogonId) {
        this.purchUpdLogonId = purchUpdLogonId;
    }
    
    public String getExplodeInd() {
        return this.explodeInd;
    }
    
    public void setExplodeInd(String explodeInd) {
        this.explodeInd = explodeInd;
    }
    
    public String getFilingTypeCode() {
        return this.filingTypeCode;
    }
    
    public void setFilingTypeCode(String filingTypeCode) {
        this.filingTypeCode = filingTypeCode;
    }
    
    public int getEpdDelayDaysCnt() {
        return this.epdDelayDaysCnt;
    }
    
    public void setEpdDelayDaysCnt(int epdDelayDaysCnt) {
        this.epdDelayDaysCnt = epdDelayDaysCnt;
    }
    
    public String getSalesZipCode() {
        return this.salesZipCode;
    }
    
    public void setSalesZipCode(String salesZipCode) {
        this.salesZipCode = salesZipCode;
    }
    
    public Date getFinStEntryDate() {
        return this.finStEntryDate;
    }
    
    public void setFinStEntryDate(Date finStEntryDate) {
        this.finStEntryDate = finStEntryDate;
    }
    
    public Date getTrstSpecPrtDate() {
        return this.trstSpecPrtDate;
    }
    
    public void setTrstSpecPrtDate(Date trstSpecPrtDate) {
        this.trstSpecPrtDate = trstSpecPrtDate;
    }
    
    public String getTrstSpecPrtInd() {
        return this.trstSpecPrtInd;
    }
    
    public void setTrstSpecPrtInd(String trstSpecPrtInd) {
        this.trstSpecPrtInd = trstSpecPrtInd;
    }
    
    public String getCredPrtFlag() {
        return this.credPrtFlag;
    }
    
    public void setCredPrtFlag(String credPrtFlag) {
        this.credPrtFlag = credPrtFlag;
    }
    
    public String getCredPrtFreqCode() {
        return this.credPrtFreqCode;
    }
    
    public void setCredPrtFreqCode(String credPrtFreqCode) {
        this.credPrtFreqCode = credPrtFreqCode;
    }
    
    public String getStProvCode() {
        return this.stProvCode;
    }
    
    public void setStProvCode(String stProvCode) {
        this.stProvCode = stProvCode;
    }
    
    public String getZipPostalCode() {
        return this.zipPostalCode;
    }
    
    public void setZipPostalCode(String zipPostalCode) {
        this.zipPostalCode = zipPostalCode;
    }
    
    public String getCountryCode() {
        return this.countryCode;
    }
    
    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }
    
    public String getAddr1Name() {
        return this.addr1Name;
    }
    
    public void setAddr1Name(String addr1Name) {
        this.addr1Name = addr1Name;
    }
    
    public String getAddr2Name() {
        return this.addr2Name;
    }
    
    public void setAddr2Name(String addr2Name) {
        this.addr2Name = addr2Name;
    }
    
    public String getCityName() {
        return this.cityName;
    }
    
    public void setCityName(String cityName) {
        this.cityName = cityName;
    }
    
    public String getCountyName() {
        return this.countyName;
    }
    
    public void setCountyName(String countyName) {
        this.countyName = countyName;
    }
    
    public String getLegalName() {
        return this.legalName;
    }
    
    public void setLegalName(String legalName) {
        this.legalName = legalName;
    }
    
    public String getDbaName() {
        return this.dbaName;
    }
    
    public void setDbaName(String dbaName) {
        this.dbaName = dbaName;
    }
    
    public String getContactName() {
        return this.contactName;
    }
    
    public void setContactName(String contactName) {
        this.contactName = contactName;
    }
    
    public String getContactTitle() {
        return this.contactTitle;
    }
    
    public void setContactTitle(String contactTitle) {
        this.contactTitle = contactTitle;
    }
    
    public String getLocDesc() {
        return this.locDesc;
    }
    
    public void setLocDesc(String locDesc) {
        this.locDesc = locDesc;
    }
    
    public int getCashPriorityNo() {
        return this.cashPriorityNo;
    }
    
    public void setCashPriorityNo(int cashPriorityNo) {
        this.cashPriorityNo = cashPriorityNo;
    }
    
    public String getXcSourceFlag() {
        return this.xcSourceFlag;
    }
    
    public void setXcSourceFlag(String xcSourceFlag) {
        this.xcSourceFlag = xcSourceFlag;
    }
    
    public String getXcEligibleFlag() {
        return this.xcEligibleFlag;
    }
    
    public void setXcEligibleFlag(String xcEligibleFlag) {
        this.xcEligibleFlag = xcEligibleFlag;
    }
    
    public String getXcCredStatCode() {
        return this.xcCredStatCode;
    }
    
    public void setXcCredStatCode(String xcCredStatCode) {
        this.xcCredStatCode = xcCredStatCode;
    }
    
    public String getSlWeekdayFlag() {
        return this.slWeekdayFlag;
    }
    
    public void setSlWeekdayFlag(String slWeekdayFlag) {
        this.slWeekdayFlag = slWeekdayFlag;
    }
    
    public String getPrtCredMemoInd() {
        return this.prtCredMemoInd;
    }
    
    public void setPrtCredMemoInd(String prtCredMemoInd) {
        this.prtCredMemoInd = prtCredMemoInd;
    }
    
    public String getCmApplyInd() {
        return this.cmApplyInd;
    }
    
    public void setCmApplyInd(String cmApplyInd) {
        this.cmApplyInd = cmApplyInd;
    }
    
    public String getCmFundFreqCode() {
        return this.cmFundFreqCode;
    }
    
    public void setCmFundFreqCode(String cmFundFreqCode) {
        this.cmFundFreqCode = cmFundFreqCode;
    }
    
    public String getCmFundDayCode() {
        return this.cmFundDayCode;
    }
    
    public void setCmFundDayCode(String cmFundDayCode) {
        this.cmFundDayCode = cmFundDayCode;
    }
    
    public String getPrtCurrencyFlag() {
        return this.prtCurrencyFlag;
    }
    
    public void setPrtCurrencyFlag(String prtCurrencyFlag) {
        this.prtCurrencyFlag = prtCurrencyFlag;
    }
    
    public String getAttrnOvrdFlag() {
        return this.attrnOvrdFlag;
    }
    
    public void setAttrnOvrdFlag(String attrnOvrdFlag) {
        this.attrnOvrdFlag = attrnOvrdFlag;
    }
    
    public Date getAttrnExpDate() {
        return this.attrnExpDate;
    }
    
    public void setAttrnExpDate(Date attrnExpDate) {
        this.attrnExpDate = attrnExpDate;
    }
    
    public String getShortName() {
        return this.shortName;
    }
    
    public void setShortName(String shortName) {
        this.shortName = shortName;
    }
    
    public String getTrstSpecPrtCode() {
        return this.trstSpecPrtCode;
    }
    
    public void setTrstSpecPrtCode(String trstSpecPrtCode) {
        this.trstSpecPrtCode = trstSpecPrtCode;
    }
    
    public String getEmailAddrName() {
        return this.emailAddrName;
    }
    
    public void setEmailAddrName(String emailAddrName) {
        this.emailAddrName = emailAddrName;
    }
    
    public String getPdeSortSeqCode() {
        return this.pdeSortSeqCode;
    }
    
    public void setPdeSortSeqCode(String pdeSortSeqCode) {
        this.pdeSortSeqCode = pdeSortSeqCode;
    }
    
    public String getMstrInvPrtCode() {
        return this.mstrInvPrtCode;
    }
    
    public void setMstrInvPrtCode(String mstrInvPrtCode) {
        this.mstrInvPrtCode = mstrInvPrtCode;
    }
    
    public String getMstrInvReqInd() {
        return this.mstrInvReqInd;
    }
    
    public void setMstrInvReqInd(String mstrInvReqInd) {
        this.mstrInvReqInd = mstrInvReqInd;
    }
    
    public String getTrustEdiCode() {
        return this.trustEdiCode;
    }
    
    public void setTrustEdiCode(String trustEdiCode) {
        this.trustEdiCode = trustEdiCode;
    }
    
    public String getVatRgstrNo() {
        return this.vatRgstrNo;
    }
    
    public void setVatRgstrNo(String vatRgstrNo) {
        this.vatRgstrNo = vatRgstrNo;
    }
    
    public String getVatElectionInd() {
        return this.vatElectionInd;
    }
    
    public void setVatElectionInd(String vatElectionInd) {
        this.vatElectionInd = vatElectionInd;
    }
    
    public String getLanguageCode() {
        return this.languageCode;
    }
    
    public void setLanguageCode(String languageCode) {
        this.languageCode = languageCode;
    }
    
    public String getHoldNegPblInd() {
        return this.holdNegPblInd;
    }
    
    public void setHoldNegPblInd(String holdNegPblInd) {
        this.holdNegPblInd = holdNegPblInd;
    }
    
    public String getFaxStubsFlag() {
        return this.faxStubsFlag;
    }
    
    public void setFaxStubsFlag(String faxStubsFlag) {
        this.faxStubsFlag = faxStubsFlag;
    }
    
    public String getPolCredMemoFlag() {
        return this.polCredMemoFlag;
    }
    
    public void setPolCredMemoFlag(String polCredMemoFlag) {
        this.polCredMemoFlag = polCredMemoFlag;
    }
    
    public int getEpdGraceDaysCnt() {
        return this.epdGraceDaysCnt;
    }
    
    public void setEpdGraceDaysCnt(int epdGraceDaysCnt) {
        this.epdGraceDaysCnt = epdGraceDaysCnt;
    }
    
    public String getEpdFundFreqCode() {
        return this.epdFundFreqCode;
    }
    
    public void setEpdFundFreqCode(String epdFundFreqCode) {
        this.epdFundFreqCode = epdFundFreqCode;
    }
    
    public int getEpdFundDaysCnt() {
        return this.epdFundDaysCnt;
    }
    
    public void setEpdFundDaysCnt(int epdFundDaysCnt) {
        this.epdFundDaysCnt = epdFundDaysCnt;
    }
    
    public String getCredClaimFlag() {
        return this.credClaimFlag;
    }
    
    public void setCredClaimFlag(String credClaimFlag) {
        this.credClaimFlag = credClaimFlag;
    }
    
    public int getCredClaimDayCnt() {
        return this.credClaimDayCnt;
    }
    
    public void setCredClaimDayCnt(int credClaimDayCnt) {
        this.credClaimDayCnt = credClaimDayCnt;
    }
    
    public int getRmbCmDiscPgmNo() {
        return this.rmbCmDiscPgmNo;
    }
    
    public void setRmbCmDiscPgmNo(int rmbCmDiscPgmNo) {
        this.rmbCmDiscPgmNo = rmbCmDiscPgmNo;
    }
    
    public String getSkuIdFlag() {
        return this.skuIdFlag;
    }
    
    public void setSkuIdFlag(String skuIdFlag) {
        this.skuIdFlag = skuIdFlag;
    }
    
    public String getPrtCredNoteInd() {
        return this.prtCredNoteInd;
    }
    
    public void setPrtCredNoteInd(String prtCredNoteInd) {
        this.prtCredNoteInd = prtCredNoteInd;
    }
    
    public String getUccOrgNo() {
        return this.uccOrgNo;
    }
    
    public void setUccOrgNo(String uccOrgNo) {
        this.uccOrgNo = uccOrgNo;
    }
    
    public String getUccOrgSt() {
        return this.uccOrgSt;
    }
    
    public void setUccOrgSt(String uccOrgSt) {
        this.uccOrgSt = uccOrgSt;
    }
    
    public String getSystemCreateFlag() {
        return this.systemCreateFlag;
    }
    
    public void setSystemCreateFlag(String systemCreateFlag) {
        this.systemCreateFlag = systemCreateFlag;
    }
    
    public String getCurrencyCode() {
        return this.currencyCode;
    }
    
    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }
    
    public String getAutoCashFlag() {
        return this.autoCashFlag;
    }
    
    public void setAutoCashFlag(String autoCashFlag) {
        this.autoCashFlag = autoCashFlag;
    }
    
    public String getIncomplCashFlag() {
        return this.incomplCashFlag;
    }
    
    public void setIncomplCashFlag(String incomplCashFlag) {
        this.incomplCashFlag = incomplCashFlag;
    }
    
    public int getPodPresDayCnt() {
        return this.podPresDayCnt;
    }
    
    public void setPodPresDayCnt(int podPresDayCnt) {
        this.podPresDayCnt = podPresDayCnt;
    }
    
    public String getPodPresDateFlag() {
        return this.podPresDateFlag;
    }
    
    public void setPodPresDateFlag(String podPresDateFlag) {
        this.podPresDateFlag = podPresDateFlag;
    }
    
    public String getTrueUpEarlyFlag() {
        return this.trueUpEarlyFlag;
    }
    
    public void setTrueUpEarlyFlag(String trueUpEarlyFlag) {
        this.trueUpEarlyFlag = trueUpEarlyFlag;
    }
    
    public String getTrueUpLateFlag() {
        return this.trueUpLateFlag;
    }
    
    public void setTrueUpLateFlag(String trueUpLateFlag) {
        this.trueUpLateFlag = trueUpLateFlag;
    }
    
    public int getTdfRiskPct() {
        return this.tdfRiskPct;
    }
    
    public void setTdfRiskPct(int tdfRiskPct) {
        this.tdfRiskPct = tdfRiskPct;
    }
    
    public int getRiskFeeRate() {
        return this.riskFeeRate;
    }
    
    public void setRiskFeeRate(int riskFeeRate) {
        this.riskFeeRate = riskFeeRate;
    }
    
    public String getRiskFeeFiuFlag() {
        return this.riskFeeFiuFlag;
    }
    
    public void setRiskFeeFiuFlag(String riskFeeFiuFlag) {
        this.riskFeeFiuFlag = riskFeeFiuFlag;
    }
    
    public String getPortTypeCode() {
        return this.portTypeCode;
    }
    
    public void setPortTypeCode(String portTypeCode) {
        this.portTypeCode = portTypeCode;
    }
    
    public String getServTrustFlag() {
        return this.servTrustFlag;
    }
    
    public void setServTrustFlag(String servTrustFlag) {
        this.servTrustFlag = servTrustFlag;
    }
    
    public int getLossReserve2Rate() {
        return this.lossReserve2Rate;
    }
    
    public void setLossReserve2Rate(int lossReserve2Rate) {
        this.lossReserve2Rate = lossReserve2Rate;
    }
    
    public int getGrossupPct() {
        return this.grossupPct;
    }
    
    public void setGrossupPct(int grossupPct) {
        this.grossupPct = grossupPct;
    }
    
    public String getAdbDiscInd() {
        return this.adbDiscInd;
    }
    
    public void setAdbDiscInd(String adbDiscInd) {
        this.adbDiscInd = adbDiscInd;
    }
    
    public int getDistFundOsAmt() {
        return this.distFundOsAmt;
    }
    
    public void setDistFundOsAmt(int distFundOsAmt) {
        this.distFundOsAmt = distFundOsAmt;
    }
    
    public String getDlrChrgTdfFlag() {
        return this.dlrChrgTdfFlag;
    }
    
    public void setDlrChrgTdfFlag(String dlrChrgTdfFlag) {
        this.dlrChrgTdfFlag = dlrChrgTdfFlag;
    }
    
    public String getMiscId() {
        return this.miscId;
    }
    
    public void setMiscId(String miscId) {
        this.miscId = miscId;
    }
    
    public String getServFeeFreqInd() {
        return this.servFeeFreqInd;
    }
    
    public void setServFeeFreqInd(String servFeeFreqInd) {
        this.servFeeFreqInd = servFeeFreqInd;
    }
    
    public int getCollFeeRate() {
        return this.collFeeRate;
    }
    
    public void setCollFeeRate(int collFeeRate) {
        this.collFeeRate = collFeeRate;
    }
    
    public int getFundHoldPct() {
        return this.fundHoldPct;
    }
    
    public void setFundHoldPct(int fundHoldPct) {
        this.fundHoldPct = fundHoldPct;
    }
    
    public int getFundLimitAmt() {
        return this.fundLimitAmt;
    }
    
    public void setFundLimitAmt(int fundLimitAmt) {
        this.fundLimitAmt = fundLimitAmt;
    }
    
    public int getServFeeBranchNo() {
        return this.servFeeBranchNo;
    }
    
    public void setServFeeBranchNo(int servFeeBranchNo) {
        this.servFeeBranchNo = servFeeBranchNo;
    }
    
    public String getTrueUpIntFlag() {
        return this.trueUpIntFlag;
    }
    
    public void setTrueUpIntFlag(String trueUpIntFlag) {
        this.trueUpIntFlag = trueUpIntFlag;
    }
    
    public int getCurtGraceDayCnt() {
        return this.curtGraceDayCnt;
    }
    
    public void setCurtGraceDayCnt(int curtGraceDayCnt) {
        this.curtGraceDayCnt = curtGraceDayCnt;
    }
    
    public int getAnrMonthsCnt() {
        return this.anrMonthsCnt;
    }
    
    public void setAnrMonthsCnt(int anrMonthsCnt) {
        this.anrMonthsCnt = anrMonthsCnt;
    }
    
    public Date getMstrAgreeDate() {
        return this.mstrAgreeDate;
    }
    
    public void setMstrAgreeDate(Date mstrAgreeDate) {
        this.mstrAgreeDate = mstrAgreeDate;
    }
    
    public String getCmApplyRuleCode() {
        return this.cmApplyRuleCode;
    }
    
    public void setCmApplyRuleCode(String cmApplyRuleCode) {
        this.cmApplyRuleCode = cmApplyRuleCode;
    }
    
    public String getCmApplyExcpCode() {
        return this.cmApplyExcpCode;
    }
    
    public void setCmApplyExcpCode(String cmApplyExcpCode) {
        this.cmApplyExcpCode = cmApplyExcpCode;
    }
    
    public String getScrtzEligCd() {
        return this.scrtzEligCd;
    }
    
    public void setScrtzEligCd(String scrtzEligCd) {
        this.scrtzEligCd = scrtzEligCd;
    }
    
    public String getSecuredFl() {
        return this.securedFl;
    }
    
    public void setSecuredFl(String securedFl) {
        this.securedFl = securedFl;
    }
    
    public Date getScrtzEffDate() {
        return this.scrtzEffDate;
    }
    
    public void setScrtzEffDate(Date scrtzEffDate) {
        this.scrtzEffDate = scrtzEffDate;
    }
    
    public int getScrtzParticRt() {
        return this.scrtzParticRt;
    }
    
    public void setScrtzParticRt(int scrtzParticRt) {
        this.scrtzParticRt = scrtzParticRt;
    }
    
    public String getScrtzPoolId() {
        return this.scrtzPoolId;
    }
    
    public void setScrtzPoolId(String scrtzPoolId) {
        this.scrtzPoolId = scrtzPoolId;
    }
    
    public String getAdbBillMethCode() {
        return this.adbBillMethCode;
    }
    
    public void setAdbBillMethCode(String adbBillMethCode) {
        this.adbBillMethCode = adbBillMethCode;
    }
    
    public String getLateFeeBillInd() {
        return this.lateFeeBillInd;
    }
    
    public void setLateFeeBillInd(String lateFeeBillInd) {
        this.lateFeeBillInd = lateFeeBillInd;
    }
    
    public String getEdocsInd() {
        return this.edocsInd;
    }
    
    public void setEdocsInd(String edocsInd) {
        this.edocsInd = edocsInd;
    }
    
    public Date getEdocsStartDate() {
        return this.edocsStartDate;
    }
    
    public void setEdocsStartDate(Date edocsStartDate) {
        this.edocsStartDate = edocsStartDate;
    }
    
    public Date getEdocsPrtEndDate() {
        return this.edocsPrtEndDate;
    }
    
    public void setEdocsPrtEndDate(Date edocsPrtEndDate) {
        this.edocsPrtEndDate = edocsPrtEndDate;
    }
    
    public int getAuxCarrNo() {
        return this.auxCarrNo;
    }
    
    public void setAuxCarrNo(int auxCarrNo) {
        this.auxCarrNo = auxCarrNo;
    }
    
    public String getCurtPrepaidFlag() {
        return this.curtPrepaidFlag;
    }
    
    public void setCurtPrepaidFlag(String curtPrepaidFlag) {
        this.curtPrepaidFlag = curtPrepaidFlag;
    }
    
    public String getStmtSortCode() {
        return this.stmtSortCode;
    }
    
    public void setStmtSortCode(String stmtSortCode) {
        this.stmtSortCode = stmtSortCode;
    }
    
    public String getStmtPageBrkFlag() {
        return this.stmtPageBrkFlag;
    }
    
    public void setStmtPageBrkFlag(String stmtPageBrkFlag) {
        this.stmtPageBrkFlag = stmtPageBrkFlag;
    }
    
    public String getRecvCmPrtFlag() {
        return this.recvCmPrtFlag;
    }
    
    public void setRecvCmPrtFlag(String recvCmPrtFlag) {
        this.recvCmPrtFlag = recvCmPrtFlag;
    }
    
    public String getSlStmtPrtFl() {
        return this.slStmtPrtFl;
    }
    
    public void setSlStmtPrtFl(String slStmtPrtFl) {
        this.slStmtPrtFl = slStmtPrtFl;
    }
    
    public int getEpdCbgnDay1No() {
        return this.epdCbgnDay1No;
    }
    
    public void setEpdCbgnDay1No(int epdCbgnDay1No) {
        this.epdCbgnDay1No = epdCbgnDay1No;
    }
    
    public int getEpdCbgnDay2No() {
        return this.epdCbgnDay2No;
    }
    
    public void setEpdCbgnDay2No(int epdCbgnDay2No) {
        this.epdCbgnDay2No = epdCbgnDay2No;
    }
    
    public int getEpdCbgnDay3No() {
        return this.epdCbgnDay3No;
    }
    
    public void setEpdCbgnDay3No(int epdCbgnDay3No) {
        this.epdCbgnDay3No = epdCbgnDay3No;
    }
    
    public int getEpdCbgnDay4No() {
        return this.epdCbgnDay4No;
    }
    
    public void setEpdCbgnDay4No(int epdCbgnDay4No) {
        this.epdCbgnDay4No = epdCbgnDay4No;
    }
    
    public int getEpdCdueDay1No() {
        return this.epdCdueDay1No;
    }
    
    public void setEpdCdueDay1No(int epdCdueDay1No) {
        this.epdCdueDay1No = epdCdueDay1No;
    }
    
    public int getEpdCdueDay2No() {
        return this.epdCdueDay2No;
    }
    
    public void setEpdCdueDay2No(int epdCdueDay2No) {
        this.epdCdueDay2No = epdCdueDay2No;
    }
    
    public int getEpdCdueDay3No() {
        return this.epdCdueDay3No;
    }
    
    public void setEpdCdueDay3No(int epdCdueDay3No) {
        this.epdCdueDay3No = epdCdueDay3No;
    }
    
    public int getEpdCdueDay4No() {
        return this.epdCdueDay4No;
    }
    
    public void setEpdCdueDay4No(int epdCdueDay4No) {
        this.epdCdueDay4No = epdCdueDay4No;
    }
    
    public int getEpdCendDay1No() {
        return this.epdCendDay1No;
    }
    
    public void setEpdCendDay1No(int epdCendDay1No) {
        this.epdCendDay1No = epdCendDay1No;
    }
    
    public int getEpdCendDay2No() {
        return this.epdCendDay2No;
    }
    
    public void setEpdCendDay2No(int epdCendDay2No) {
        this.epdCendDay2No = epdCendDay2No;
    }
    
    public int getEpdCendDay3No() {
        return this.epdCendDay3No;
    }
    
    public void setEpdCendDay3No(int epdCendDay3No) {
        this.epdCendDay3No = epdCendDay3No;
    }
    
    public int getEpdCendDay4No() {
        return this.epdCendDay4No;
    }
    
    public void setEpdCendDay4No(int epdCendDay4No) {
        this.epdCendDay4No = epdCendDay4No;
    }
    
    public String getRefundDistFdFl() {
        return this.refundDistFdFl;
    }
    
    public void setRefundDistFdFl(String refundDistFdFl) {
        this.refundDistFdFl = refundDistFdFl;
    }
    
    public int getCashAlgCode() {
        return this.cashAlgCode;
    }
    
    public void setCashAlgCode(int cashAlgCode) {
        this.cashAlgCode = cashAlgCode;
    }
    
    public String getAutoStlmntFl() {
        return this.autoStlmntFl;
    }
    
    public void setAutoStlmntFl(String autoStlmntFl) {
        this.autoStlmntFl = autoStlmntFl;
    }
    
    public String getTranStmtFrmtCd() {
        return this.tranStmtFrmtCd;
    }
    
    public void setTranStmtFrmtCd(String tranStmtFrmtCd) {
        this.tranStmtFrmtCd = tranStmtFrmtCd;
    }
    
    public String getDefNoPayFlag() {
        return this.defNoPayFlag;
    }
    
    public void setDefNoPayFlag(String defNoPayFlag) {
        this.defNoPayFlag = defNoPayFlag;
    }
    
    public String getDefComposRtFlag() {
        return this.defComposRtFlag;
    }
    
    public void setDefComposRtFlag(String defComposRtFlag) {
        this.defComposRtFlag = defComposRtFlag;
    }
    
    public int getDefBankCode() {
        return this.defBankCode;
    }
    
    public void setDefBankCode(int defBankCode) {
        this.defBankCode = defBankCode;
    }
    
    public String getDefTypeInd() {
        return this.defTypeInd;
    }
    
    public void setDefTypeInd(String defTypeInd) {
        this.defTypeInd = defTypeInd;
    }
    
    public int getDefMinprimeRate() {
        return this.defMinprimeRate;
    }
    
    public void setDefMinprimeRate(int defMinprimeRate) {
        this.defMinprimeRate = defMinprimeRate;
    }
    
    public int getDefMaxprimeRate() {
        return this.defMaxprimeRate;
    }
    
    public void setDefMaxprimeRate(int defMaxprimeRate) {
        this.defMaxprimeRate = defMaxprimeRate;
    }
    
    public int getStraightRate() {
        return this.straightRate;
    }
    
    public void setStraightRate(int straightRate) {
        this.straightRate = straightRate;
    }
    
    public String getPrtNoteStmtInd() {
        return this.prtNoteStmtInd;
    }
    
    public void setPrtNoteStmtInd(String prtNoteStmtInd) {
        this.prtNoteStmtInd = prtNoteStmtInd;
    }
    
    public String getSegmentCd() {
        return this.segmentCd;
    }
    
    public void setSegmentCd(String segmentCd) {
        this.segmentCd = segmentCd;
    }
    
    public String getHighRiskFl() {
        return this.highRiskFl;
    }
    
    public void setHighRiskFl(String highRiskFl) {
        this.highRiskFl = highRiskFl;
    }
    
    public String getKmvId() {
        return this.kmvId;
    }
    
    public void setKmvId(String kmvId) {
        this.kmvId = kmvId;
    }
    
    public String getExtIdTypeCd() {
        return this.extIdTypeCd;
    }
    
    public void setExtIdTypeCd(String extIdTypeCd) {
        this.extIdTypeCd = extIdTypeCd;
    }
    
    public String getExtIdNo() {
        return this.extIdNo;
    }
    
    public void setExtIdNo(String extIdNo) {
        this.extIdNo = extIdNo;
    }
    
    public String getOfstTypeCd() {
        return this.ofstTypeCd;
    }
    
    public void setOfstTypeCd(String ofstTypeCd) {
        this.ofstTypeCd = ofstTypeCd;
    }
    
    public String getLineDetailFl() {
        return this.lineDetailFl;
    }
    
    public void setLineDetailFl(String lineDetailFl) {
        this.lineDetailFl = lineDetailFl;
    }
    
    public String getFixpSkipMo01Fl() {
        return this.fixpSkipMo01Fl;
    }
    
    public void setFixpSkipMo01Fl(String fixpSkipMo01Fl) {
        this.fixpSkipMo01Fl = fixpSkipMo01Fl;
    }
    
    public String getFixpSkipMo02Fl() {
        return this.fixpSkipMo02Fl;
    }
    
    public void setFixpSkipMo02Fl(String fixpSkipMo02Fl) {
        this.fixpSkipMo02Fl = fixpSkipMo02Fl;
    }
    
    public String getFixpSkipMo03Fl() {
        return this.fixpSkipMo03Fl;
    }
    
    public void setFixpSkipMo03Fl(String fixpSkipMo03Fl) {
        this.fixpSkipMo03Fl = fixpSkipMo03Fl;
    }
    
    public String getFixpSkipMo04Fl() {
        return this.fixpSkipMo04Fl;
    }
    
    public void setFixpSkipMo04Fl(String fixpSkipMo04Fl) {
        this.fixpSkipMo04Fl = fixpSkipMo04Fl;
    }
    
    public String getFixpSkipMo05Fl() {
        return this.fixpSkipMo05Fl;
    }
    
    public void setFixpSkipMo05Fl(String fixpSkipMo05Fl) {
        this.fixpSkipMo05Fl = fixpSkipMo05Fl;
    }
    
    public String getFixpSkipMo06Fl() {
        return this.fixpSkipMo06Fl;
    }
    
    public void setFixpSkipMo06Fl(String fixpSkipMo06Fl) {
        this.fixpSkipMo06Fl = fixpSkipMo06Fl;
    }
    
    public String getFixpSkipMo07Fl() {
        return this.fixpSkipMo07Fl;
    }
    
    public void setFixpSkipMo07Fl(String fixpSkipMo07Fl) {
        this.fixpSkipMo07Fl = fixpSkipMo07Fl;
    }
    
    public String getFixpSkipMo08Fl() {
        return this.fixpSkipMo08Fl;
    }
    
    public void setFixpSkipMo08Fl(String fixpSkipMo08Fl) {
        this.fixpSkipMo08Fl = fixpSkipMo08Fl;
    }
    
    public String getFixpSkipMo09Fl() {
        return this.fixpSkipMo09Fl;
    }
    
    public void setFixpSkipMo09Fl(String fixpSkipMo09Fl) {
        this.fixpSkipMo09Fl = fixpSkipMo09Fl;
    }
    
    public String getFixpSkipMo10Fl() {
        return this.fixpSkipMo10Fl;
    }
    
    public void setFixpSkipMo10Fl(String fixpSkipMo10Fl) {
        this.fixpSkipMo10Fl = fixpSkipMo10Fl;
    }
    
    public String getFixpSkipMo11Fl() {
        return this.fixpSkipMo11Fl;
    }
    
    public void setFixpSkipMo11Fl(String fixpSkipMo11Fl) {
        this.fixpSkipMo11Fl = fixpSkipMo11Fl;
    }
    
    public String getFixpSkipMo12Fl() {
        return this.fixpSkipMo12Fl;
    }
    
    public void setFixpSkipMo12Fl(String fixpSkipMo12Fl) {
        this.fixpSkipMo12Fl = fixpSkipMo12Fl;
    }
    
    public String getDistPrchEmailNm() {
        return this.distPrchEmailNm;
    }
    
    public void setDistPrchEmailNm(String distPrchEmailNm) {
        this.distPrchEmailNm = distPrchEmailNm;
    }
    
    public String getRstrFl() {
        return this.rstrFl;
    }
    
    public void setRstrFl(String rstrFl) {
        this.rstrFl = rstrFl;
    }
    
    public String getStarFl() {
        return this.starFl;
    }
    
    public void setStarFl(String starFl) {
        this.starFl = starFl;
    }
    
    public String getBnkrpFl() {
        return this.bnkrpFl;
    }
    
    public void setBnkrpFl(String bnkrpFl) {
        this.bnkrpFl = bnkrpFl;
    }
    
    public String getRegSweepFl() {
        return this.regSweepFl;
    }
    
    public void setRegSweepFl(String regSweepFl) {
        this.regSweepFl = regSweepFl;
    }
    
    public String getPutReasonCd() {
        return this.putReasonCd;
    }
    
    public void setPutReasonCd(String putReasonCd) {
        this.putReasonCd = putReasonCd;
    }
    
    public Date getPutReasonDt() {
        return this.putReasonDt;
    }
    
    public void setPutReasonDt(Date putReasonDt) {
        this.putReasonDt = putReasonDt;
    }
    
    public String getBankWatchFl() {
        return this.bankWatchFl;
    }
    
    public void setBankWatchFl(String bankWatchFl) {
        this.bankWatchFl = bankWatchFl;
    }
    
    public int getOvlnTolerRt() {
        return this.ovlnTolerRt;
    }
    
    public void setOvlnTolerRt(int ovlnTolerRt) {
        this.ovlnTolerRt = ovlnTolerRt;
    }
    
    public String getOtbHeldPayFl() {
        return this.otbHeldPayFl;
    }
    
    public void setOtbHeldPayFl(String otbHeldPayFl) {
        this.otbHeldPayFl = otbHeldPayFl;
    }
    
    public String getOtbUnapplCashFl() {
        return this.otbUnapplCashFl;
    }
    
    public void setOtbUnapplCashFl(String otbUnapplCashFl) {
        this.otbUnapplCashFl = otbUnapplCashFl;
    }
    
    public String getOtbUnidCashFl() {
        return this.otbUnidCashFl;
    }
    
    public void setOtbUnidCashFl(String otbUnidCashFl) {
        this.otbUnidCashFl = otbUnidCashFl;
    }
    
    public String getOtbDefFl() {
        return this.otbDefFl;
    }
    
    public void setOtbDefFl(String otbDefFl) {
        this.otbDefFl = otbDefFl;
    }
    
    public int getPcsDefApprAmt() {
        return this.pcsDefApprAmt;
    }
    
    public void setPcsDefApprAmt(int pcsDefApprAmt) {
        this.pcsDefApprAmt = pcsDefApprAmt;
    }
    
    public String getOtbExpoExclInd() {
        return this.otbExpoExclInd;
    }
    
    public void setOtbExpoExclInd(String otbExpoExclInd) {
        this.otbExpoExclInd = otbExpoExclInd;
    }
    
    public Date getFinanStmtInrmDt() {
        return this.finanStmtInrmDt;
    }
    
    public void setFinanStmtInrmDt(Date finanStmtInrmDt) {
        this.finanStmtInrmDt = finanStmtInrmDt;
    }
    
    public int getFinanStmtInrmCt() {
        return this.finanStmtInrmCt;
    }
    
    public void setFinanStmtInrmCt(int finanStmtInrmCt) {
        this.finanStmtInrmCt = finanStmtInrmCt;
    }
    
    public Date getFinanStmtExtnDt() {
        return this.finanStmtExtnDt;
    }
    
    public void setFinanStmtExtnDt(Date finanStmtExtnDt) {
        this.finanStmtExtnDt = finanStmtExtnDt;
    }
    
    public String getPublicPrivInd() {
        return this.publicPrivInd;
    }
    
    public void setPublicPrivInd(String publicPrivInd) {
        this.publicPrivInd = publicPrivInd;
    }
    
    public Date getLastRevExtnDt() {
        return this.lastRevExtnDt;
    }
    
    public void setLastRevExtnDt(Date lastRevExtnDt) {
        this.lastRevExtnDt = lastRevExtnDt;
    }
    
    public String getFinanStmtQualCd() {
        return this.finanStmtQualCd;
    }
    
    public void setFinanStmtQualCd(String finanStmtQualCd) {
        this.finanStmtQualCd = finanStmtQualCd;
    }
    
    public String getEndUserNm() {
        return this.endUserNm;
    }
    
    public void setEndUserNm(String endUserNm) {
        this.endUserNm = endUserNm;
    }
    
    public String getMraCustId() {
        return this.mraCustId;
    }
    
    public void setMraCustId(String mraCustId) {
        this.mraCustId = mraCustId;
    }
    
    public String getDefOfstPrincFl() {
        return this.defOfstPrincFl;
    }
    
    public void setDefOfstPrincFl(String defOfstPrincFl) {
        this.defOfstPrincFl = defOfstPrincFl;
    }
    
    public String getDefActivPageFl() {
        return this.defActivPageFl;
    }
    
    public void setDefActivPageFl(String defActivPageFl) {
        this.defActivPageFl = defActivPageFl;
    }
    
    public String getUndwrGdlnExcpFl() {
        return this.undwrGdlnExcpFl;
    }
    
    public void setUndwrGdlnExcpFl(String undwrGdlnExcpFl) {
        this.undwrGdlnExcpFl = undwrGdlnExcpFl;
    }
    
    public String getDlrPreauthFl() {
        return this.dlrPreauthFl;
    }
    
    public void setDlrPreauthFl(String dlrPreauthFl) {
        this.dlrPreauthFl = dlrPreauthFl;
    }
    
    public String getPreauthAllowFl() {
        return this.preauthAllowFl;
    }
    
    public void setPreauthAllowFl(String preauthAllowFl) {
        this.preauthAllowFl = preauthAllowFl;
    }
    
    public String getEdiStmtTypeCd() {
        return this.ediStmtTypeCd;
    }
    
    public void setEdiStmtTypeCd(String ediStmtTypeCd) {
        this.ediStmtTypeCd = ediStmtTypeCd;
    }
    
    public String getEdiMfgDistFlag() {
        return this.ediMfgDistFlag;
    }
    
    public void setEdiMfgDistFlag(String ediMfgDistFlag) {
        this.ediMfgDistFlag = ediMfgDistFlag;
    }
    
    public String getEpdCmFl() {
        return this.epdCmFl;
    }
    
    public void setEpdCmFl(String epdCmFl) {
        this.epdCmFl = epdCmFl;
    }
    
    public String getMmtsCmprsFrmtFl() {
        return this.mmtsCmprsFrmtFl;
    }
    
    public void setMmtsCmprsFrmtFl(String mmtsCmprsFrmtFl) {
        this.mmtsCmprsFrmtFl = mmtsCmprsFrmtFl;
    }
    
    public String getPrpCrtWavRprFl() {
        return this.prpCrtWavRprFl;
    }
    
    public void setPrpCrtWavRprFl(String prpCrtWavRprFl) {
        this.prpCrtWavRprFl = prpCrtWavRprFl;
    }
    
    public int getComsBigBufferCt() {
        return this.comsBigBufferCt;
    }
    
    public void setComsBigBufferCt(int comsBigBufferCt) {
        this.comsBigBufferCt = comsBigBufferCt;
    }
    
    public String getDlvVrfctnFl() {
        return this.dlvVrfctnFl;
    }
    
    public void setDlvVrfctnFl(String dlvVrfctnFl) {
        this.dlvVrfctnFl = dlvVrfctnFl;
    }
    
    public String getRcpCrtTaxInvFl() {
        return this.rcpCrtTaxInvFl;
    }
    
    public void setRcpCrtTaxInvFl(String rcpCrtTaxInvFl) {
        this.rcpCrtTaxInvFl = rcpCrtTaxInvFl;
    }
    
    public String getOverseasDistFl() {
        return this.overseasDistFl;
    }
    
    public void setOverseasDistFl(String overseasDistFl) {
        this.overseasDistFl = overseasDistFl;
    }
    
    public String getMobileNo() {
        return this.mobileNo;
    }
    
    public void setMobileNo(String mobileNo) {
        this.mobileNo = mobileNo;
    }
    
    public String getServLvlCatCd() {
        return this.servLvlCatCd;
    }
    
    public void setServLvlCatCd(String servLvlCatCd) {
        this.servLvlCatCd = servLvlCatCd;
    }
    
    public String getPartyId() {
        return this.partyId;
    }
    
    public void setPartyId(String partyId) {
        this.partyId = partyId;
    }
    
    public int getTouchlessSyncId() {
        return this.touchlessSyncId;
    }
    
    public void setTouchlessSyncId(int touchlessSyncId) {
        this.touchlessSyncId = touchlessSyncId;
    }
    
    public Date getPriorChrgOffDt() {
        return this.priorChrgOffDt;
    }
    
    public void setPriorChrgOffDt(Date priorChrgOffDt) {
        this.priorChrgOffDt = priorChrgOffDt;
    }
    
    public int getTotChrgOffAmt() {
        return this.totChrgOffAmt;
    }
    
    public void setTotChrgOffAmt(int totChrgOffAmt) {
        this.totChrgOffAmt = totChrgOffAmt;
    }
    
    public String getDdOvrdSweepFl() {
        return this.ddOvrdSweepFl;
    }
    
    public void setDdOvrdSweepFl(String ddOvrdSweepFl) {
        this.ddOvrdSweepFl = ddOvrdSweepFl;
    }
    
    public String getPrevScrtzPoolId() {
        return this.prevScrtzPoolId;
    }
    
    public void setPrevScrtzPoolId(String prevScrtzPoolId) {
        this.prevScrtzPoolId = prevScrtzPoolId;
    }
    
    public Date getLastSpecRevDate() {
        return this.lastSpecRevDate;
    }
    
    public void setLastSpecRevDate(Date lastSpecRevDate) {
        this.lastSpecRevDate = lastSpecRevDate;
    }
    
    public String getLoanCommitFl() {
        return this.loanCommitFl;
    }
    
    public void setLoanCommitFl(String loanCommitFl) {
        this.loanCommitFl = loanCommitFl;
    }
    
    public Date getLoanCommitRenDt() {
        return this.loanCommitRenDt;
    }
    
    public void setLoanCommitRenDt(Date loanCommitRenDt) {
        this.loanCommitRenDt = loanCommitRenDt;
    }
    
    public String getCkDeliveryCd() {
        return this.ckDeliveryCd;
    }
    
    public void setCkDeliveryCd(String ckDeliveryCd) {
        this.ckDeliveryCd = ckDeliveryCd;
    }
    
    public int getWcisId() {
        return this.wcisId;
    }
    
    public void setWcisId(int wcisId) {
        this.wcisId = wcisId;
    }
    
    public String getNaicsCd() {
        return this.naicsCd;
    }
    
    public void setNaicsCd(String naicsCd) {
        this.naicsCd = naicsCd;
    }
    
    public String getCustNoteTx() {
        return this.custNoteTx;
    }
    
    public void setCustNoteTx(String custNoteTx) {
        this.custNoteTx = custNoteTx;
    }
    
    public String getNaceCd() {
        return this.naceCd;
    }
    
    public void setNaceCd(String naceCd) {
        this.naceCd = naceCd;
    }
    
    public String getCollatSegmentDs() {
        return this.collatSegmentDs;
    }
    
    public void setCollatSegmentDs(String collatSegmentDs) {
        this.collatSegmentDs = collatSegmentDs;
    }
    
    public Date getAuditTs() {
        return this.auditTs;
    }
    
    public void setAuditTs(Date auditTs) {
        this.auditTs = auditTs;
    }
    
    public String getAuditLogonId() {
        return this.auditLogonId;
    }
    
    public void setAuditLogonId(String auditLogonId) {
        this.auditLogonId = auditLogonId;
    }
    
    public String getAuditProcessTx() {
        return this.auditProcessTx;
    }
    
    public void setAuditProcessTx(String auditProcessTx) {
        this.auditProcessTx = auditProcessTx;
    }
    
    public String getAuditDeleteFl() {
        return this.auditDeleteFl;
    }
    
    public void setAuditDeleteFl(String auditDeleteFl) {
        this.auditDeleteFl = auditDeleteFl;
    }
    
    public int getNetRelianceAmt() {
        return this.netRelianceAmt;
    }
    
    public void setNetRelianceAmt(int netRelianceAmt) {
        this.netRelianceAmt = netRelianceAmt;
    }
    
    public Date getLastAmlRevDt() {
        return this.lastAmlRevDt;
    }
    
    public void setLastAmlRevDt(Date lastAmlRevDt) {
        this.lastAmlRevDt = lastAmlRevDt;
    }
    
    public String getEcrrCd() {
        return this.ecrrCd;
    }
    
    public void setEcrrCd(String ecrrCd) {
        this.ecrrCd = ecrrCd;
    }
    
    public String getCraCd() {
        return this.craCd;
    }
    
    public void setCraCd(String craCd) {
        this.craCd = craCd;
    }
    
    public Date getCraEffDt() {
        return this.craEffDt;
    }
    
    public void setCraEffDt(Date craEffDt) {
        this.craEffDt = craEffDt;
    }
    
    public Date getCraEndDt() {
        return this.craEndDt;
    }
    
    public void setCraEndDt(Date craEndDt) {
        this.craEndDt = craEndDt;
    }
    
    public int getHardCredLnAmt() {
        return this.hardCredLnAmt;
    }
    
    public void setHardCredLnAmt(int hardCredLnAmt) {
        this.hardCredLnAmt = hardCredLnAmt;
    }
    
    public String getWorkoutFl() {
        return this.workoutFl;
    }
    
    public void setWorkoutFl(String workoutFl) {
        this.workoutFl = workoutFl;
    }
    
    public String getWorkoutResolveCd() {
        return this.workoutResolveCd;
    }
    
    public void setWorkoutResolveCd(String workoutResolveCd) {
        this.workoutResolveCd = workoutResolveCd;
    }
    
    public Date getOrigEffDt() {
        return this.origEffDt;
    }
    
    public void setOrigEffDt(Date origEffDt) {
        this.origEffDt = origEffDt;
    }
    
    public Date getAcquireDt() {
        return this.acquireDt;
    }
    
    public void setAcquireDt(Date acquireDt) {
        this.acquireDt = acquireDt;
    }
    
    public int getRooftopCnt() {
        return this.rooftopCnt;
    }
    
    public void setRooftopCnt(int rooftopCnt) {
        this.rooftopCnt = rooftopCnt;
    }
    
    public String getCmrclDsclrFl() {
        return this.cmrclDsclrFl;
    }
    
    public void setCmrclDsclrFl(String cmrclDsclrFl) {
        this.cmrclDsclrFl = cmrclDsclrFl;
    }
    
    public Date getDistAgreeDt() {
        return this.distAgreeDt;
    }
    
    public void setDistAgreeDt(Date distAgreeDt) {
        this.distAgreeDt = distAgreeDt;
    }
    
    public String getWfbnaDocFl() {
        return this.wfbnaDocFl;
    }
    
    public void setWfbnaDocFl(String wfbnaDocFl) {
        this.wfbnaDocFl = wfbnaDocFl;
    }
    
    public String getCustDescTx() {
        return this.custDescTx;
    }
    
    public void setCustDescTx(String custDescTx) {
        this.custDescTx = custDescTx;
    }
    
    public Date getCurrEffDt() {
        return this.currEffDt;
    }
    
    public void setCurrEffDt(Date currEffDt) {
        this.currEffDt = currEffDt;
    }
    
    public String getRetailStlmntFl() {
        return this.retailStlmntFl;
    }
    
    public void setRetailStlmntFl(String retailStlmntFl) {
        this.retailStlmntFl = retailStlmntFl;
    }
    
    public String getGovernLawCd() {
        return this.governLawCd;
    }
    
    public void setGovernLawCd(String governLawCd) {
        this.governLawCd = governLawCd;
    }
    
    public int getTotalEmplCnt() {
        return this.totalEmplCnt;
    }
    
    public void setTotalEmplCnt(int totalEmplCnt) {
        this.totalEmplCnt = totalEmplCnt;
    }
    
    public int getTotalAssetAmt() {
        return this.totalAssetAmt;
    }
    
    public void setTotalAssetAmt(int totalAssetAmt) {
        this.totalAssetAmt = totalAssetAmt;
    }
    
    public Date getAgreeRestateDt() {
        return this.agreeRestateDt;
    }
    
    public void setAgreeRestateDt(Date agreeRestateDt) {
        this.agreeRestateDt = agreeRestateDt;
    }
    
    public Date getTotalEmplDt() {
        return this.totalEmplDt;
    }
    
    public void setTotalEmplDt(Date totalEmplDt) {
        this.totalEmplDt = totalEmplDt;
    }
    
    public Date getTotalAssetDt() {
        return this.totalAssetDt;
    }
    
    public void setTotalAssetDt(Date totalAssetDt) {
        this.totalAssetDt = totalAssetDt;
    }
    
    public Date getAnnualSalesDt() {
        return this.annualSalesDt;
    }
    
    public void setAnnualSalesDt(Date annualSalesDt) {
        this.annualSalesDt = annualSalesDt;
    }
    
    public int getHardClPydownAmt() {
        return this.hardClPydownAmt;
    }
    
    public void setHardClPydownAmt(int hardClPydownAmt) {
        this.hardClPydownAmt = hardClPydownAmt;
    }
    
    public int getWcisCupId() {
        return this.wcisCupId;
    }
    
    public void setWcisCupId(int wcisCupId) {
        this.wcisCupId = wcisCupId;
    }
    
    public int getWcisSupId() {
        return this.wcisSupId;
    }
    
    public void setWcisSupId(int wcisSupId) {
        this.wcisSupId = wcisSupId;
    }
    
    public int getHardTempClAmt() {
        return this.hardTempClAmt;
    }
    
    public void setHardTempClAmt(int hardTempClAmt) {
        this.hardTempClAmt = hardTempClAmt;
    }
    
    public int getHardTempPdwnAmt() {
        return this.hardTempPdwnAmt;
    }
    
    public void setHardTempPdwnAmt(int hardTempPdwnAmt) {
        this.hardTempPdwnAmt = hardTempPdwnAmt;
    }
    
    public String getStockExchMicId() {
        return this.stockExchMicId;
    }
    
    public void setStockExchMicId(String stockExchMicId) {
        this.stockExchMicId = stockExchMicId;
    }
    
    public String getStockTickerId() {
        return this.stockTickerId;
    }
    
    public void setStockTickerId(String stockTickerId) {
        this.stockTickerId = stockTickerId;
    }
    
    public String getRiskCountryCd() {
        return this.riskCountryCd;
    }
    
    public void setRiskCountryCd(String riskCountryCd) {
        this.riskCountryCd = riskCountryCd;
    }
    
    public int getTotRecovAmt() {
        return this.totRecovAmt;
    }
    
    public void setTotRecovAmt(int totRecovAmt) {
        this.totRecovAmt = totRecovAmt;
    }
    
    public String getTaxIdTypeCd() {
        return this.taxIdTypeCd;
    }
    
    public void setTaxIdTypeCd(String taxIdTypeCd) {
        this.taxIdTypeCd = taxIdTypeCd;
    }
    
    public Date getBnkrpDt() {
        return this.bnkrpDt;
    }
    
    public void setBnkrpDt(Date bnkrpDt) {
        this.bnkrpDt = bnkrpDt;
    }
    
    public String getApiHApprUpdFl() {
        return this.apiHApprUpdFl;
    }
    
    public void setApiHApprUpdFl(String apiHApprUpdFl) {
        this.apiHApprUpdFl = apiHApprUpdFl;
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder();
        s.append("{ custNo=\"");
        s.append(getCustNo());
        s.append("\"");
        s.append(", custTypeCode=\"");
        s.append(getCustTypeCode());
        s.append("\"");
        s.append(", custStatusCode=\"");
        s.append(getCustStatusCode());
        s.append("\"");
        s.append(", affilCustNo=\"");
        s.append(getAffilCustNo());
        s.append("\"");
        s.append(", cntlEntNo=\"");
        s.append(getCntlEntNo());
        s.append("\"");
        s.append(", billAcctRepCode=\"");
        s.append(getBillAcctRepCode());
        s.append("\"");
        s.append(", busCode=\"");
        s.append(getBusCode());
        s.append("\"");
        s.append(", collBranchNo=\"");
        s.append(getCollBranchNo());
        s.append("\"");
        s.append(", collDelayCnt=\"");
        s.append(getCollDelayCnt());
        s.append("\"");
        s.append(", rate360Flag=\"");
        s.append(getRate360Flag());
        s.append("\"");
        s.append(", advPayFlag=\"");
        s.append(getAdvPayFlag());
        s.append("\"");
        s.append(", recourseFlag=\"");
        s.append(getRecourseFlag());
        s.append("\"");
        s.append(", shipAuthAmt=\"");
        s.append(getShipAuthAmt());
        s.append("\"");
        s.append(", billCustFlag=\"");
        s.append(getBillCustFlag());
        s.append("\"");
        s.append(", slCycleNo=\"");
        s.append(getSlCycleNo());
        s.append("\"");
        s.append(", shipAuthFlag=\"");
        s.append(getShipAuthFlag());
        s.append("\"");
        s.append(", finanStmtDate=\"");
        s.append(getFinanStmtDate());
        s.append("\"");
        s.append(", dunnSicCode=\"");
        s.append(getDunnSicCode());
        s.append("\"");
        s.append(", dunnNo=\"");
        s.append(getDunnNo());
        s.append("\"");
        s.append(", busEstabYearNo=\"");
        s.append(getBusEstabYearNo());
        s.append("\"");
        s.append(", commBeginDay1No=\"");
        s.append(getCommBeginDay1No());
        s.append("\"");
        s.append(", commEndDay1No=\"");
        s.append(getCommEndDay1No());
        s.append("\"");
        s.append(", commDueDay1No=\"");
        s.append(getCommDueDay1No());
        s.append("\"");
        s.append(", commBeginDay2No=\"");
        s.append(getCommBeginDay2No());
        s.append("\"");
        s.append(", commEndDay2No=\"");
        s.append(getCommEndDay2No());
        s.append("\"");
        s.append(", commDueDay2No=\"");
        s.append(getCommDueDay2No());
        s.append("\"");
        s.append(", commBeginDay3No=\"");
        s.append(getCommBeginDay3No());
        s.append("\"");
        s.append(", commEndDay3No=\"");
        s.append(getCommEndDay3No());
        s.append("\"");
        s.append(", commDueDay3No=\"");
        s.append(getCommDueDay3No());
        s.append("\"");
        s.append(", commBeginDay4No=\"");
        s.append(getCommBeginDay4No());
        s.append("\"");
        s.append(", commEndDay4No=\"");
        s.append(getCommEndDay4No());
        s.append("\"");
        s.append(", commDueDay4No=\"");
        s.append(getCommDueDay4No());
        s.append("\"");
        s.append(", repurFlag=\"");
        s.append(getRepurFlag());
        s.append("\"");
        s.append(", stripPassFlag=\"");
        s.append(getStripPassFlag());
        s.append("\"");
        s.append(", revFreqCode=\"");
        s.append(getRevFreqCode());
        s.append("\"");
        s.append(", trustPrtformCode=\"");
        s.append(getTrustPrtformCode());
        s.append("\"");
        s.append(", trustPrtCode=\"");
        s.append(getTrustPrtCode());
        s.append("\"");
        s.append(", taxIdUpdCnt=\"");
        s.append(getTaxIdUpdCnt());
        s.append("\"");
        s.append(", taxIdNo=\"");
        s.append(getTaxIdNo());
        s.append("\"");
        s.append(", intCredExpDate=\"");
        s.append(getIntCredExpDate());
        s.append("\"");
        s.append(", tcfcStartYearNo=\"");
        s.append(getTcfcStartYearNo());
        s.append("\"");
        s.append(", apprTypeCode=\"");
        s.append(getApprTypeCode());
        s.append("\"");
        s.append(", billConsFlag=\"");
        s.append(getBillConsFlag());
        s.append("\"");
        s.append(", billSubDistFlag=\"");
        s.append(getBillSubDistFlag());
        s.append("\"");
        s.append(", lastRevDate=\"");
        s.append(getLastRevDate());
        s.append("\"");
        s.append(", insFlag=\"");
        s.append(getInsFlag());
        s.append("\"");
        s.append(", coverageInsAmt=\"");
        s.append(getCoverageInsAmt());
        s.append("\"");
        s.append(", policyExpDate=\"");
        s.append(getPolicyExpDate());
        s.append("\"");
        s.append(", ovrdInsRate=\"");
        s.append(getOvrdInsRate());
        s.append("\"");
        s.append(", insRateCode=\"");
        s.append(getInsRateCode());
        s.append("\"");
        s.append(", sotUpdDate=\"");
        s.append(getSotUpdDate());
        s.append("\"");
        s.append(", repoUpdDate=\"");
        s.append(getRepoUpdDate());
        s.append("\"");
        s.append(", repoAmt=\"");
        s.append(getRepoAmt());
        s.append("\"");
        s.append(", sotBalDueAmt=\"");
        s.append(getSotBalDueAmt());
        s.append("\"");
        s.append(", reserveFund2Amt=\"");
        s.append(getReserveFund2Amt());
        s.append("\"");
        s.append(", reserveFund1Amt=\"");
        s.append(getReserveFund1Amt());
        s.append("\"");
        s.append(", inctPayBranchNo=\"");
        s.append(getInctPayBranchNo());
        s.append("\"");
        s.append(", inctBasedOnCode=\"");
        s.append(getInctBasedOnCode());
        s.append("\"");
        s.append(", inctPayFreqCode=\"");
        s.append(getInctPayFreqCode());
        s.append("\"");
        s.append(", inctPaidDate=\"");
        s.append(getInctPaidDate());
        s.append("\"");
        s.append(", billAddrNo=\"");
        s.append(getBillAddrNo());
        s.append("\"");
        s.append(", payAddrNo=\"");
        s.append(getPayAddrNo());
        s.append("\"");
        s.append(", phoneNo=\"");
        s.append(getPhoneNo());
        s.append("\"");
        s.append(", faxNo=\"");
        s.append(getFaxNo());
        s.append("\"");
        s.append(", fcBranchNo=\"");
        s.append(getFcBranchNo());
        s.append("\"");
        s.append(", fcCycleNo=\"");
        s.append(getFcCycleNo());
        s.append("\"");
        s.append(", fcSortSeqCode=\"");
        s.append(getFcSortSeqCode());
        s.append("\"");
        s.append(", fcTypeCode=\"");
        s.append(getFcTypeCode());
        s.append("\"");
        s.append(", fcLastDate=\"");
        s.append(getFcLastDate());
        s.append("\"");
        s.append(", fcLastInitials=\"");
        s.append(getFcLastInitials());
        s.append("\"");
        s.append(", estLossInitials=\"");
        s.append(getEstLossInitials());
        s.append("\"");
        s.append(", estLossAmt=\"");
        s.append(getEstLossAmt());
        s.append("\"");
        s.append(", distMfgOsAmt=\"");
        s.append(getDistMfgOsAmt());
        s.append("\"");
        s.append(", uccStFilingNo=\"");
        s.append(getUccStFilingNo());
        s.append("\"");
        s.append(", uccStExpDate=\"");
        s.append(getUccStExpDate());
        s.append("\"");
        s.append(", uccCntyFilingNo=\"");
        s.append(getUccCntyFilingNo());
        s.append("\"");
        s.append(", uccCntyExpDate=\"");
        s.append(getUccCntyExpDate());
        s.append("\"");
        s.append(", initChrgRptFlag=\"");
        s.append(getInitChrgRptFlag());
        s.append("\"");
        s.append(", collDelayInd=\"");
        s.append(getCollDelayInd());
        s.append("\"");
        s.append(", slGraceDaysCnt=\"");
        s.append(getSlGraceDaysCnt());
        s.append("\"");
        s.append(", credScoreNo=\"");
        s.append(getCredScoreNo());
        s.append("\"");
        s.append(", industryCode=\"");
        s.append(getIndustryCode());
        s.append("\"");
        s.append(", dlrRepCode=\"");
        s.append(getDlrRepCode());
        s.append("\"");
        s.append(", bosAuditDate=\"");
        s.append(getBosAuditDate());
        s.append("\"");
        s.append(", bosCycleNo=\"");
        s.append(getBosCycleNo());
        s.append("\"");
        s.append(", bankAuditDate=\"");
        s.append(getBankAuditDate());
        s.append("\"");
        s.append(", bankCycleNo=\"");
        s.append(getBankCycleNo());
        s.append("\"");
        s.append(", adminFlatAmt=\"");
        s.append(getAdminFlatAmt());
        s.append("\"");
        s.append(", adminRate=\"");
        s.append(getAdminRate());
        s.append("\"");
        s.append(", adminMinOsAmt=\"");
        s.append(getAdminMinOsAmt());
        s.append("\"");
        s.append(", lossReserveRate=\"");
        s.append(getLossReserveRate());
        s.append("\"");
        s.append(", lossReserveAmt=\"");
        s.append(getLossReserveAmt());
        s.append("\"");
        s.append(", cntlBranchNo=\"");
        s.append(getCntlBranchNo());
        s.append("\"");
        s.append(", fsrRepCode=\"");
        s.append(getFsrRepCode());
        s.append("\"");
        s.append(", lastFsrRepCode=\"");
        s.append(getLastFsrRepCode());
        s.append("\"");
        s.append(", titleCode=\"");
        s.append(getTitleCode());
        s.append("\"");
        s.append(", shipCodeReqFlag=\"");
        s.append(getShipCodeReqFlag());
        s.append("\"");
        s.append(", plbBranchNo=\"");
        s.append(getPlbBranchNo());
        s.append("\"");
        s.append(", plbPayAddrNo=\"");
        s.append(getPlbPayAddrNo());
        s.append("\"");
        s.append(", annualSalesAmt=\"");
        s.append(getAnnualSalesAmt());
        s.append("\"");
        s.append(", cogsAmt=\"");
        s.append(getCogsAmt());
        s.append("\"");
        s.append(", bankName=\"");
        s.append(getBankName());
        s.append("\"");
        s.append(", leadSourceCode=\"");
        s.append(getLeadSourceCode());
        s.append("\"");
        s.append(", leadCustNo=\"");
        s.append(getLeadCustNo());
        s.append("\"");
        s.append(", projCode=\"");
        s.append(getProjCode());
        s.append("\"");
        s.append(", salesCogsDate=\"");
        s.append(getSalesCogsDate());
        s.append("\"");
        s.append(", crRvReqstInd=\"");
        s.append(getCrRvReqstInd());
        s.append("\"");
        s.append(", crRvReqstDate=\"");
        s.append(getCrRvReqstDate());
        s.append("\"");
        s.append(", crRvDlrRepCode=\"");
        s.append(getCrRvDlrRepCode());
        s.append("\"");
        s.append(", estCredLnAmt=\"");
        s.append(getEstCredLnAmt());
        s.append("\"");
        s.append(", finSource1Code=\"");
        s.append(getFinSource1Code());
        s.append("\"");
        s.append(", finSource2Code=\"");
        s.append(getFinSource2Code());
        s.append("\"");
        s.append(", finSource3Code=\"");
        s.append(getFinSource3Code());
        s.append("\"");
        s.append(", finSource4Code=\"");
        s.append(getFinSource4Code());
        s.append("\"");
        s.append(", computSystemInd=\"");
        s.append(getComputSystemInd());
        s.append("\"");
        s.append(", cntlInvnFlag=\"");
        s.append(getCntlInvnFlag());
        s.append("\"");
        s.append(", cntlAcctrecvFlag=\"");
        s.append(getCntlAcctrecvFlag());
        s.append("\"");
        s.append(", cntlAcctPblFlag=\"");
        s.append(getCntlAcctPblFlag());
        s.append("\"");
        s.append(", modemFlag=\"");
        s.append(getModemFlag());
        s.append("\"");
        s.append(", scfcRecvdDate=\"");
        s.append(getScfcRecvdDate());
        s.append("\"");
        s.append(", seasonalCode=\"");
        s.append(getSeasonalCode());
        s.append("\"");
        s.append(", fcJanCnt=\"");
        s.append(getFcJanCnt());
        s.append("\"");
        s.append(", fcFebCnt=\"");
        s.append(getFcFebCnt());
        s.append("\"");
        s.append(", fcMarCnt=\"");
        s.append(getFcMarCnt());
        s.append("\"");
        s.append(", fcAprCnt=\"");
        s.append(getFcAprCnt());
        s.append("\"");
        s.append(", fcMayCnt=\"");
        s.append(getFcMayCnt());
        s.append("\"");
        s.append(", fcJunCnt=\"");
        s.append(getFcJunCnt());
        s.append("\"");
        s.append(", fcJulCnt=\"");
        s.append(getFcJulCnt());
        s.append("\"");
        s.append(", fcAugCnt=\"");
        s.append(getFcAugCnt());
        s.append("\"");
        s.append(", fcSepCnt=\"");
        s.append(getFcSepCnt());
        s.append("\"");
        s.append(", fcOctCnt=\"");
        s.append(getFcOctCnt());
        s.append("\"");
        s.append(", fcNovCnt=\"");
        s.append(getFcNovCnt());
        s.append("\"");
        s.append(", fcDecCnt=\"");
        s.append(getFcDecCnt());
        s.append("\"");
        s.append(", fcTypeEffDate=\"");
        s.append(getFcTypeEffDate());
        s.append("\"");
        s.append(", fcTypeLogonId=\"");
        s.append(getFcTypeLogonId());
        s.append("\"");
        s.append(", fcReqCnt=\"");
        s.append(getFcReqCnt());
        s.append("\"");
        s.append(", lastYearFcCnt=\"");
        s.append(getLastYearFcCnt());
        s.append("\"");
        s.append(", scfcPrtInd=\"");
        s.append(getScfcPrtInd());
        s.append("\"");
        s.append(", scfcInclSlFlag=\"");
        s.append(getScfcInclSlFlag());
        s.append("\"");
        s.append(", nextSchedFcDate=\"");
        s.append(getNextSchedFcDate());
        s.append("\"");
        s.append(", prtAllTrustFlag=\"");
        s.append(getPrtAllTrustFlag());
        s.append("\"");
        s.append(", createDate=\"");
        s.append(getCreateDate());
        s.append("\"");
        s.append(", fcFreqEffDt=\"");
        s.append(getFcFreqEffDt());
        s.append("\"");
        s.append(", fcFreqLogonId=\"");
        s.append(getFcFreqLogonId());
        s.append("\"");
        s.append(", equityCycNo=\"");
        s.append(getEquityCycNo());
        s.append("\"");
        s.append(", equityDate=\"");
        s.append(getEquityDate());
        s.append("\"");
        s.append(", invnCycNo=\"");
        s.append(getInvnCycNo());
        s.append("\"");
        s.append(", invnDate=\"");
        s.append(getInvnDate());
        s.append("\"");
        s.append(", recclAmt=\"");
        s.append(getRecclAmt());
        s.append("\"");
        s.append(", scfcTrkNonFlag=\"");
        s.append(getScfcTrkNonFlag());
        s.append("\"");
        s.append(", scfcSent01Date=\"");
        s.append(getScfcSent01Date());
        s.append("\"");
        s.append(", scfcSent02Date=\"");
        s.append(getScfcSent02Date());
        s.append("\"");
        s.append(", pblFundDayInd=\"");
        s.append(getPblFundDayInd());
        s.append("\"");
        s.append(", pcsPurgeFlag=\"");
        s.append(getPcsPurgeFlag());
        s.append("\"");
        s.append(", ovlnTolerFlag=\"");
        s.append(getOvlnTolerFlag());
        s.append("\"");
        s.append(", apprPurgeMinAmt=\"");
        s.append(getApprPurgeMinAmt());
        s.append("\"");
        s.append(", apprPurgeMinPct=\"");
        s.append(getApprPurgeMinPct());
        s.append("\"");
        s.append(", apprPurgeDayCnt=\"");
        s.append(getApprPurgeDayCnt());
        s.append("\"");
        s.append(", credHoldDate=\"");
        s.append(getCredHoldDate());
        s.append("\"");
        s.append(", ovrdFundDateInd=\"");
        s.append(getOvrdFundDateInd());
        s.append("\"");
        s.append(", noTrustDtlFlag=\"");
        s.append(getNoTrustDtlFlag());
        s.append("\"");
        s.append(", purchContactName=\"");
        s.append(getPurchContactName());
        s.append("\"");
        s.append(", purchPhoneNo=\"");
        s.append(getPurchPhoneNo());
        s.append("\"");
        s.append(", purchFaxNo=\"");
        s.append(getPurchFaxNo());
        s.append("\"");
        s.append(", adminLogonId=\"");
        s.append(getAdminLogonId());
        s.append("\"");
        s.append(", serialNoFlag=\"");
        s.append(getSerialNoFlag());
        s.append("\"");
        s.append(", purchUpdDate=\"");
        s.append(getPurchUpdDate());
        s.append("\"");
        s.append(", purchUpdLogonId=\"");
        s.append(getPurchUpdLogonId());
        s.append("\"");
        s.append(", explodeInd=\"");
        s.append(getExplodeInd());
        s.append("\"");
        s.append(", filingTypeCode=\"");
        s.append(getFilingTypeCode());
        s.append("\"");
        s.append(", epdDelayDaysCnt=\"");
        s.append(getEpdDelayDaysCnt());
        s.append("\"");
        s.append(", salesZipCode=\"");
        s.append(getSalesZipCode());
        s.append("\"");
        s.append(", finStEntryDate=\"");
        s.append(getFinStEntryDate());
        s.append("\"");
        s.append(", trstSpecPrtDate=\"");
        s.append(getTrstSpecPrtDate());
        s.append("\"");
        s.append(", trstSpecPrtInd=\"");
        s.append(getTrstSpecPrtInd());
        s.append("\"");
        s.append(", credPrtFlag=\"");
        s.append(getCredPrtFlag());
        s.append("\"");
        s.append(", credPrtFreqCode=\"");
        s.append(getCredPrtFreqCode());
        s.append("\"");
        s.append(", stProvCode=\"");
        s.append(getStProvCode());
        s.append("\"");
        s.append(", zipPostalCode=\"");
        s.append(getZipPostalCode());
        s.append("\"");
        s.append(", countryCode=\"");
        s.append(getCountryCode());
        s.append("\"");
        s.append(", addr1Name=\"");
        s.append(getAddr1Name());
        s.append("\"");
        s.append(", addr2Name=\"");
        s.append(getAddr2Name());
        s.append("\"");
        s.append(", cityName=\"");
        s.append(getCityName());
        s.append("\"");
        s.append(", countyName=\"");
        s.append(getCountyName());
        s.append("\"");
        s.append(", legalName=\"");
        s.append(getLegalName());
        s.append("\"");
        s.append(", dbaName=\"");
        s.append(getDbaName());
        s.append("\"");
        s.append(", contactName=\"");
        s.append(getContactName());
        s.append("\"");
        s.append(", contactTitle=\"");
        s.append(getContactTitle());
        s.append("\"");
        s.append(", locDesc=\"");
        s.append(getLocDesc());
        s.append("\"");
        s.append(", cashPriorityNo=\"");
        s.append(getCashPriorityNo());
        s.append("\"");
        s.append(", xcSourceFlag=\"");
        s.append(getXcSourceFlag());
        s.append("\"");
        s.append(", xcEligibleFlag=\"");
        s.append(getXcEligibleFlag());
        s.append("\"");
        s.append(", xcCredStatCode=\"");
        s.append(getXcCredStatCode());
        s.append("\"");
        s.append(", slWeekdayFlag=\"");
        s.append(getSlWeekdayFlag());
        s.append("\"");
        s.append(", prtCredMemoInd=\"");
        s.append(getPrtCredMemoInd());
        s.append("\"");
        s.append(", cmApplyInd=\"");
        s.append(getCmApplyInd());
        s.append("\"");
        s.append(", cmFundFreqCode=\"");
        s.append(getCmFundFreqCode());
        s.append("\"");
        s.append(", cmFundDayCode=\"");
        s.append(getCmFundDayCode());
        s.append("\"");
        s.append(", prtCurrencyFlag=\"");
        s.append(getPrtCurrencyFlag());
        s.append("\"");
        s.append(", attrnOvrdFlag=\"");
        s.append(getAttrnOvrdFlag());
        s.append("\"");
        s.append(", attrnExpDate=\"");
        s.append(getAttrnExpDate());
        s.append("\"");
        s.append(", shortName=\"");
        s.append(getShortName());
        s.append("\"");
        s.append(", trstSpecPrtCode=\"");
        s.append(getTrstSpecPrtCode());
        s.append("\"");
        s.append(", emailAddrName=\"");
        s.append(getEmailAddrName());
        s.append("\"");
        s.append(", pdeSortSeqCode=\"");
        s.append(getPdeSortSeqCode());
        s.append("\"");
        s.append(", mstrInvPrtCode=\"");
        s.append(getMstrInvPrtCode());
        s.append("\"");
        s.append(", mstrInvReqInd=\"");
        s.append(getMstrInvReqInd());
        s.append("\"");
        s.append(", trustEdiCode=\"");
        s.append(getTrustEdiCode());
        s.append("\"");
        s.append(", vatRgstrNo=\"");
        s.append(getVatRgstrNo());
        s.append("\"");
        s.append(", vatElectionInd=\"");
        s.append(getVatElectionInd());
        s.append("\"");
        s.append(", languageCode=\"");
        s.append(getLanguageCode());
        s.append("\"");
        s.append(", holdNegPblInd=\"");
        s.append(getHoldNegPblInd());
        s.append("\"");
        s.append(", faxStubsFlag=\"");
        s.append(getFaxStubsFlag());
        s.append("\"");
        s.append(", polCredMemoFlag=\"");
        s.append(getPolCredMemoFlag());
        s.append("\"");
        s.append(", epdGraceDaysCnt=\"");
        s.append(getEpdGraceDaysCnt());
        s.append("\"");
        s.append(", epdFundFreqCode=\"");
        s.append(getEpdFundFreqCode());
        s.append("\"");
        s.append(", epdFundDaysCnt=\"");
        s.append(getEpdFundDaysCnt());
        s.append("\"");
        s.append(", credClaimFlag=\"");
        s.append(getCredClaimFlag());
        s.append("\"");
        s.append(", credClaimDayCnt=\"");
        s.append(getCredClaimDayCnt());
        s.append("\"");
        s.append(", rmbCmDiscPgmNo=\"");
        s.append(getRmbCmDiscPgmNo());
        s.append("\"");
        s.append(", skuIdFlag=\"");
        s.append(getSkuIdFlag());
        s.append("\"");
        s.append(", prtCredNoteInd=\"");
        s.append(getPrtCredNoteInd());
        s.append("\"");
        s.append(", uccOrgNo=\"");
        s.append(getUccOrgNo());
        s.append("\"");
        s.append(", uccOrgSt=\"");
        s.append(getUccOrgSt());
        s.append("\"");
        s.append(", systemCreateFlag=\"");
        s.append(getSystemCreateFlag());
        s.append("\"");
        s.append(", currencyCode=\"");
        s.append(getCurrencyCode());
        s.append("\"");
        s.append(", autoCashFlag=\"");
        s.append(getAutoCashFlag());
        s.append("\"");
        s.append(", incomplCashFlag=\"");
        s.append(getIncomplCashFlag());
        s.append("\"");
        s.append(", podPresDayCnt=\"");
        s.append(getPodPresDayCnt());
        s.append("\"");
        s.append(", podPresDateFlag=\"");
        s.append(getPodPresDateFlag());
        s.append("\"");
        s.append(", trueUpEarlyFlag=\"");
        s.append(getTrueUpEarlyFlag());
        s.append("\"");
        s.append(", trueUpLateFlag=\"");
        s.append(getTrueUpLateFlag());
        s.append("\"");
        s.append(", tdfRiskPct=\"");
        s.append(getTdfRiskPct());
        s.append("\"");
        s.append(", riskFeeRate=\"");
        s.append(getRiskFeeRate());
        s.append("\"");
        s.append(", riskFeeFiuFlag=\"");
        s.append(getRiskFeeFiuFlag());
        s.append("\"");
        s.append(", portTypeCode=\"");
        s.append(getPortTypeCode());
        s.append("\"");
        s.append(", servTrustFlag=\"");
        s.append(getServTrustFlag());
        s.append("\"");
        s.append(", lossReserve2Rate=\"");
        s.append(getLossReserve2Rate());
        s.append("\"");
        s.append(", grossupPct=\"");
        s.append(getGrossupPct());
        s.append("\"");
        s.append(", adbDiscInd=\"");
        s.append(getAdbDiscInd());
        s.append("\"");
        s.append(", distFundOsAmt=\"");
        s.append(getDistFundOsAmt());
        s.append("\"");
        s.append(", dlrChrgTdfFlag=\"");
        s.append(getDlrChrgTdfFlag());
        s.append("\"");
        s.append(", miscId=\"");
        s.append(getMiscId());
        s.append("\"");
        s.append(", servFeeFreqInd=\"");
        s.append(getServFeeFreqInd());
        s.append("\"");
        s.append(", collFeeRate=\"");
        s.append(getCollFeeRate());
        s.append("\"");
        s.append(", fundHoldPct=\"");
        s.append(getFundHoldPct());
        s.append("\"");
        s.append(", fundLimitAmt=\"");
        s.append(getFundLimitAmt());
        s.append("\"");
        s.append(", servFeeBranchNo=\"");
        s.append(getServFeeBranchNo());
        s.append("\"");
        s.append(", trueUpIntFlag=\"");
        s.append(getTrueUpIntFlag());
        s.append("\"");
        s.append(", curtGraceDayCnt=\"");
        s.append(getCurtGraceDayCnt());
        s.append("\"");
        s.append(", anrMonthsCnt=\"");
        s.append(getAnrMonthsCnt());
        s.append("\"");
        s.append(", mstrAgreeDate=\"");
        s.append(getMstrAgreeDate());
        s.append("\"");
        s.append(", cmApplyRuleCode=\"");
        s.append(getCmApplyRuleCode());
        s.append("\"");
        s.append(", cmApplyExcpCode=\"");
        s.append(getCmApplyExcpCode());
        s.append("\"");
        s.append(", scrtzEligCd=\"");
        s.append(getScrtzEligCd());
        s.append("\"");
        s.append(", securedFl=\"");
        s.append(getSecuredFl());
        s.append("\"");
        s.append(", scrtzEffDate=\"");
        s.append(getScrtzEffDate());
        s.append("\"");
        s.append(", scrtzParticRt=\"");
        s.append(getScrtzParticRt());
        s.append("\"");
        s.append(", scrtzPoolId=\"");
        s.append(getScrtzPoolId());
        s.append("\"");
        s.append(", adbBillMethCode=\"");
        s.append(getAdbBillMethCode());
        s.append("\"");
        s.append(", lateFeeBillInd=\"");
        s.append(getLateFeeBillInd());
        s.append("\"");
        s.append(", edocsInd=\"");
        s.append(getEdocsInd());
        s.append("\"");
        s.append(", edocsStartDate=\"");
        s.append(getEdocsStartDate());
        s.append("\"");
        s.append(", edocsPrtEndDate=\"");
        s.append(getEdocsPrtEndDate());
        s.append("\"");
        s.append(", auxCarrNo=\"");
        s.append(getAuxCarrNo());
        s.append("\"");
        s.append(", curtPrepaidFlag=\"");
        s.append(getCurtPrepaidFlag());
        s.append("\"");
        s.append(", stmtSortCode=\"");
        s.append(getStmtSortCode());
        s.append("\"");
        s.append(", stmtPageBrkFlag=\"");
        s.append(getStmtPageBrkFlag());
        s.append("\"");
        s.append(", recvCmPrtFlag=\"");
        s.append(getRecvCmPrtFlag());
        s.append("\"");
        s.append(", slStmtPrtFl=\"");
        s.append(getSlStmtPrtFl());
        s.append("\"");
        s.append(", epdCbgnDay1No=\"");
        s.append(getEpdCbgnDay1No());
        s.append("\"");
        s.append(", epdCbgnDay2No=\"");
        s.append(getEpdCbgnDay2No());
        s.append("\"");
        s.append(", epdCbgnDay3No=\"");
        s.append(getEpdCbgnDay3No());
        s.append("\"");
        s.append(", epdCbgnDay4No=\"");
        s.append(getEpdCbgnDay4No());
        s.append("\"");
        s.append(", epdCdueDay1No=\"");
        s.append(getEpdCdueDay1No());
        s.append("\"");
        s.append(", epdCdueDay2No=\"");
        s.append(getEpdCdueDay2No());
        s.append("\"");
        s.append(", epdCdueDay3No=\"");
        s.append(getEpdCdueDay3No());
        s.append("\"");
        s.append(", epdCdueDay4No=\"");
        s.append(getEpdCdueDay4No());
        s.append("\"");
        s.append(", epdCendDay1No=\"");
        s.append(getEpdCendDay1No());
        s.append("\"");
        s.append(", epdCendDay2No=\"");
        s.append(getEpdCendDay2No());
        s.append("\"");
        s.append(", epdCendDay3No=\"");
        s.append(getEpdCendDay3No());
        s.append("\"");
        s.append(", epdCendDay4No=\"");
        s.append(getEpdCendDay4No());
        s.append("\"");
        s.append(", refundDistFdFl=\"");
        s.append(getRefundDistFdFl());
        s.append("\"");
        s.append(", cashAlgCode=\"");
        s.append(getCashAlgCode());
        s.append("\"");
        s.append(", autoStlmntFl=\"");
        s.append(getAutoStlmntFl());
        s.append("\"");
        s.append(", tranStmtFrmtCd=\"");
        s.append(getTranStmtFrmtCd());
        s.append("\"");
        s.append(", defNoPayFlag=\"");
        s.append(getDefNoPayFlag());
        s.append("\"");
        s.append(", defComposRtFlag=\"");
        s.append(getDefComposRtFlag());
        s.append("\"");
        s.append(", defBankCode=\"");
        s.append(getDefBankCode());
        s.append("\"");
        s.append(", defTypeInd=\"");
        s.append(getDefTypeInd());
        s.append("\"");
        s.append(", defMinprimeRate=\"");
        s.append(getDefMinprimeRate());
        s.append("\"");
        s.append(", defMaxprimeRate=\"");
        s.append(getDefMaxprimeRate());
        s.append("\"");
        s.append(", straightRate=\"");
        s.append(getStraightRate());
        s.append("\"");
        s.append(", prtNoteStmtInd=\"");
        s.append(getPrtNoteStmtInd());
        s.append("\"");
        s.append(", segmentCd=\"");
        s.append(getSegmentCd());
        s.append("\"");
        s.append(", highRiskFl=\"");
        s.append(getHighRiskFl());
        s.append("\"");
        s.append(", kmvId=\"");
        s.append(getKmvId());
        s.append("\"");
        s.append(", extIdTypeCd=\"");
        s.append(getExtIdTypeCd());
        s.append("\"");
        s.append(", extIdNo=\"");
        s.append(getExtIdNo());
        s.append("\"");
        s.append(", ofstTypeCd=\"");
        s.append(getOfstTypeCd());
        s.append("\"");
        s.append(", lineDetailFl=\"");
        s.append(getLineDetailFl());
        s.append("\"");
        s.append(", fixpSkipMo01Fl=\"");
        s.append(getFixpSkipMo01Fl());
        s.append("\"");
        s.append(", fixpSkipMo02Fl=\"");
        s.append(getFixpSkipMo02Fl());
        s.append("\"");
        s.append(", fixpSkipMo03Fl=\"");
        s.append(getFixpSkipMo03Fl());
        s.append("\"");
        s.append(", fixpSkipMo04Fl=\"");
        s.append(getFixpSkipMo04Fl());
        s.append("\"");
        s.append(", fixpSkipMo05Fl=\"");
        s.append(getFixpSkipMo05Fl());
        s.append("\"");
        s.append(", fixpSkipMo06Fl=\"");
        s.append(getFixpSkipMo06Fl());
        s.append("\"");
        s.append(", fixpSkipMo07Fl=\"");
        s.append(getFixpSkipMo07Fl());
        s.append("\"");
        s.append(", fixpSkipMo08Fl=\"");
        s.append(getFixpSkipMo08Fl());
        s.append("\"");
        s.append(", fixpSkipMo09Fl=\"");
        s.append(getFixpSkipMo09Fl());
        s.append("\"");
        s.append(", fixpSkipMo10Fl=\"");
        s.append(getFixpSkipMo10Fl());
        s.append("\"");
        s.append(", fixpSkipMo11Fl=\"");
        s.append(getFixpSkipMo11Fl());
        s.append("\"");
        s.append(", fixpSkipMo12Fl=\"");
        s.append(getFixpSkipMo12Fl());
        s.append("\"");
        s.append(", distPrchEmailNm=\"");
        s.append(getDistPrchEmailNm());
        s.append("\"");
        s.append(", rstrFl=\"");
        s.append(getRstrFl());
        s.append("\"");
        s.append(", starFl=\"");
        s.append(getStarFl());
        s.append("\"");
        s.append(", bnkrpFl=\"");
        s.append(getBnkrpFl());
        s.append("\"");
        s.append(", regSweepFl=\"");
        s.append(getRegSweepFl());
        s.append("\"");
        s.append(", putReasonCd=\"");
        s.append(getPutReasonCd());
        s.append("\"");
        s.append(", putReasonDt=\"");
        s.append(getPutReasonDt());
        s.append("\"");
        s.append(", bankWatchFl=\"");
        s.append(getBankWatchFl());
        s.append("\"");
        s.append(", ovlnTolerRt=\"");
        s.append(getOvlnTolerRt());
        s.append("\"");
        s.append(", otbHeldPayFl=\"");
        s.append(getOtbHeldPayFl());
        s.append("\"");
        s.append(", otbUnapplCashFl=\"");
        s.append(getOtbUnapplCashFl());
        s.append("\"");
        s.append(", otbUnidCashFl=\"");
        s.append(getOtbUnidCashFl());
        s.append("\"");
        s.append(", otbDefFl=\"");
        s.append(getOtbDefFl());
        s.append("\"");
        s.append(", pcsDefApprAmt=\"");
        s.append(getPcsDefApprAmt());
        s.append("\"");
        s.append(", otbExpoExclInd=\"");
        s.append(getOtbExpoExclInd());
        s.append("\"");
        s.append(", finanStmtInrmDt=\"");
        s.append(getFinanStmtInrmDt());
        s.append("\"");
        s.append(", finanStmtInrmCt=\"");
        s.append(getFinanStmtInrmCt());
        s.append("\"");
        s.append(", finanStmtExtnDt=\"");
        s.append(getFinanStmtExtnDt());
        s.append("\"");
        s.append(", publicPrivInd=\"");
        s.append(getPublicPrivInd());
        s.append("\"");
        s.append(", lastRevExtnDt=\"");
        s.append(getLastRevExtnDt());
        s.append("\"");
        s.append(", finanStmtQualCd=\"");
        s.append(getFinanStmtQualCd());
        s.append("\"");
        s.append(", endUserNm=\"");
        s.append(getEndUserNm());
        s.append("\"");
        s.append(", mraCustId=\"");
        s.append(getMraCustId());
        s.append("\"");
        s.append(", defOfstPrincFl=\"");
        s.append(getDefOfstPrincFl());
        s.append("\"");
        s.append(", defActivPageFl=\"");
        s.append(getDefActivPageFl());
        s.append("\"");
        s.append(", undwrGdlnExcpFl=\"");
        s.append(getUndwrGdlnExcpFl());
        s.append("\"");
        s.append(", dlrPreauthFl=\"");
        s.append(getDlrPreauthFl());
        s.append("\"");
        s.append(", preauthAllowFl=\"");
        s.append(getPreauthAllowFl());
        s.append("\"");
        s.append(", ediStmtTypeCd=\"");
        s.append(getEdiStmtTypeCd());
        s.append("\"");
        s.append(", ediMfgDistFlag=\"");
        s.append(getEdiMfgDistFlag());
        s.append("\"");
        s.append(", epdCmFl=\"");
        s.append(getEpdCmFl());
        s.append("\"");
        s.append(", mmtsCmprsFrmtFl=\"");
        s.append(getMmtsCmprsFrmtFl());
        s.append("\"");
        s.append(", prpCrtWavRprFl=\"");
        s.append(getPrpCrtWavRprFl());
        s.append("\"");
        s.append(", comsBigBufferCt=\"");
        s.append(getComsBigBufferCt());
        s.append("\"");
        s.append(", dlvVrfctnFl=\"");
        s.append(getDlvVrfctnFl());
        s.append("\"");
        s.append(", rcpCrtTaxInvFl=\"");
        s.append(getRcpCrtTaxInvFl());
        s.append("\"");
        s.append(", overseasDistFl=\"");
        s.append(getOverseasDistFl());
        s.append("\"");
        s.append(", mobileNo=\"");
        s.append(getMobileNo());
        s.append("\"");
        s.append(", servLvlCatCd=\"");
        s.append(getServLvlCatCd());
        s.append("\"");
        s.append(", partyId=\"");
        s.append(getPartyId());
        s.append("\"");
        s.append(", touchlessSyncId=\"");
        s.append(getTouchlessSyncId());
        s.append("\"");
        s.append(", priorChrgOffDt=\"");
        s.append(getPriorChrgOffDt());
        s.append("\"");
        s.append(", totChrgOffAmt=\"");
        s.append(getTotChrgOffAmt());
        s.append("\"");
        s.append(", ddOvrdSweepFl=\"");
        s.append(getDdOvrdSweepFl());
        s.append("\"");
        s.append(", prevScrtzPoolId=\"");
        s.append(getPrevScrtzPoolId());
        s.append("\"");
        s.append(", lastSpecRevDate=\"");
        s.append(getLastSpecRevDate());
        s.append("\"");
        s.append(", loanCommitFl=\"");
        s.append(getLoanCommitFl());
        s.append("\"");
        s.append(", loanCommitRenDt=\"");
        s.append(getLoanCommitRenDt());
        s.append("\"");
        s.append(", ckDeliveryCd=\"");
        s.append(getCkDeliveryCd());
        s.append("\"");
        s.append(", wcisId=\"");
        s.append(getWcisId());
        s.append("\"");
        s.append(", naicsCd=\"");
        s.append(getNaicsCd());
        s.append("\"");
        s.append(", custNoteTx=\"");
        s.append(getCustNoteTx());
        s.append("\"");
        s.append(", naceCd=\"");
        s.append(getNaceCd());
        s.append("\"");
        s.append(", collatSegmentDs=\"");
        s.append(getCollatSegmentDs());
        s.append("\"");
        s.append(", auditTs=\"");
        s.append(getAuditTs());
        s.append("\"");
        s.append(", auditLogonId=\"");
        s.append(getAuditLogonId());
        s.append("\"");
        s.append(", auditProcessTx=\"");
        s.append(getAuditProcessTx());
        s.append("\"");
        s.append(", auditDeleteFl=\"");
        s.append(getAuditDeleteFl());
        s.append("\"");
        s.append(", netRelianceAmt=\"");
        s.append(getNetRelianceAmt());
        s.append("\"");
        s.append(", lastAmlRevDt=\"");
        s.append(getLastAmlRevDt());
        s.append("\"");
        s.append(", ecrrCd=\"");
        s.append(getEcrrCd());
        s.append("\"");
        s.append(", craCd=\"");
        s.append(getCraCd());
        s.append("\"");
        s.append(", craEffDt=\"");
        s.append(getCraEffDt());
        s.append("\"");
        s.append(", craEndDt=\"");
        s.append(getCraEndDt());
        s.append("\"");
        s.append(", hardCredLnAmt=\"");
        s.append(getHardCredLnAmt());
        s.append("\"");
        s.append(", workoutFl=\"");
        s.append(getWorkoutFl());
        s.append("\"");
        s.append(", workoutResolveCd=\"");
        s.append(getWorkoutResolveCd());
        s.append("\"");
        s.append(", origEffDt=\"");
        s.append(getOrigEffDt());
        s.append("\"");
        s.append(", acquireDt=\"");
        s.append(getAcquireDt());
        s.append("\"");
        s.append(", rooftopCnt=\"");
        s.append(getRooftopCnt());
        s.append("\"");
        s.append(", cmrclDsclrFl=\"");
        s.append(getCmrclDsclrFl());
        s.append("\"");
        s.append(", distAgreeDt=\"");
        s.append(getDistAgreeDt());
        s.append("\"");
        s.append(", wfbnaDocFl=\"");
        s.append(getWfbnaDocFl());
        s.append("\"");
        s.append(", custDescTx=\"");
        s.append(getCustDescTx());
        s.append("\"");
        s.append(", currEffDt=\"");
        s.append(getCurrEffDt());
        s.append("\"");
        s.append(", retailStlmntFl=\"");
        s.append(getRetailStlmntFl());
        s.append("\"");
        s.append(", governLawCd=\"");
        s.append(getGovernLawCd());
        s.append("\"");
        s.append(", totalEmplCnt=\"");
        s.append(getTotalEmplCnt());
        s.append("\"");
        s.append(", totalAssetAmt=\"");
        s.append(getTotalAssetAmt());
        s.append("\"");
        s.append(", agreeRestateDt=\"");
        s.append(getAgreeRestateDt());
        s.append("\"");
        s.append(", totalEmplDt=\"");
        s.append(getTotalEmplDt());
        s.append("\"");
        s.append(", totalAssetDt=\"");
        s.append(getTotalAssetDt());
        s.append("\"");
        s.append(", annualSalesDt=\"");
        s.append(getAnnualSalesDt());
        s.append("\"");
        s.append(", hardClPydownAmt=\"");
        s.append(getHardClPydownAmt());
        s.append("\"");
        s.append(", wcisCupId=\"");
        s.append(getWcisCupId());
        s.append("\"");
        s.append(", wcisSupId=\"");
        s.append(getWcisSupId());
        s.append("\"");
        s.append(", hardTempClAmt=\"");
        s.append(getHardTempClAmt());
        s.append("\"");
        s.append(", hardTempPdwnAmt=\"");
        s.append(getHardTempPdwnAmt());
        s.append("\"");
        s.append(", stockExchMicId=\"");
        s.append(getStockExchMicId());
        s.append("\"");
        s.append(", stockTickerId=\"");
        s.append(getStockTickerId());
        s.append("\"");
        s.append(", riskCountryCd=\"");
        s.append(getRiskCountryCd());
        s.append("\"");
        s.append(", totRecovAmt=\"");
        s.append(getTotRecovAmt());
        s.append("\"");
        s.append(", taxIdTypeCd=\"");
        s.append(getTaxIdTypeCd());
        s.append("\"");
        s.append(", bnkrpDt=\"");
        s.append(getBnkrpDt());
        s.append("\"");
        s.append(", apiHApprUpdFl=\"");
        s.append(getApiHApprUpdFl());
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(Vwmcu00 that) {
        return this.custNo == that.custNo &&
            this.custTypeCode.equals(that.custTypeCode) &&
            this.custStatusCode.equals(that.custStatusCode) &&
            this.affilCustNo == that.affilCustNo &&
            this.cntlEntNo == that.cntlEntNo &&
            this.billAcctRepCode.equals(that.billAcctRepCode) &&
            this.busCode.equals(that.busCode) &&
            this.collBranchNo == that.collBranchNo &&
            this.collDelayCnt == that.collDelayCnt &&
            this.rate360Flag.equals(that.rate360Flag) &&
            this.advPayFlag.equals(that.advPayFlag) &&
            this.recourseFlag.equals(that.recourseFlag) &&
            this.shipAuthAmt == that.shipAuthAmt &&
            this.billCustFlag.equals(that.billCustFlag) &&
            this.slCycleNo.equals(that.slCycleNo) &&
            this.shipAuthFlag.equals(that.shipAuthFlag) &&
            this.finanStmtDate.equals(that.finanStmtDate) &&
            this.dunnSicCode.equals(that.dunnSicCode) &&
            this.dunnNo.equals(that.dunnNo) &&
            this.busEstabYearNo == that.busEstabYearNo &&
            this.commBeginDay1No == that.commBeginDay1No &&
            this.commEndDay1No == that.commEndDay1No &&
            this.commDueDay1No == that.commDueDay1No &&
            this.commBeginDay2No == that.commBeginDay2No &&
            this.commEndDay2No == that.commEndDay2No &&
            this.commDueDay2No == that.commDueDay2No &&
            this.commBeginDay3No == that.commBeginDay3No &&
            this.commEndDay3No == that.commEndDay3No &&
            this.commDueDay3No == that.commDueDay3No &&
            this.commBeginDay4No == that.commBeginDay4No &&
            this.commEndDay4No == that.commEndDay4No &&
            this.commDueDay4No == that.commDueDay4No &&
            this.repurFlag.equals(that.repurFlag) &&
            this.stripPassFlag.equals(that.stripPassFlag) &&
            this.revFreqCode == that.revFreqCode &&
            this.trustPrtformCode.equals(that.trustPrtformCode) &&
            this.trustPrtCode.equals(that.trustPrtCode) &&
            this.taxIdUpdCnt == that.taxIdUpdCnt &&
            this.taxIdNo.equals(that.taxIdNo) &&
            this.intCredExpDate.equals(that.intCredExpDate) &&
            this.tcfcStartYearNo == that.tcfcStartYearNo &&
            this.apprTypeCode.equals(that.apprTypeCode) &&
            this.billConsFlag.equals(that.billConsFlag) &&
            this.billSubDistFlag.equals(that.billSubDistFlag) &&
            this.lastRevDate.equals(that.lastRevDate) &&
            this.insFlag.equals(that.insFlag) &&
            this.coverageInsAmt == that.coverageInsAmt &&
            this.policyExpDate.equals(that.policyExpDate) &&
            this.ovrdInsRate == that.ovrdInsRate &&
            this.insRateCode == that.insRateCode &&
            this.sotUpdDate.equals(that.sotUpdDate) &&
            this.repoUpdDate.equals(that.repoUpdDate) &&
            this.repoAmt == that.repoAmt &&
            this.sotBalDueAmt == that.sotBalDueAmt &&
            this.reserveFund2Amt == that.reserveFund2Amt &&
            this.reserveFund1Amt == that.reserveFund1Amt &&
            this.inctPayBranchNo == that.inctPayBranchNo &&
            this.inctBasedOnCode.equals(that.inctBasedOnCode) &&
            this.inctPayFreqCode.equals(that.inctPayFreqCode) &&
            this.inctPaidDate.equals(that.inctPaidDate) &&
            this.billAddrNo == that.billAddrNo &&
            this.payAddrNo == that.payAddrNo &&
            this.phoneNo.equals(that.phoneNo) &&
            this.faxNo.equals(that.faxNo) &&
            this.fcBranchNo == that.fcBranchNo &&
            this.fcCycleNo == that.fcCycleNo &&
            this.fcSortSeqCode.equals(that.fcSortSeqCode) &&
            this.fcTypeCode.equals(that.fcTypeCode) &&
            this.fcLastDate.equals(that.fcLastDate) &&
            this.fcLastInitials.equals(that.fcLastInitials) &&
            this.estLossInitials.equals(that.estLossInitials) &&
            this.estLossAmt == that.estLossAmt &&
            this.distMfgOsAmt == that.distMfgOsAmt &&
            this.uccStFilingNo.equals(that.uccStFilingNo) &&
            this.uccStExpDate.equals(that.uccStExpDate) &&
            this.uccCntyFilingNo.equals(that.uccCntyFilingNo) &&
            this.uccCntyExpDate.equals(that.uccCntyExpDate) &&
            this.initChrgRptFlag.equals(that.initChrgRptFlag) &&
            this.collDelayInd.equals(that.collDelayInd) &&
            this.slGraceDaysCnt == that.slGraceDaysCnt &&
            this.credScoreNo == that.credScoreNo &&
            this.industryCode.equals(that.industryCode) &&
            this.dlrRepCode.equals(that.dlrRepCode) &&
            this.bosAuditDate.equals(that.bosAuditDate) &&
            this.bosCycleNo == that.bosCycleNo &&
            this.bankAuditDate.equals(that.bankAuditDate) &&
            this.bankCycleNo == that.bankCycleNo &&
            this.adminFlatAmt == that.adminFlatAmt &&
            this.adminRate == that.adminRate &&
            this.adminMinOsAmt == that.adminMinOsAmt &&
            this.lossReserveRate == that.lossReserveRate &&
            this.lossReserveAmt == that.lossReserveAmt &&
            this.cntlBranchNo == that.cntlBranchNo &&
            this.fsrRepCode.equals(that.fsrRepCode) &&
            this.lastFsrRepCode.equals(that.lastFsrRepCode) &&
            this.titleCode.equals(that.titleCode) &&
            this.shipCodeReqFlag.equals(that.shipCodeReqFlag) &&
            this.plbBranchNo == that.plbBranchNo &&
            this.plbPayAddrNo == that.plbPayAddrNo &&
            this.annualSalesAmt == that.annualSalesAmt &&
            this.cogsAmt == that.cogsAmt &&
            this.bankName.equals(that.bankName) &&
            this.leadSourceCode.equals(that.leadSourceCode) &&
            this.leadCustNo == that.leadCustNo &&
            this.projCode.equals(that.projCode) &&
            this.salesCogsDate.equals(that.salesCogsDate) &&
            this.crRvReqstInd.equals(that.crRvReqstInd) &&
            this.crRvReqstDate.equals(that.crRvReqstDate) &&
            this.crRvDlrRepCode.equals(that.crRvDlrRepCode) &&
            this.estCredLnAmt == that.estCredLnAmt &&
            this.finSource1Code.equals(that.finSource1Code) &&
            this.finSource2Code.equals(that.finSource2Code) &&
            this.finSource3Code.equals(that.finSource3Code) &&
            this.finSource4Code.equals(that.finSource4Code) &&
            this.computSystemInd.equals(that.computSystemInd) &&
            this.cntlInvnFlag.equals(that.cntlInvnFlag) &&
            this.cntlAcctrecvFlag.equals(that.cntlAcctrecvFlag) &&
            this.cntlAcctPblFlag.equals(that.cntlAcctPblFlag) &&
            this.modemFlag.equals(that.modemFlag) &&
            this.scfcRecvdDate.equals(that.scfcRecvdDate) &&
            this.seasonalCode == that.seasonalCode &&
            this.fcJanCnt == that.fcJanCnt &&
            this.fcFebCnt == that.fcFebCnt &&
            this.fcMarCnt == that.fcMarCnt &&
            this.fcAprCnt == that.fcAprCnt &&
            this.fcMayCnt == that.fcMayCnt &&
            this.fcJunCnt == that.fcJunCnt &&
            this.fcJulCnt == that.fcJulCnt &&
            this.fcAugCnt == that.fcAugCnt &&
            this.fcSepCnt == that.fcSepCnt &&
            this.fcOctCnt == that.fcOctCnt &&
            this.fcNovCnt == that.fcNovCnt &&
            this.fcDecCnt == that.fcDecCnt &&
            this.fcTypeEffDate.equals(that.fcTypeEffDate) &&
            this.fcTypeLogonId.equals(that.fcTypeLogonId) &&
            this.fcReqCnt == that.fcReqCnt &&
            this.lastYearFcCnt == that.lastYearFcCnt &&
            this.scfcPrtInd.equals(that.scfcPrtInd) &&
            this.scfcInclSlFlag.equals(that.scfcInclSlFlag) &&
            this.nextSchedFcDate.equals(that.nextSchedFcDate) &&
            this.prtAllTrustFlag.equals(that.prtAllTrustFlag) &&
            this.createDate.equals(that.createDate) &&
            this.fcFreqEffDt.equals(that.fcFreqEffDt) &&
            this.fcFreqLogonId.equals(that.fcFreqLogonId) &&
            this.equityCycNo == that.equityCycNo &&
            this.equityDate.equals(that.equityDate) &&
            this.invnCycNo == that.invnCycNo &&
            this.invnDate.equals(that.invnDate) &&
            this.recclAmt == that.recclAmt &&
            this.scfcTrkNonFlag.equals(that.scfcTrkNonFlag) &&
            this.scfcSent01Date.equals(that.scfcSent01Date) &&
            this.scfcSent02Date.equals(that.scfcSent02Date) &&
            this.pblFundDayInd.equals(that.pblFundDayInd) &&
            this.pcsPurgeFlag.equals(that.pcsPurgeFlag) &&
            this.ovlnTolerFlag.equals(that.ovlnTolerFlag) &&
            this.apprPurgeMinAmt == that.apprPurgeMinAmt &&
            this.apprPurgeMinPct == that.apprPurgeMinPct &&
            this.apprPurgeDayCnt == that.apprPurgeDayCnt &&
            this.credHoldDate.equals(that.credHoldDate) &&
            this.ovrdFundDateInd.equals(that.ovrdFundDateInd) &&
            this.noTrustDtlFlag.equals(that.noTrustDtlFlag) &&
            this.purchContactName.equals(that.purchContactName) &&
            this.purchPhoneNo.equals(that.purchPhoneNo) &&
            this.purchFaxNo.equals(that.purchFaxNo) &&
            this.adminLogonId.equals(that.adminLogonId) &&
            this.serialNoFlag.equals(that.serialNoFlag) &&
            this.purchUpdDate.equals(that.purchUpdDate) &&
            this.purchUpdLogonId.equals(that.purchUpdLogonId) &&
            this.explodeInd.equals(that.explodeInd) &&
            this.filingTypeCode.equals(that.filingTypeCode) &&
            this.epdDelayDaysCnt == that.epdDelayDaysCnt &&
            this.salesZipCode.equals(that.salesZipCode) &&
            this.finStEntryDate.equals(that.finStEntryDate) &&
            this.trstSpecPrtDate.equals(that.trstSpecPrtDate) &&
            this.trstSpecPrtInd.equals(that.trstSpecPrtInd) &&
            this.credPrtFlag.equals(that.credPrtFlag) &&
            this.credPrtFreqCode.equals(that.credPrtFreqCode) &&
            this.stProvCode.equals(that.stProvCode) &&
            this.zipPostalCode.equals(that.zipPostalCode) &&
            this.countryCode.equals(that.countryCode) &&
            this.addr1Name.equals(that.addr1Name) &&
            this.addr2Name.equals(that.addr2Name) &&
            this.cityName.equals(that.cityName) &&
            this.countyName.equals(that.countyName) &&
            this.legalName.equals(that.legalName) &&
            this.dbaName.equals(that.dbaName) &&
            this.contactName.equals(that.contactName) &&
            this.contactTitle.equals(that.contactTitle) &&
            this.locDesc.equals(that.locDesc) &&
            this.cashPriorityNo == that.cashPriorityNo &&
            this.xcSourceFlag.equals(that.xcSourceFlag) &&
            this.xcEligibleFlag.equals(that.xcEligibleFlag) &&
            this.xcCredStatCode.equals(that.xcCredStatCode) &&
            this.slWeekdayFlag.equals(that.slWeekdayFlag) &&
            this.prtCredMemoInd.equals(that.prtCredMemoInd) &&
            this.cmApplyInd.equals(that.cmApplyInd) &&
            this.cmFundFreqCode.equals(that.cmFundFreqCode) &&
            this.cmFundDayCode.equals(that.cmFundDayCode) &&
            this.prtCurrencyFlag.equals(that.prtCurrencyFlag) &&
            this.attrnOvrdFlag.equals(that.attrnOvrdFlag) &&
            this.attrnExpDate.equals(that.attrnExpDate) &&
            this.shortName.equals(that.shortName) &&
            this.trstSpecPrtCode.equals(that.trstSpecPrtCode) &&
            this.emailAddrName.equals(that.emailAddrName) &&
            this.pdeSortSeqCode.equals(that.pdeSortSeqCode) &&
            this.mstrInvPrtCode.equals(that.mstrInvPrtCode) &&
            this.mstrInvReqInd.equals(that.mstrInvReqInd) &&
            this.trustEdiCode.equals(that.trustEdiCode) &&
            this.vatRgstrNo.equals(that.vatRgstrNo) &&
            this.vatElectionInd.equals(that.vatElectionInd) &&
            this.languageCode.equals(that.languageCode) &&
            this.holdNegPblInd.equals(that.holdNegPblInd) &&
            this.faxStubsFlag.equals(that.faxStubsFlag) &&
            this.polCredMemoFlag.equals(that.polCredMemoFlag) &&
            this.epdGraceDaysCnt == that.epdGraceDaysCnt &&
            this.epdFundFreqCode.equals(that.epdFundFreqCode) &&
            this.epdFundDaysCnt == that.epdFundDaysCnt &&
            this.credClaimFlag.equals(that.credClaimFlag) &&
            this.credClaimDayCnt == that.credClaimDayCnt &&
            this.rmbCmDiscPgmNo == that.rmbCmDiscPgmNo &&
            this.skuIdFlag.equals(that.skuIdFlag) &&
            this.prtCredNoteInd.equals(that.prtCredNoteInd) &&
            this.uccOrgNo.equals(that.uccOrgNo) &&
            this.uccOrgSt.equals(that.uccOrgSt) &&
            this.systemCreateFlag.equals(that.systemCreateFlag) &&
            this.currencyCode.equals(that.currencyCode) &&
            this.autoCashFlag.equals(that.autoCashFlag) &&
            this.incomplCashFlag.equals(that.incomplCashFlag) &&
            this.podPresDayCnt == that.podPresDayCnt &&
            this.podPresDateFlag.equals(that.podPresDateFlag) &&
            this.trueUpEarlyFlag.equals(that.trueUpEarlyFlag) &&
            this.trueUpLateFlag.equals(that.trueUpLateFlag) &&
            this.tdfRiskPct == that.tdfRiskPct &&
            this.riskFeeRate == that.riskFeeRate &&
            this.riskFeeFiuFlag.equals(that.riskFeeFiuFlag) &&
            this.portTypeCode.equals(that.portTypeCode) &&
            this.servTrustFlag.equals(that.servTrustFlag) &&
            this.lossReserve2Rate == that.lossReserve2Rate &&
            this.grossupPct == that.grossupPct &&
            this.adbDiscInd.equals(that.adbDiscInd) &&
            this.distFundOsAmt == that.distFundOsAmt &&
            this.dlrChrgTdfFlag.equals(that.dlrChrgTdfFlag) &&
            this.miscId.equals(that.miscId) &&
            this.servFeeFreqInd.equals(that.servFeeFreqInd) &&
            this.collFeeRate == that.collFeeRate &&
            this.fundHoldPct == that.fundHoldPct &&
            this.fundLimitAmt == that.fundLimitAmt &&
            this.servFeeBranchNo == that.servFeeBranchNo &&
            this.trueUpIntFlag.equals(that.trueUpIntFlag) &&
            this.curtGraceDayCnt == that.curtGraceDayCnt &&
            this.anrMonthsCnt == that.anrMonthsCnt &&
            this.mstrAgreeDate.equals(that.mstrAgreeDate) &&
            this.cmApplyRuleCode.equals(that.cmApplyRuleCode) &&
            this.cmApplyExcpCode.equals(that.cmApplyExcpCode) &&
            this.scrtzEligCd.equals(that.scrtzEligCd) &&
            this.securedFl.equals(that.securedFl) &&
            this.scrtzEffDate.equals(that.scrtzEffDate) &&
            this.scrtzParticRt == that.scrtzParticRt &&
            this.scrtzPoolId.equals(that.scrtzPoolId) &&
            this.adbBillMethCode.equals(that.adbBillMethCode) &&
            this.lateFeeBillInd.equals(that.lateFeeBillInd) &&
            this.edocsInd.equals(that.edocsInd) &&
            this.edocsStartDate.equals(that.edocsStartDate) &&
            this.edocsPrtEndDate.equals(that.edocsPrtEndDate) &&
            this.auxCarrNo == that.auxCarrNo &&
            this.curtPrepaidFlag.equals(that.curtPrepaidFlag) &&
            this.stmtSortCode.equals(that.stmtSortCode) &&
            this.stmtPageBrkFlag.equals(that.stmtPageBrkFlag) &&
            this.recvCmPrtFlag.equals(that.recvCmPrtFlag) &&
            this.slStmtPrtFl.equals(that.slStmtPrtFl) &&
            this.epdCbgnDay1No == that.epdCbgnDay1No &&
            this.epdCbgnDay2No == that.epdCbgnDay2No &&
            this.epdCbgnDay3No == that.epdCbgnDay3No &&
            this.epdCbgnDay4No == that.epdCbgnDay4No &&
            this.epdCdueDay1No == that.epdCdueDay1No &&
            this.epdCdueDay2No == that.epdCdueDay2No &&
            this.epdCdueDay3No == that.epdCdueDay3No &&
            this.epdCdueDay4No == that.epdCdueDay4No &&
            this.epdCendDay1No == that.epdCendDay1No &&
            this.epdCendDay2No == that.epdCendDay2No &&
            this.epdCendDay3No == that.epdCendDay3No &&
            this.epdCendDay4No == that.epdCendDay4No &&
            this.refundDistFdFl.equals(that.refundDistFdFl) &&
            this.cashAlgCode == that.cashAlgCode &&
            this.autoStlmntFl.equals(that.autoStlmntFl) &&
            this.tranStmtFrmtCd.equals(that.tranStmtFrmtCd) &&
            this.defNoPayFlag.equals(that.defNoPayFlag) &&
            this.defComposRtFlag.equals(that.defComposRtFlag) &&
            this.defBankCode == that.defBankCode &&
            this.defTypeInd.equals(that.defTypeInd) &&
            this.defMinprimeRate == that.defMinprimeRate &&
            this.defMaxprimeRate == that.defMaxprimeRate &&
            this.straightRate == that.straightRate &&
            this.prtNoteStmtInd.equals(that.prtNoteStmtInd) &&
            this.segmentCd.equals(that.segmentCd) &&
            this.highRiskFl.equals(that.highRiskFl) &&
            this.kmvId.equals(that.kmvId) &&
            this.extIdTypeCd.equals(that.extIdTypeCd) &&
            this.extIdNo.equals(that.extIdNo) &&
            this.ofstTypeCd.equals(that.ofstTypeCd) &&
            this.lineDetailFl.equals(that.lineDetailFl) &&
            this.fixpSkipMo01Fl.equals(that.fixpSkipMo01Fl) &&
            this.fixpSkipMo02Fl.equals(that.fixpSkipMo02Fl) &&
            this.fixpSkipMo03Fl.equals(that.fixpSkipMo03Fl) &&
            this.fixpSkipMo04Fl.equals(that.fixpSkipMo04Fl) &&
            this.fixpSkipMo05Fl.equals(that.fixpSkipMo05Fl) &&
            this.fixpSkipMo06Fl.equals(that.fixpSkipMo06Fl) &&
            this.fixpSkipMo07Fl.equals(that.fixpSkipMo07Fl) &&
            this.fixpSkipMo08Fl.equals(that.fixpSkipMo08Fl) &&
            this.fixpSkipMo09Fl.equals(that.fixpSkipMo09Fl) &&
            this.fixpSkipMo10Fl.equals(that.fixpSkipMo10Fl) &&
            this.fixpSkipMo11Fl.equals(that.fixpSkipMo11Fl) &&
            this.fixpSkipMo12Fl.equals(that.fixpSkipMo12Fl) &&
            this.distPrchEmailNm.equals(that.distPrchEmailNm) &&
            this.rstrFl.equals(that.rstrFl) &&
            this.starFl.equals(that.starFl) &&
            this.bnkrpFl.equals(that.bnkrpFl) &&
            this.regSweepFl.equals(that.regSweepFl) &&
            this.putReasonCd.equals(that.putReasonCd) &&
            this.putReasonDt.equals(that.putReasonDt) &&
            this.bankWatchFl.equals(that.bankWatchFl) &&
            this.ovlnTolerRt == that.ovlnTolerRt &&
            this.otbHeldPayFl.equals(that.otbHeldPayFl) &&
            this.otbUnapplCashFl.equals(that.otbUnapplCashFl) &&
            this.otbUnidCashFl.equals(that.otbUnidCashFl) &&
            this.otbDefFl.equals(that.otbDefFl) &&
            this.pcsDefApprAmt == that.pcsDefApprAmt &&
            this.otbExpoExclInd.equals(that.otbExpoExclInd) &&
            this.finanStmtInrmDt.equals(that.finanStmtInrmDt) &&
            this.finanStmtInrmCt == that.finanStmtInrmCt &&
            this.finanStmtExtnDt.equals(that.finanStmtExtnDt) &&
            this.publicPrivInd.equals(that.publicPrivInd) &&
            this.lastRevExtnDt.equals(that.lastRevExtnDt) &&
            this.finanStmtQualCd.equals(that.finanStmtQualCd) &&
            this.endUserNm.equals(that.endUserNm) &&
            this.mraCustId.equals(that.mraCustId) &&
            this.defOfstPrincFl.equals(that.defOfstPrincFl) &&
            this.defActivPageFl.equals(that.defActivPageFl) &&
            this.undwrGdlnExcpFl.equals(that.undwrGdlnExcpFl) &&
            this.dlrPreauthFl.equals(that.dlrPreauthFl) &&
            this.preauthAllowFl.equals(that.preauthAllowFl) &&
            this.ediStmtTypeCd.equals(that.ediStmtTypeCd) &&
            this.ediMfgDistFlag.equals(that.ediMfgDistFlag) &&
            this.epdCmFl.equals(that.epdCmFl) &&
            this.mmtsCmprsFrmtFl.equals(that.mmtsCmprsFrmtFl) &&
            this.prpCrtWavRprFl.equals(that.prpCrtWavRprFl) &&
            this.comsBigBufferCt == that.comsBigBufferCt &&
            this.dlvVrfctnFl.equals(that.dlvVrfctnFl) &&
            this.rcpCrtTaxInvFl.equals(that.rcpCrtTaxInvFl) &&
            this.overseasDistFl.equals(that.overseasDistFl) &&
            this.mobileNo.equals(that.mobileNo) &&
            this.servLvlCatCd.equals(that.servLvlCatCd) &&
            this.partyId.equals(that.partyId) &&
            this.touchlessSyncId == that.touchlessSyncId &&
            this.priorChrgOffDt.equals(that.priorChrgOffDt) &&
            this.totChrgOffAmt == that.totChrgOffAmt &&
            this.ddOvrdSweepFl.equals(that.ddOvrdSweepFl) &&
            this.prevScrtzPoolId.equals(that.prevScrtzPoolId) &&
            this.lastSpecRevDate.equals(that.lastSpecRevDate) &&
            this.loanCommitFl.equals(that.loanCommitFl) &&
            this.loanCommitRenDt.equals(that.loanCommitRenDt) &&
            this.ckDeliveryCd.equals(that.ckDeliveryCd) &&
            this.wcisId == that.wcisId &&
            this.naicsCd.equals(that.naicsCd) &&
            this.custNoteTx.equals(that.custNoteTx) &&
            this.naceCd.equals(that.naceCd) &&
            this.collatSegmentDs.equals(that.collatSegmentDs) &&
            this.auditTs.equals(that.auditTs) &&
            this.auditLogonId.equals(that.auditLogonId) &&
            this.auditProcessTx.equals(that.auditProcessTx) &&
            this.auditDeleteFl.equals(that.auditDeleteFl) &&
            this.netRelianceAmt == that.netRelianceAmt &&
            this.lastAmlRevDt.equals(that.lastAmlRevDt) &&
            this.ecrrCd.equals(that.ecrrCd) &&
            this.craCd.equals(that.craCd) &&
            this.craEffDt.equals(that.craEffDt) &&
            this.craEndDt.equals(that.craEndDt) &&
            this.hardCredLnAmt == that.hardCredLnAmt &&
            this.workoutFl.equals(that.workoutFl) &&
            this.workoutResolveCd.equals(that.workoutResolveCd) &&
            this.origEffDt.equals(that.origEffDt) &&
            this.acquireDt.equals(that.acquireDt) &&
            this.rooftopCnt == that.rooftopCnt &&
            this.cmrclDsclrFl.equals(that.cmrclDsclrFl) &&
            this.distAgreeDt.equals(that.distAgreeDt) &&
            this.wfbnaDocFl.equals(that.wfbnaDocFl) &&
            this.custDescTx.equals(that.custDescTx) &&
            this.currEffDt.equals(that.currEffDt) &&
            this.retailStlmntFl.equals(that.retailStlmntFl) &&
            this.governLawCd.equals(that.governLawCd) &&
            this.totalEmplCnt == that.totalEmplCnt &&
            this.totalAssetAmt == that.totalAssetAmt &&
            this.agreeRestateDt.equals(that.agreeRestateDt) &&
            this.totalEmplDt.equals(that.totalEmplDt) &&
            this.totalAssetDt.equals(that.totalAssetDt) &&
            this.annualSalesDt.equals(that.annualSalesDt) &&
            this.hardClPydownAmt == that.hardClPydownAmt &&
            this.wcisCupId == that.wcisCupId &&
            this.wcisSupId == that.wcisSupId &&
            this.hardTempClAmt == that.hardTempClAmt &&
            this.hardTempPdwnAmt == that.hardTempPdwnAmt &&
            this.stockExchMicId.equals(that.stockExchMicId) &&
            this.stockTickerId.equals(that.stockTickerId) &&
            this.riskCountryCd.equals(that.riskCountryCd) &&
            this.totRecovAmt == that.totRecovAmt &&
            this.taxIdTypeCd.equals(that.taxIdTypeCd) &&
            this.bnkrpDt.equals(that.bnkrpDt) &&
            this.apiHApprUpdFl.equals(that.apiHApprUpdFl);
    }
    
    /** Per-field equality comparison; only returns equal if argument is the same class */
    @Override
    public boolean equals(Object that) {
        return (that instanceof Vwmcu00 other) && other.canEqual(this) && this.equals(other);
    }
    
    /** Does {@code that} have the same runtime type as {@code this}? */
    public boolean canEqual(Object that) {
        return that instanceof Vwmcu00;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * result + Integer.hashCode(custNo);
        result = 31 * result + Objects.hashCode(custTypeCode);
        result = 31 * result + Objects.hashCode(custStatusCode);
        result = 31 * result + Integer.hashCode(affilCustNo);
        result = 31 * result + Integer.hashCode(cntlEntNo);
        result = 31 * result + Objects.hashCode(billAcctRepCode);
        result = 31 * result + Objects.hashCode(busCode);
        result = 31 * result + Integer.hashCode(collBranchNo);
        result = 31 * result + Integer.hashCode(collDelayCnt);
        result = 31 * result + Objects.hashCode(rate360Flag);
        result = 31 * result + Objects.hashCode(advPayFlag);
        result = 31 * result + Objects.hashCode(recourseFlag);
        result = 31 * result + Integer.hashCode(shipAuthAmt);
        result = 31 * result + Objects.hashCode(billCustFlag);
        result = 31 * result + Objects.hashCode(slCycleNo);
        result = 31 * result + Objects.hashCode(shipAuthFlag);
        result = 31 * result + Objects.hashCode(finanStmtDate);
        result = 31 * result + Objects.hashCode(dunnSicCode);
        result = 31 * result + Objects.hashCode(dunnNo);
        result = 31 * result + Integer.hashCode(busEstabYearNo);
        result = 31 * result + Integer.hashCode(commBeginDay1No);
        result = 31 * result + Integer.hashCode(commEndDay1No);
        result = 31 * result + Integer.hashCode(commDueDay1No);
        result = 31 * result + Integer.hashCode(commBeginDay2No);
        result = 31 * result + Integer.hashCode(commEndDay2No);
        result = 31 * result + Integer.hashCode(commDueDay2No);
        result = 31 * result + Integer.hashCode(commBeginDay3No);
        result = 31 * result + Integer.hashCode(commEndDay3No);
        result = 31 * result + Integer.hashCode(commDueDay3No);
        result = 31 * result + Integer.hashCode(commBeginDay4No);
        result = 31 * result + Integer.hashCode(commEndDay4No);
        result = 31 * result + Integer.hashCode(commDueDay4No);
        result = 31 * result + Objects.hashCode(repurFlag);
        result = 31 * result + Objects.hashCode(stripPassFlag);
        result = 31 * result + Integer.hashCode(revFreqCode);
        result = 31 * result + Objects.hashCode(trustPrtformCode);
        result = 31 * result + Objects.hashCode(trustPrtCode);
        result = 31 * result + Integer.hashCode(taxIdUpdCnt);
        result = 31 * result + Objects.hashCode(taxIdNo);
        result = 31 * result + Objects.hashCode(intCredExpDate);
        result = 31 * result + Integer.hashCode(tcfcStartYearNo);
        result = 31 * result + Objects.hashCode(apprTypeCode);
        result = 31 * result + Objects.hashCode(billConsFlag);
        result = 31 * result + Objects.hashCode(billSubDistFlag);
        result = 31 * result + Objects.hashCode(lastRevDate);
        result = 31 * result + Objects.hashCode(insFlag);
        result = 31 * result + Integer.hashCode(coverageInsAmt);
        result = 31 * result + Objects.hashCode(policyExpDate);
        result = 31 * result + Integer.hashCode(ovrdInsRate);
        result = 31 * result + Integer.hashCode(insRateCode);
        result = 31 * result + Objects.hashCode(sotUpdDate);
        result = 31 * result + Objects.hashCode(repoUpdDate);
        result = 31 * result + Integer.hashCode(repoAmt);
        result = 31 * result + Integer.hashCode(sotBalDueAmt);
        result = 31 * result + Integer.hashCode(reserveFund2Amt);
        result = 31 * result + Integer.hashCode(reserveFund1Amt);
        result = 31 * result + Integer.hashCode(inctPayBranchNo);
        result = 31 * result + Objects.hashCode(inctBasedOnCode);
        result = 31 * result + Objects.hashCode(inctPayFreqCode);
        result = 31 * result + Objects.hashCode(inctPaidDate);
        result = 31 * result + Integer.hashCode(billAddrNo);
        result = 31 * result + Integer.hashCode(payAddrNo);
        result = 31 * result + Objects.hashCode(phoneNo);
        result = 31 * result + Objects.hashCode(faxNo);
        result = 31 * result + Integer.hashCode(fcBranchNo);
        result = 31 * result + Integer.hashCode(fcCycleNo);
        result = 31 * result + Objects.hashCode(fcSortSeqCode);
        result = 31 * result + Objects.hashCode(fcTypeCode);
        result = 31 * result + Objects.hashCode(fcLastDate);
        result = 31 * result + Objects.hashCode(fcLastInitials);
        result = 31 * result + Objects.hashCode(estLossInitials);
        result = 31 * result + Integer.hashCode(estLossAmt);
        result = 31 * result + Integer.hashCode(distMfgOsAmt);
        result = 31 * result + Objects.hashCode(uccStFilingNo);
        result = 31 * result + Objects.hashCode(uccStExpDate);
        result = 31 * result + Objects.hashCode(uccCntyFilingNo);
        result = 31 * result + Objects.hashCode(uccCntyExpDate);
        result = 31 * result + Objects.hashCode(initChrgRptFlag);
        result = 31 * result + Objects.hashCode(collDelayInd);
        result = 31 * result + Integer.hashCode(slGraceDaysCnt);
        result = 31 * result + Integer.hashCode(credScoreNo);
        result = 31 * result + Objects.hashCode(industryCode);
        result = 31 * result + Objects.hashCode(dlrRepCode);
        result = 31 * result + Objects.hashCode(bosAuditDate);
        result = 31 * result + Integer.hashCode(bosCycleNo);
        result = 31 * result + Objects.hashCode(bankAuditDate);
        result = 31 * result + Integer.hashCode(bankCycleNo);
        result = 31 * result + Integer.hashCode(adminFlatAmt);
        result = 31 * result + Integer.hashCode(adminRate);
        result = 31 * result + Integer.hashCode(adminMinOsAmt);
        result = 31 * result + Integer.hashCode(lossReserveRate);
        result = 31 * result + Integer.hashCode(lossReserveAmt);
        result = 31 * result + Integer.hashCode(cntlBranchNo);
        result = 31 * result + Objects.hashCode(fsrRepCode);
        result = 31 * result + Objects.hashCode(lastFsrRepCode);
        result = 31 * result + Objects.hashCode(titleCode);
        result = 31 * result + Objects.hashCode(shipCodeReqFlag);
        result = 31 * result + Integer.hashCode(plbBranchNo);
        result = 31 * result + Integer.hashCode(plbPayAddrNo);
        result = 31 * result + Integer.hashCode(annualSalesAmt);
        result = 31 * result + Integer.hashCode(cogsAmt);
        result = 31 * result + Objects.hashCode(bankName);
        result = 31 * result + Objects.hashCode(leadSourceCode);
        result = 31 * result + Integer.hashCode(leadCustNo);
        result = 31 * result + Objects.hashCode(projCode);
        result = 31 * result + Objects.hashCode(salesCogsDate);
        result = 31 * result + Objects.hashCode(crRvReqstInd);
        result = 31 * result + Objects.hashCode(crRvReqstDate);
        result = 31 * result + Objects.hashCode(crRvDlrRepCode);
        result = 31 * result + Integer.hashCode(estCredLnAmt);
        result = 31 * result + Objects.hashCode(finSource1Code);
        result = 31 * result + Objects.hashCode(finSource2Code);
        result = 31 * result + Objects.hashCode(finSource3Code);
        result = 31 * result + Objects.hashCode(finSource4Code);
        result = 31 * result + Objects.hashCode(computSystemInd);
        result = 31 * result + Objects.hashCode(cntlInvnFlag);
        result = 31 * result + Objects.hashCode(cntlAcctrecvFlag);
        result = 31 * result + Objects.hashCode(cntlAcctPblFlag);
        result = 31 * result + Objects.hashCode(modemFlag);
        result = 31 * result + Objects.hashCode(scfcRecvdDate);
        result = 31 * result + Integer.hashCode(seasonalCode);
        result = 31 * result + Integer.hashCode(fcJanCnt);
        result = 31 * result + Integer.hashCode(fcFebCnt);
        result = 31 * result + Integer.hashCode(fcMarCnt);
        result = 31 * result + Integer.hashCode(fcAprCnt);
        result = 31 * result + Integer.hashCode(fcMayCnt);
        result = 31 * result + Integer.hashCode(fcJunCnt);
        result = 31 * result + Integer.hashCode(fcJulCnt);
        result = 31 * result + Integer.hashCode(fcAugCnt);
        result = 31 * result + Integer.hashCode(fcSepCnt);
        result = 31 * result + Integer.hashCode(fcOctCnt);
        result = 31 * result + Integer.hashCode(fcNovCnt);
        result = 31 * result + Integer.hashCode(fcDecCnt);
        result = 31 * result + Objects.hashCode(fcTypeEffDate);
        result = 31 * result + Objects.hashCode(fcTypeLogonId);
        result = 31 * result + Integer.hashCode(fcReqCnt);
        result = 31 * result + Integer.hashCode(lastYearFcCnt);
        result = 31 * result + Objects.hashCode(scfcPrtInd);
        result = 31 * result + Objects.hashCode(scfcInclSlFlag);
        result = 31 * result + Objects.hashCode(nextSchedFcDate);
        result = 31 * result + Objects.hashCode(prtAllTrustFlag);
        result = 31 * result + Objects.hashCode(createDate);
        result = 31 * result + Objects.hashCode(fcFreqEffDt);
        result = 31 * result + Objects.hashCode(fcFreqLogonId);
        result = 31 * result + Integer.hashCode(equityCycNo);
        result = 31 * result + Objects.hashCode(equityDate);
        result = 31 * result + Integer.hashCode(invnCycNo);
        result = 31 * result + Objects.hashCode(invnDate);
        result = 31 * result + Integer.hashCode(recclAmt);
        result = 31 * result + Objects.hashCode(scfcTrkNonFlag);
        result = 31 * result + Objects.hashCode(scfcSent01Date);
        result = 31 * result + Objects.hashCode(scfcSent02Date);
        result = 31 * result + Objects.hashCode(pblFundDayInd);
        result = 31 * result + Objects.hashCode(pcsPurgeFlag);
        result = 31 * result + Objects.hashCode(ovlnTolerFlag);
        result = 31 * result + Integer.hashCode(apprPurgeMinAmt);
        result = 31 * result + Integer.hashCode(apprPurgeMinPct);
        result = 31 * result + Integer.hashCode(apprPurgeDayCnt);
        result = 31 * result + Objects.hashCode(credHoldDate);
        result = 31 * result + Objects.hashCode(ovrdFundDateInd);
        result = 31 * result + Objects.hashCode(noTrustDtlFlag);
        result = 31 * result + Objects.hashCode(purchContactName);
        result = 31 * result + Objects.hashCode(purchPhoneNo);
        result = 31 * result + Objects.hashCode(purchFaxNo);
        result = 31 * result + Objects.hashCode(adminLogonId);
        result = 31 * result + Objects.hashCode(serialNoFlag);
        result = 31 * result + Objects.hashCode(purchUpdDate);
        result = 31 * result + Objects.hashCode(purchUpdLogonId);
        result = 31 * result + Objects.hashCode(explodeInd);
        result = 31 * result + Objects.hashCode(filingTypeCode);
        result = 31 * result + Integer.hashCode(epdDelayDaysCnt);
        result = 31 * result + Objects.hashCode(salesZipCode);
        result = 31 * result + Objects.hashCode(finStEntryDate);
        result = 31 * result + Objects.hashCode(trstSpecPrtDate);
        result = 31 * result + Objects.hashCode(trstSpecPrtInd);
        result = 31 * result + Objects.hashCode(credPrtFlag);
        result = 31 * result + Objects.hashCode(credPrtFreqCode);
        result = 31 * result + Objects.hashCode(stProvCode);
        result = 31 * result + Objects.hashCode(zipPostalCode);
        result = 31 * result + Objects.hashCode(countryCode);
        result = 31 * result + Objects.hashCode(addr1Name);
        result = 31 * result + Objects.hashCode(addr2Name);
        result = 31 * result + Objects.hashCode(cityName);
        result = 31 * result + Objects.hashCode(countyName);
        result = 31 * result + Objects.hashCode(legalName);
        result = 31 * result + Objects.hashCode(dbaName);
        result = 31 * result + Objects.hashCode(contactName);
        result = 31 * result + Objects.hashCode(contactTitle);
        result = 31 * result + Objects.hashCode(locDesc);
        result = 31 * result + Integer.hashCode(cashPriorityNo);
        result = 31 * result + Objects.hashCode(xcSourceFlag);
        result = 31 * result + Objects.hashCode(xcEligibleFlag);
        result = 31 * result + Objects.hashCode(xcCredStatCode);
        result = 31 * result + Objects.hashCode(slWeekdayFlag);
        result = 31 * result + Objects.hashCode(prtCredMemoInd);
        result = 31 * result + Objects.hashCode(cmApplyInd);
        result = 31 * result + Objects.hashCode(cmFundFreqCode);
        result = 31 * result + Objects.hashCode(cmFundDayCode);
        result = 31 * result + Objects.hashCode(prtCurrencyFlag);
        result = 31 * result + Objects.hashCode(attrnOvrdFlag);
        result = 31 * result + Objects.hashCode(attrnExpDate);
        result = 31 * result + Objects.hashCode(shortName);
        result = 31 * result + Objects.hashCode(trstSpecPrtCode);
        result = 31 * result + Objects.hashCode(emailAddrName);
        result = 31 * result + Objects.hashCode(pdeSortSeqCode);
        result = 31 * result + Objects.hashCode(mstrInvPrtCode);
        result = 31 * result + Objects.hashCode(mstrInvReqInd);
        result = 31 * result + Objects.hashCode(trustEdiCode);
        result = 31 * result + Objects.hashCode(vatRgstrNo);
        result = 31 * result + Objects.hashCode(vatElectionInd);
        result = 31 * result + Objects.hashCode(languageCode);
        result = 31 * result + Objects.hashCode(holdNegPblInd);
        result = 31 * result + Objects.hashCode(faxStubsFlag);
        result = 31 * result + Objects.hashCode(polCredMemoFlag);
        result = 31 * result + Integer.hashCode(epdGraceDaysCnt);
        result = 31 * result + Objects.hashCode(epdFundFreqCode);
        result = 31 * result + Integer.hashCode(epdFundDaysCnt);
        result = 31 * result + Objects.hashCode(credClaimFlag);
        result = 31 * result + Integer.hashCode(credClaimDayCnt);
        result = 31 * result + Integer.hashCode(rmbCmDiscPgmNo);
        result = 31 * result + Objects.hashCode(skuIdFlag);
        result = 31 * result + Objects.hashCode(prtCredNoteInd);
        result = 31 * result + Objects.hashCode(uccOrgNo);
        result = 31 * result + Objects.hashCode(uccOrgSt);
        result = 31 * result + Objects.hashCode(systemCreateFlag);
        result = 31 * result + Objects.hashCode(currencyCode);
        result = 31 * result + Objects.hashCode(autoCashFlag);
        result = 31 * result + Objects.hashCode(incomplCashFlag);
        result = 31 * result + Integer.hashCode(podPresDayCnt);
        result = 31 * result + Objects.hashCode(podPresDateFlag);
        result = 31 * result + Objects.hashCode(trueUpEarlyFlag);
        result = 31 * result + Objects.hashCode(trueUpLateFlag);
        result = 31 * result + Integer.hashCode(tdfRiskPct);
        result = 31 * result + Integer.hashCode(riskFeeRate);
        result = 31 * result + Objects.hashCode(riskFeeFiuFlag);
        result = 31 * result + Objects.hashCode(portTypeCode);
        result = 31 * result + Objects.hashCode(servTrustFlag);
        result = 31 * result + Integer.hashCode(lossReserve2Rate);
        result = 31 * result + Integer.hashCode(grossupPct);
        result = 31 * result + Objects.hashCode(adbDiscInd);
        result = 31 * result + Integer.hashCode(distFundOsAmt);
        result = 31 * result + Objects.hashCode(dlrChrgTdfFlag);
        result = 31 * result + Objects.hashCode(miscId);
        result = 31 * result + Objects.hashCode(servFeeFreqInd);
        result = 31 * result + Integer.hashCode(collFeeRate);
        result = 31 * result + Integer.hashCode(fundHoldPct);
        result = 31 * result + Integer.hashCode(fundLimitAmt);
        result = 31 * result + Integer.hashCode(servFeeBranchNo);
        result = 31 * result + Objects.hashCode(trueUpIntFlag);
        result = 31 * result + Integer.hashCode(curtGraceDayCnt);
        result = 31 * result + Integer.hashCode(anrMonthsCnt);
        result = 31 * result + Objects.hashCode(mstrAgreeDate);
        result = 31 * result + Objects.hashCode(cmApplyRuleCode);
        result = 31 * result + Objects.hashCode(cmApplyExcpCode);
        result = 31 * result + Objects.hashCode(scrtzEligCd);
        result = 31 * result + Objects.hashCode(securedFl);
        result = 31 * result + Objects.hashCode(scrtzEffDate);
        result = 31 * result + Integer.hashCode(scrtzParticRt);
        result = 31 * result + Objects.hashCode(scrtzPoolId);
        result = 31 * result + Objects.hashCode(adbBillMethCode);
        result = 31 * result + Objects.hashCode(lateFeeBillInd);
        result = 31 * result + Objects.hashCode(edocsInd);
        result = 31 * result + Objects.hashCode(edocsStartDate);
        result = 31 * result + Objects.hashCode(edocsPrtEndDate);
        result = 31 * result + Integer.hashCode(auxCarrNo);
        result = 31 * result + Objects.hashCode(curtPrepaidFlag);
        result = 31 * result + Objects.hashCode(stmtSortCode);
        result = 31 * result + Objects.hashCode(stmtPageBrkFlag);
        result = 31 * result + Objects.hashCode(recvCmPrtFlag);
        result = 31 * result + Objects.hashCode(slStmtPrtFl);
        result = 31 * result + Integer.hashCode(epdCbgnDay1No);
        result = 31 * result + Integer.hashCode(epdCbgnDay2No);
        result = 31 * result + Integer.hashCode(epdCbgnDay3No);
        result = 31 * result + Integer.hashCode(epdCbgnDay4No);
        result = 31 * result + Integer.hashCode(epdCdueDay1No);
        result = 31 * result + Integer.hashCode(epdCdueDay2No);
        result = 31 * result + Integer.hashCode(epdCdueDay3No);
        result = 31 * result + Integer.hashCode(epdCdueDay4No);
        result = 31 * result + Integer.hashCode(epdCendDay1No);
        result = 31 * result + Integer.hashCode(epdCendDay2No);
        result = 31 * result + Integer.hashCode(epdCendDay3No);
        result = 31 * result + Integer.hashCode(epdCendDay4No);
        result = 31 * result + Objects.hashCode(refundDistFdFl);
        result = 31 * result + Integer.hashCode(cashAlgCode);
        result = 31 * result + Objects.hashCode(autoStlmntFl);
        result = 31 * result + Objects.hashCode(tranStmtFrmtCd);
        result = 31 * result + Objects.hashCode(defNoPayFlag);
        result = 31 * result + Objects.hashCode(defComposRtFlag);
        result = 31 * result + Integer.hashCode(defBankCode);
        result = 31 * result + Objects.hashCode(defTypeInd);
        result = 31 * result + Integer.hashCode(defMinprimeRate);
        result = 31 * result + Integer.hashCode(defMaxprimeRate);
        result = 31 * result + Integer.hashCode(straightRate);
        result = 31 * result + Objects.hashCode(prtNoteStmtInd);
        result = 31 * result + Objects.hashCode(segmentCd);
        result = 31 * result + Objects.hashCode(highRiskFl);
        result = 31 * result + Objects.hashCode(kmvId);
        result = 31 * result + Objects.hashCode(extIdTypeCd);
        result = 31 * result + Objects.hashCode(extIdNo);
        result = 31 * result + Objects.hashCode(ofstTypeCd);
        result = 31 * result + Objects.hashCode(lineDetailFl);
        result = 31 * result + Objects.hashCode(fixpSkipMo01Fl);
        result = 31 * result + Objects.hashCode(fixpSkipMo02Fl);
        result = 31 * result + Objects.hashCode(fixpSkipMo03Fl);
        result = 31 * result + Objects.hashCode(fixpSkipMo04Fl);
        result = 31 * result + Objects.hashCode(fixpSkipMo05Fl);
        result = 31 * result + Objects.hashCode(fixpSkipMo06Fl);
        result = 31 * result + Objects.hashCode(fixpSkipMo07Fl);
        result = 31 * result + Objects.hashCode(fixpSkipMo08Fl);
        result = 31 * result + Objects.hashCode(fixpSkipMo09Fl);
        result = 31 * result + Objects.hashCode(fixpSkipMo10Fl);
        result = 31 * result + Objects.hashCode(fixpSkipMo11Fl);
        result = 31 * result + Objects.hashCode(fixpSkipMo12Fl);
        result = 31 * result + Objects.hashCode(distPrchEmailNm);
        result = 31 * result + Objects.hashCode(rstrFl);
        result = 31 * result + Objects.hashCode(starFl);
        result = 31 * result + Objects.hashCode(bnkrpFl);
        result = 31 * result + Objects.hashCode(regSweepFl);
        result = 31 * result + Objects.hashCode(putReasonCd);
        result = 31 * result + Objects.hashCode(putReasonDt);
        result = 31 * result + Objects.hashCode(bankWatchFl);
        result = 31 * result + Integer.hashCode(ovlnTolerRt);
        result = 31 * result + Objects.hashCode(otbHeldPayFl);
        result = 31 * result + Objects.hashCode(otbUnapplCashFl);
        result = 31 * result + Objects.hashCode(otbUnidCashFl);
        result = 31 * result + Objects.hashCode(otbDefFl);
        result = 31 * result + Integer.hashCode(pcsDefApprAmt);
        result = 31 * result + Objects.hashCode(otbExpoExclInd);
        result = 31 * result + Objects.hashCode(finanStmtInrmDt);
        result = 31 * result + Integer.hashCode(finanStmtInrmCt);
        result = 31 * result + Objects.hashCode(finanStmtExtnDt);
        result = 31 * result + Objects.hashCode(publicPrivInd);
        result = 31 * result + Objects.hashCode(lastRevExtnDt);
        result = 31 * result + Objects.hashCode(finanStmtQualCd);
        result = 31 * result + Objects.hashCode(endUserNm);
        result = 31 * result + Objects.hashCode(mraCustId);
        result = 31 * result + Objects.hashCode(defOfstPrincFl);
        result = 31 * result + Objects.hashCode(defActivPageFl);
        result = 31 * result + Objects.hashCode(undwrGdlnExcpFl);
        result = 31 * result + Objects.hashCode(dlrPreauthFl);
        result = 31 * result + Objects.hashCode(preauthAllowFl);
        result = 31 * result + Objects.hashCode(ediStmtTypeCd);
        result = 31 * result + Objects.hashCode(ediMfgDistFlag);
        result = 31 * result + Objects.hashCode(epdCmFl);
        result = 31 * result + Objects.hashCode(mmtsCmprsFrmtFl);
        result = 31 * result + Objects.hashCode(prpCrtWavRprFl);
        result = 31 * result + Integer.hashCode(comsBigBufferCt);
        result = 31 * result + Objects.hashCode(dlvVrfctnFl);
        result = 31 * result + Objects.hashCode(rcpCrtTaxInvFl);
        result = 31 * result + Objects.hashCode(overseasDistFl);
        result = 31 * result + Objects.hashCode(mobileNo);
        result = 31 * result + Objects.hashCode(servLvlCatCd);
        result = 31 * result + Objects.hashCode(partyId);
        result = 31 * result + Integer.hashCode(touchlessSyncId);
        result = 31 * result + Objects.hashCode(priorChrgOffDt);
        result = 31 * result + Integer.hashCode(totChrgOffAmt);
        result = 31 * result + Objects.hashCode(ddOvrdSweepFl);
        result = 31 * result + Objects.hashCode(prevScrtzPoolId);
        result = 31 * result + Objects.hashCode(lastSpecRevDate);
        result = 31 * result + Objects.hashCode(loanCommitFl);
        result = 31 * result + Objects.hashCode(loanCommitRenDt);
        result = 31 * result + Objects.hashCode(ckDeliveryCd);
        result = 31 * result + Integer.hashCode(wcisId);
        result = 31 * result + Objects.hashCode(naicsCd);
        result = 31 * result + Objects.hashCode(custNoteTx);
        result = 31 * result + Objects.hashCode(naceCd);
        result = 31 * result + Objects.hashCode(collatSegmentDs);
        result = 31 * result + Objects.hashCode(auditTs);
        result = 31 * result + Objects.hashCode(auditLogonId);
        result = 31 * result + Objects.hashCode(auditProcessTx);
        result = 31 * result + Objects.hashCode(auditDeleteFl);
        result = 31 * result + Integer.hashCode(netRelianceAmt);
        result = 31 * result + Objects.hashCode(lastAmlRevDt);
        result = 31 * result + Objects.hashCode(ecrrCd);
        result = 31 * result + Objects.hashCode(craCd);
        result = 31 * result + Objects.hashCode(craEffDt);
        result = 31 * result + Objects.hashCode(craEndDt);
        result = 31 * result + Integer.hashCode(hardCredLnAmt);
        result = 31 * result + Objects.hashCode(workoutFl);
        result = 31 * result + Objects.hashCode(workoutResolveCd);
        result = 31 * result + Objects.hashCode(origEffDt);
        result = 31 * result + Objects.hashCode(acquireDt);
        result = 31 * result + Integer.hashCode(rooftopCnt);
        result = 31 * result + Objects.hashCode(cmrclDsclrFl);
        result = 31 * result + Objects.hashCode(distAgreeDt);
        result = 31 * result + Objects.hashCode(wfbnaDocFl);
        result = 31 * result + Objects.hashCode(custDescTx);
        result = 31 * result + Objects.hashCode(currEffDt);
        result = 31 * result + Objects.hashCode(retailStlmntFl);
        result = 31 * result + Objects.hashCode(governLawCd);
        result = 31 * result + Integer.hashCode(totalEmplCnt);
        result = 31 * result + Integer.hashCode(totalAssetAmt);
        result = 31 * result + Objects.hashCode(agreeRestateDt);
        result = 31 * result + Objects.hashCode(totalEmplDt);
        result = 31 * result + Objects.hashCode(totalAssetDt);
        result = 31 * result + Objects.hashCode(annualSalesDt);
        result = 31 * result + Integer.hashCode(hardClPydownAmt);
        result = 31 * result + Integer.hashCode(wcisCupId);
        result = 31 * result + Integer.hashCode(wcisSupId);
        result = 31 * result + Integer.hashCode(hardTempClAmt);
        result = 31 * result + Integer.hashCode(hardTempPdwnAmt);
        result = 31 * result + Objects.hashCode(stockExchMicId);
        result = 31 * result + Objects.hashCode(stockTickerId);
        result = 31 * result + Objects.hashCode(riskCountryCd);
        result = 31 * result + Integer.hashCode(totRecovAmt);
        result = 31 * result + Objects.hashCode(taxIdTypeCd);
        result = 31 * result + Objects.hashCode(bnkrpDt);
        result = 31 * result + Objects.hashCode(apiHApprUpdFl);
        return result;
    }
    
    @Override
    public int compareTo(Vwmcu00 that) {
        int c = 0;
        c = Integer.compare(this.custNo, that.custNo);
        if ( c != 0 ) return c;
        c = this.custTypeCode.compareTo(that.custTypeCode);
        if ( c != 0 ) return c;
        c = this.custStatusCode.compareTo(that.custStatusCode);
        if ( c != 0 ) return c;
        c = Integer.compare(this.affilCustNo, that.affilCustNo);
        if ( c != 0 ) return c;
        c = Integer.compare(this.cntlEntNo, that.cntlEntNo);
        if ( c != 0 ) return c;
        c = this.billAcctRepCode.compareTo(that.billAcctRepCode);
        if ( c != 0 ) return c;
        c = this.busCode.compareTo(that.busCode);
        if ( c != 0 ) return c;
        c = Integer.compare(this.collBranchNo, that.collBranchNo);
        if ( c != 0 ) return c;
        c = Integer.compare(this.collDelayCnt, that.collDelayCnt);
        if ( c != 0 ) return c;
        c = this.rate360Flag.compareTo(that.rate360Flag);
        if ( c != 0 ) return c;
        c = this.advPayFlag.compareTo(that.advPayFlag);
        if ( c != 0 ) return c;
        c = this.recourseFlag.compareTo(that.recourseFlag);
        if ( c != 0 ) return c;
        c = Integer.compare(this.shipAuthAmt, that.shipAuthAmt);
        if ( c != 0 ) return c;
        c = this.billCustFlag.compareTo(that.billCustFlag);
        if ( c != 0 ) return c;
        c = this.slCycleNo.compareTo(that.slCycleNo);
        if ( c != 0 ) return c;
        c = this.shipAuthFlag.compareTo(that.shipAuthFlag);
        if ( c != 0 ) return c;
        c = this.finanStmtDate.compareTo(that.finanStmtDate);
        if ( c != 0 ) return c;
        c = this.dunnSicCode.compareTo(that.dunnSicCode);
        if ( c != 0 ) return c;
        c = this.dunnNo.compareTo(that.dunnNo);
        if ( c != 0 ) return c;
        c = Integer.compare(this.busEstabYearNo, that.busEstabYearNo);
        if ( c != 0 ) return c;
        c = Integer.compare(this.commBeginDay1No, that.commBeginDay1No);
        if ( c != 0 ) return c;
        c = Integer.compare(this.commEndDay1No, that.commEndDay1No);
        if ( c != 0 ) return c;
        c = Integer.compare(this.commDueDay1No, that.commDueDay1No);
        if ( c != 0 ) return c;
        c = Integer.compare(this.commBeginDay2No, that.commBeginDay2No);
        if ( c != 0 ) return c;
        c = Integer.compare(this.commEndDay2No, that.commEndDay2No);
        if ( c != 0 ) return c;
        c = Integer.compare(this.commDueDay2No, that.commDueDay2No);
        if ( c != 0 ) return c;
        c = Integer.compare(this.commBeginDay3No, that.commBeginDay3No);
        if ( c != 0 ) return c;
        c = Integer.compare(this.commEndDay3No, that.commEndDay3No);
        if ( c != 0 ) return c;
        c = Integer.compare(this.commDueDay3No, that.commDueDay3No);
        if ( c != 0 ) return c;
        c = Integer.compare(this.commBeginDay4No, that.commBeginDay4No);
        if ( c != 0 ) return c;
        c = Integer.compare(this.commEndDay4No, that.commEndDay4No);
        if ( c != 0 ) return c;
        c = Integer.compare(this.commDueDay4No, that.commDueDay4No);
        if ( c != 0 ) return c;
        c = this.repurFlag.compareTo(that.repurFlag);
        if ( c != 0 ) return c;
        c = this.stripPassFlag.compareTo(that.stripPassFlag);
        if ( c != 0 ) return c;
        c = Integer.compare(this.revFreqCode, that.revFreqCode);
        if ( c != 0 ) return c;
        c = this.trustPrtformCode.compareTo(that.trustPrtformCode);
        if ( c != 0 ) return c;
        c = this.trustPrtCode.compareTo(that.trustPrtCode);
        if ( c != 0 ) return c;
        c = Integer.compare(this.taxIdUpdCnt, that.taxIdUpdCnt);
        if ( c != 0 ) return c;
        c = this.taxIdNo.compareTo(that.taxIdNo);
        if ( c != 0 ) return c;
        c = this.intCredExpDate.compareTo(that.intCredExpDate);
        if ( c != 0 ) return c;
        c = Integer.compare(this.tcfcStartYearNo, that.tcfcStartYearNo);
        if ( c != 0 ) return c;
        c = this.apprTypeCode.compareTo(that.apprTypeCode);
        if ( c != 0 ) return c;
        c = this.billConsFlag.compareTo(that.billConsFlag);
        if ( c != 0 ) return c;
        c = this.billSubDistFlag.compareTo(that.billSubDistFlag);
        if ( c != 0 ) return c;
        c = this.lastRevDate.compareTo(that.lastRevDate);
        if ( c != 0 ) return c;
        c = this.insFlag.compareTo(that.insFlag);
        if ( c != 0 ) return c;
        c = Integer.compare(this.coverageInsAmt, that.coverageInsAmt);
        if ( c != 0 ) return c;
        c = this.policyExpDate.compareTo(that.policyExpDate);
        if ( c != 0 ) return c;
        c = Integer.compare(this.ovrdInsRate, that.ovrdInsRate);
        if ( c != 0 ) return c;
        c = Integer.compare(this.insRateCode, that.insRateCode);
        if ( c != 0 ) return c;
        c = this.sotUpdDate.compareTo(that.sotUpdDate);
        if ( c != 0 ) return c;
        c = this.repoUpdDate.compareTo(that.repoUpdDate);
        if ( c != 0 ) return c;
        c = Integer.compare(this.repoAmt, that.repoAmt);
        if ( c != 0 ) return c;
        c = Integer.compare(this.sotBalDueAmt, that.sotBalDueAmt);
        if ( c != 0 ) return c;
        c = Integer.compare(this.reserveFund2Amt, that.reserveFund2Amt);
        if ( c != 0 ) return c;
        c = Integer.compare(this.reserveFund1Amt, that.reserveFund1Amt);
        if ( c != 0 ) return c;
        c = Integer.compare(this.inctPayBranchNo, that.inctPayBranchNo);
        if ( c != 0 ) return c;
        c = this.inctBasedOnCode.compareTo(that.inctBasedOnCode);
        if ( c != 0 ) return c;
        c = this.inctPayFreqCode.compareTo(that.inctPayFreqCode);
        if ( c != 0 ) return c;
        c = this.inctPaidDate.compareTo(that.inctPaidDate);
        if ( c != 0 ) return c;
        c = Integer.compare(this.billAddrNo, that.billAddrNo);
        if ( c != 0 ) return c;
        c = Integer.compare(this.payAddrNo, that.payAddrNo);
        if ( c != 0 ) return c;
        c = this.phoneNo.compareTo(that.phoneNo);
        if ( c != 0 ) return c;
        c = this.faxNo.compareTo(that.faxNo);
        if ( c != 0 ) return c;
        c = Integer.compare(this.fcBranchNo, that.fcBranchNo);
        if ( c != 0 ) return c;
        c = Integer.compare(this.fcCycleNo, that.fcCycleNo);
        if ( c != 0 ) return c;
        c = this.fcSortSeqCode.compareTo(that.fcSortSeqCode);
        if ( c != 0 ) return c;
        c = this.fcTypeCode.compareTo(that.fcTypeCode);
        if ( c != 0 ) return c;
        c = this.fcLastDate.compareTo(that.fcLastDate);
        if ( c != 0 ) return c;
        c = this.fcLastInitials.compareTo(that.fcLastInitials);
        if ( c != 0 ) return c;
        c = this.estLossInitials.compareTo(that.estLossInitials);
        if ( c != 0 ) return c;
        c = Integer.compare(this.estLossAmt, that.estLossAmt);
        if ( c != 0 ) return c;
        c = Integer.compare(this.distMfgOsAmt, that.distMfgOsAmt);
        if ( c != 0 ) return c;
        c = this.uccStFilingNo.compareTo(that.uccStFilingNo);
        if ( c != 0 ) return c;
        c = this.uccStExpDate.compareTo(that.uccStExpDate);
        if ( c != 0 ) return c;
        c = this.uccCntyFilingNo.compareTo(that.uccCntyFilingNo);
        if ( c != 0 ) return c;
        c = this.uccCntyExpDate.compareTo(that.uccCntyExpDate);
        if ( c != 0 ) return c;
        c = this.initChrgRptFlag.compareTo(that.initChrgRptFlag);
        if ( c != 0 ) return c;
        c = this.collDelayInd.compareTo(that.collDelayInd);
        if ( c != 0 ) return c;
        c = Integer.compare(this.slGraceDaysCnt, that.slGraceDaysCnt);
        if ( c != 0 ) return c;
        c = Integer.compare(this.credScoreNo, that.credScoreNo);
        if ( c != 0 ) return c;
        c = this.industryCode.compareTo(that.industryCode);
        if ( c != 0 ) return c;
        c = this.dlrRepCode.compareTo(that.dlrRepCode);
        if ( c != 0 ) return c;
        c = this.bosAuditDate.compareTo(that.bosAuditDate);
        if ( c != 0 ) return c;
        c = Integer.compare(this.bosCycleNo, that.bosCycleNo);
        if ( c != 0 ) return c;
        c = this.bankAuditDate.compareTo(that.bankAuditDate);
        if ( c != 0 ) return c;
        c = Integer.compare(this.bankCycleNo, that.bankCycleNo);
        if ( c != 0 ) return c;
        c = Integer.compare(this.adminFlatAmt, that.adminFlatAmt);
        if ( c != 0 ) return c;
        c = Integer.compare(this.adminRate, that.adminRate);
        if ( c != 0 ) return c;
        c = Integer.compare(this.adminMinOsAmt, that.adminMinOsAmt);
        if ( c != 0 ) return c;
        c = Integer.compare(this.lossReserveRate, that.lossReserveRate);
        if ( c != 0 ) return c;
        c = Integer.compare(this.lossReserveAmt, that.lossReserveAmt);
        if ( c != 0 ) return c;
        c = Integer.compare(this.cntlBranchNo, that.cntlBranchNo);
        if ( c != 0 ) return c;
        c = this.fsrRepCode.compareTo(that.fsrRepCode);
        if ( c != 0 ) return c;
        c = this.lastFsrRepCode.compareTo(that.lastFsrRepCode);
        if ( c != 0 ) return c;
        c = this.titleCode.compareTo(that.titleCode);
        if ( c != 0 ) return c;
        c = this.shipCodeReqFlag.compareTo(that.shipCodeReqFlag);
        if ( c != 0 ) return c;
        c = Integer.compare(this.plbBranchNo, that.plbBranchNo);
        if ( c != 0 ) return c;
        c = Integer.compare(this.plbPayAddrNo, that.plbPayAddrNo);
        if ( c != 0 ) return c;
        c = Integer.compare(this.annualSalesAmt, that.annualSalesAmt);
        if ( c != 0 ) return c;
        c = Integer.compare(this.cogsAmt, that.cogsAmt);
        if ( c != 0 ) return c;
        c = this.bankName.compareTo(that.bankName);
        if ( c != 0 ) return c;
        c = this.leadSourceCode.compareTo(that.leadSourceCode);
        if ( c != 0 ) return c;
        c = Integer.compare(this.leadCustNo, that.leadCustNo);
        if ( c != 0 ) return c;
        c = this.projCode.compareTo(that.projCode);
        if ( c != 0 ) return c;
        c = this.salesCogsDate.compareTo(that.salesCogsDate);
        if ( c != 0 ) return c;
        c = this.crRvReqstInd.compareTo(that.crRvReqstInd);
        if ( c != 0 ) return c;
        c = this.crRvReqstDate.compareTo(that.crRvReqstDate);
        if ( c != 0 ) return c;
        c = this.crRvDlrRepCode.compareTo(that.crRvDlrRepCode);
        if ( c != 0 ) return c;
        c = Integer.compare(this.estCredLnAmt, that.estCredLnAmt);
        if ( c != 0 ) return c;
        c = this.finSource1Code.compareTo(that.finSource1Code);
        if ( c != 0 ) return c;
        c = this.finSource2Code.compareTo(that.finSource2Code);
        if ( c != 0 ) return c;
        c = this.finSource3Code.compareTo(that.finSource3Code);
        if ( c != 0 ) return c;
        c = this.finSource4Code.compareTo(that.finSource4Code);
        if ( c != 0 ) return c;
        c = this.computSystemInd.compareTo(that.computSystemInd);
        if ( c != 0 ) return c;
        c = this.cntlInvnFlag.compareTo(that.cntlInvnFlag);
        if ( c != 0 ) return c;
        c = this.cntlAcctrecvFlag.compareTo(that.cntlAcctrecvFlag);
        if ( c != 0 ) return c;
        c = this.cntlAcctPblFlag.compareTo(that.cntlAcctPblFlag);
        if ( c != 0 ) return c;
        c = this.modemFlag.compareTo(that.modemFlag);
        if ( c != 0 ) return c;
        c = this.scfcRecvdDate.compareTo(that.scfcRecvdDate);
        if ( c != 0 ) return c;
        c = Integer.compare(this.seasonalCode, that.seasonalCode);
        if ( c != 0 ) return c;
        c = Integer.compare(this.fcJanCnt, that.fcJanCnt);
        if ( c != 0 ) return c;
        c = Integer.compare(this.fcFebCnt, that.fcFebCnt);
        if ( c != 0 ) return c;
        c = Integer.compare(this.fcMarCnt, that.fcMarCnt);
        if ( c != 0 ) return c;
        c = Integer.compare(this.fcAprCnt, that.fcAprCnt);
        if ( c != 0 ) return c;
        c = Integer.compare(this.fcMayCnt, that.fcMayCnt);
        if ( c != 0 ) return c;
        c = Integer.compare(this.fcJunCnt, that.fcJunCnt);
        if ( c != 0 ) return c;
        c = Integer.compare(this.fcJulCnt, that.fcJulCnt);
        if ( c != 0 ) return c;
        c = Integer.compare(this.fcAugCnt, that.fcAugCnt);
        if ( c != 0 ) return c;
        c = Integer.compare(this.fcSepCnt, that.fcSepCnt);
        if ( c != 0 ) return c;
        c = Integer.compare(this.fcOctCnt, that.fcOctCnt);
        if ( c != 0 ) return c;
        c = Integer.compare(this.fcNovCnt, that.fcNovCnt);
        if ( c != 0 ) return c;
        c = Integer.compare(this.fcDecCnt, that.fcDecCnt);
        if ( c != 0 ) return c;
        c = this.fcTypeEffDate.compareTo(that.fcTypeEffDate);
        if ( c != 0 ) return c;
        c = this.fcTypeLogonId.compareTo(that.fcTypeLogonId);
        if ( c != 0 ) return c;
        c = Integer.compare(this.fcReqCnt, that.fcReqCnt);
        if ( c != 0 ) return c;
        c = Integer.compare(this.lastYearFcCnt, that.lastYearFcCnt);
        if ( c != 0 ) return c;
        c = this.scfcPrtInd.compareTo(that.scfcPrtInd);
        if ( c != 0 ) return c;
        c = this.scfcInclSlFlag.compareTo(that.scfcInclSlFlag);
        if ( c != 0 ) return c;
        c = this.nextSchedFcDate.compareTo(that.nextSchedFcDate);
        if ( c != 0 ) return c;
        c = this.prtAllTrustFlag.compareTo(that.prtAllTrustFlag);
        if ( c != 0 ) return c;
        c = this.createDate.compareTo(that.createDate);
        if ( c != 0 ) return c;
        c = this.fcFreqEffDt.compareTo(that.fcFreqEffDt);
        if ( c != 0 ) return c;
        c = this.fcFreqLogonId.compareTo(that.fcFreqLogonId);
        if ( c != 0 ) return c;
        c = Integer.compare(this.equityCycNo, that.equityCycNo);
        if ( c != 0 ) return c;
        c = this.equityDate.compareTo(that.equityDate);
        if ( c != 0 ) return c;
        c = Integer.compare(this.invnCycNo, that.invnCycNo);
        if ( c != 0 ) return c;
        c = this.invnDate.compareTo(that.invnDate);
        if ( c != 0 ) return c;
        c = Integer.compare(this.recclAmt, that.recclAmt);
        if ( c != 0 ) return c;
        c = this.scfcTrkNonFlag.compareTo(that.scfcTrkNonFlag);
        if ( c != 0 ) return c;
        c = this.scfcSent01Date.compareTo(that.scfcSent01Date);
        if ( c != 0 ) return c;
        c = this.scfcSent02Date.compareTo(that.scfcSent02Date);
        if ( c != 0 ) return c;
        c = this.pblFundDayInd.compareTo(that.pblFundDayInd);
        if ( c != 0 ) return c;
        c = this.pcsPurgeFlag.compareTo(that.pcsPurgeFlag);
        if ( c != 0 ) return c;
        c = this.ovlnTolerFlag.compareTo(that.ovlnTolerFlag);
        if ( c != 0 ) return c;
        c = Integer.compare(this.apprPurgeMinAmt, that.apprPurgeMinAmt);
        if ( c != 0 ) return c;
        c = Integer.compare(this.apprPurgeMinPct, that.apprPurgeMinPct);
        if ( c != 0 ) return c;
        c = Integer.compare(this.apprPurgeDayCnt, that.apprPurgeDayCnt);
        if ( c != 0 ) return c;
        c = this.credHoldDate.compareTo(that.credHoldDate);
        if ( c != 0 ) return c;
        c = this.ovrdFundDateInd.compareTo(that.ovrdFundDateInd);
        if ( c != 0 ) return c;
        c = this.noTrustDtlFlag.compareTo(that.noTrustDtlFlag);
        if ( c != 0 ) return c;
        c = this.purchContactName.compareTo(that.purchContactName);
        if ( c != 0 ) return c;
        c = this.purchPhoneNo.compareTo(that.purchPhoneNo);
        if ( c != 0 ) return c;
        c = this.purchFaxNo.compareTo(that.purchFaxNo);
        if ( c != 0 ) return c;
        c = this.adminLogonId.compareTo(that.adminLogonId);
        if ( c != 0 ) return c;
        c = this.serialNoFlag.compareTo(that.serialNoFlag);
        if ( c != 0 ) return c;
        c = this.purchUpdDate.compareTo(that.purchUpdDate);
        if ( c != 0 ) return c;
        c = this.purchUpdLogonId.compareTo(that.purchUpdLogonId);
        if ( c != 0 ) return c;
        c = this.explodeInd.compareTo(that.explodeInd);
        if ( c != 0 ) return c;
        c = this.filingTypeCode.compareTo(that.filingTypeCode);
        if ( c != 0 ) return c;
        c = Integer.compare(this.epdDelayDaysCnt, that.epdDelayDaysCnt);
        if ( c != 0 ) return c;
        c = this.salesZipCode.compareTo(that.salesZipCode);
        if ( c != 0 ) return c;
        c = this.finStEntryDate.compareTo(that.finStEntryDate);
        if ( c != 0 ) return c;
        c = this.trstSpecPrtDate.compareTo(that.trstSpecPrtDate);
        if ( c != 0 ) return c;
        c = this.trstSpecPrtInd.compareTo(that.trstSpecPrtInd);
        if ( c != 0 ) return c;
        c = this.credPrtFlag.compareTo(that.credPrtFlag);
        if ( c != 0 ) return c;
        c = this.credPrtFreqCode.compareTo(that.credPrtFreqCode);
        if ( c != 0 ) return c;
        c = this.stProvCode.compareTo(that.stProvCode);
        if ( c != 0 ) return c;
        c = this.zipPostalCode.compareTo(that.zipPostalCode);
        if ( c != 0 ) return c;
        c = this.countryCode.compareTo(that.countryCode);
        if ( c != 0 ) return c;
        c = this.addr1Name.compareTo(that.addr1Name);
        if ( c != 0 ) return c;
        c = this.addr2Name.compareTo(that.addr2Name);
        if ( c != 0 ) return c;
        c = this.cityName.compareTo(that.cityName);
        if ( c != 0 ) return c;
        c = this.countyName.compareTo(that.countyName);
        if ( c != 0 ) return c;
        c = this.legalName.compareTo(that.legalName);
        if ( c != 0 ) return c;
        c = this.dbaName.compareTo(that.dbaName);
        if ( c != 0 ) return c;
        c = this.contactName.compareTo(that.contactName);
        if ( c != 0 ) return c;
        c = this.contactTitle.compareTo(that.contactTitle);
        if ( c != 0 ) return c;
        c = this.locDesc.compareTo(that.locDesc);
        if ( c != 0 ) return c;
        c = Integer.compare(this.cashPriorityNo, that.cashPriorityNo);
        if ( c != 0 ) return c;
        c = this.xcSourceFlag.compareTo(that.xcSourceFlag);
        if ( c != 0 ) return c;
        c = this.xcEligibleFlag.compareTo(that.xcEligibleFlag);
        if ( c != 0 ) return c;
        c = this.xcCredStatCode.compareTo(that.xcCredStatCode);
        if ( c != 0 ) return c;
        c = this.slWeekdayFlag.compareTo(that.slWeekdayFlag);
        if ( c != 0 ) return c;
        c = this.prtCredMemoInd.compareTo(that.prtCredMemoInd);
        if ( c != 0 ) return c;
        c = this.cmApplyInd.compareTo(that.cmApplyInd);
        if ( c != 0 ) return c;
        c = this.cmFundFreqCode.compareTo(that.cmFundFreqCode);
        if ( c != 0 ) return c;
        c = this.cmFundDayCode.compareTo(that.cmFundDayCode);
        if ( c != 0 ) return c;
        c = this.prtCurrencyFlag.compareTo(that.prtCurrencyFlag);
        if ( c != 0 ) return c;
        c = this.attrnOvrdFlag.compareTo(that.attrnOvrdFlag);
        if ( c != 0 ) return c;
        c = this.attrnExpDate.compareTo(that.attrnExpDate);
        if ( c != 0 ) return c;
        c = this.shortName.compareTo(that.shortName);
        if ( c != 0 ) return c;
        c = this.trstSpecPrtCode.compareTo(that.trstSpecPrtCode);
        if ( c != 0 ) return c;
        c = this.emailAddrName.compareTo(that.emailAddrName);
        if ( c != 0 ) return c;
        c = this.pdeSortSeqCode.compareTo(that.pdeSortSeqCode);
        if ( c != 0 ) return c;
        c = this.mstrInvPrtCode.compareTo(that.mstrInvPrtCode);
        if ( c != 0 ) return c;
        c = this.mstrInvReqInd.compareTo(that.mstrInvReqInd);
        if ( c != 0 ) return c;
        c = this.trustEdiCode.compareTo(that.trustEdiCode);
        if ( c != 0 ) return c;
        c = this.vatRgstrNo.compareTo(that.vatRgstrNo);
        if ( c != 0 ) return c;
        c = this.vatElectionInd.compareTo(that.vatElectionInd);
        if ( c != 0 ) return c;
        c = this.languageCode.compareTo(that.languageCode);
        if ( c != 0 ) return c;
        c = this.holdNegPblInd.compareTo(that.holdNegPblInd);
        if ( c != 0 ) return c;
        c = this.faxStubsFlag.compareTo(that.faxStubsFlag);
        if ( c != 0 ) return c;
        c = this.polCredMemoFlag.compareTo(that.polCredMemoFlag);
        if ( c != 0 ) return c;
        c = Integer.compare(this.epdGraceDaysCnt, that.epdGraceDaysCnt);
        if ( c != 0 ) return c;
        c = this.epdFundFreqCode.compareTo(that.epdFundFreqCode);
        if ( c != 0 ) return c;
        c = Integer.compare(this.epdFundDaysCnt, that.epdFundDaysCnt);
        if ( c != 0 ) return c;
        c = this.credClaimFlag.compareTo(that.credClaimFlag);
        if ( c != 0 ) return c;
        c = Integer.compare(this.credClaimDayCnt, that.credClaimDayCnt);
        if ( c != 0 ) return c;
        c = Integer.compare(this.rmbCmDiscPgmNo, that.rmbCmDiscPgmNo);
        if ( c != 0 ) return c;
        c = this.skuIdFlag.compareTo(that.skuIdFlag);
        if ( c != 0 ) return c;
        c = this.prtCredNoteInd.compareTo(that.prtCredNoteInd);
        if ( c != 0 ) return c;
        c = this.uccOrgNo.compareTo(that.uccOrgNo);
        if ( c != 0 ) return c;
        c = this.uccOrgSt.compareTo(that.uccOrgSt);
        if ( c != 0 ) return c;
        c = this.systemCreateFlag.compareTo(that.systemCreateFlag);
        if ( c != 0 ) return c;
        c = this.currencyCode.compareTo(that.currencyCode);
        if ( c != 0 ) return c;
        c = this.autoCashFlag.compareTo(that.autoCashFlag);
        if ( c != 0 ) return c;
        c = this.incomplCashFlag.compareTo(that.incomplCashFlag);
        if ( c != 0 ) return c;
        c = Integer.compare(this.podPresDayCnt, that.podPresDayCnt);
        if ( c != 0 ) return c;
        c = this.podPresDateFlag.compareTo(that.podPresDateFlag);
        if ( c != 0 ) return c;
        c = this.trueUpEarlyFlag.compareTo(that.trueUpEarlyFlag);
        if ( c != 0 ) return c;
        c = this.trueUpLateFlag.compareTo(that.trueUpLateFlag);
        if ( c != 0 ) return c;
        c = Integer.compare(this.tdfRiskPct, that.tdfRiskPct);
        if ( c != 0 ) return c;
        c = Integer.compare(this.riskFeeRate, that.riskFeeRate);
        if ( c != 0 ) return c;
        c = this.riskFeeFiuFlag.compareTo(that.riskFeeFiuFlag);
        if ( c != 0 ) return c;
        c = this.portTypeCode.compareTo(that.portTypeCode);
        if ( c != 0 ) return c;
        c = this.servTrustFlag.compareTo(that.servTrustFlag);
        if ( c != 0 ) return c;
        c = Integer.compare(this.lossReserve2Rate, that.lossReserve2Rate);
        if ( c != 0 ) return c;
        c = Integer.compare(this.grossupPct, that.grossupPct);
        if ( c != 0 ) return c;
        c = this.adbDiscInd.compareTo(that.adbDiscInd);
        if ( c != 0 ) return c;
        c = Integer.compare(this.distFundOsAmt, that.distFundOsAmt);
        if ( c != 0 ) return c;
        c = this.dlrChrgTdfFlag.compareTo(that.dlrChrgTdfFlag);
        if ( c != 0 ) return c;
        c = this.miscId.compareTo(that.miscId);
        if ( c != 0 ) return c;
        c = this.servFeeFreqInd.compareTo(that.servFeeFreqInd);
        if ( c != 0 ) return c;
        c = Integer.compare(this.collFeeRate, that.collFeeRate);
        if ( c != 0 ) return c;
        c = Integer.compare(this.fundHoldPct, that.fundHoldPct);
        if ( c != 0 ) return c;
        c = Integer.compare(this.fundLimitAmt, that.fundLimitAmt);
        if ( c != 0 ) return c;
        c = Integer.compare(this.servFeeBranchNo, that.servFeeBranchNo);
        if ( c != 0 ) return c;
        c = this.trueUpIntFlag.compareTo(that.trueUpIntFlag);
        if ( c != 0 ) return c;
        c = Integer.compare(this.curtGraceDayCnt, that.curtGraceDayCnt);
        if ( c != 0 ) return c;
        c = Integer.compare(this.anrMonthsCnt, that.anrMonthsCnt);
        if ( c != 0 ) return c;
        c = this.mstrAgreeDate.compareTo(that.mstrAgreeDate);
        if ( c != 0 ) return c;
        c = this.cmApplyRuleCode.compareTo(that.cmApplyRuleCode);
        if ( c != 0 ) return c;
        c = this.cmApplyExcpCode.compareTo(that.cmApplyExcpCode);
        if ( c != 0 ) return c;
        c = this.scrtzEligCd.compareTo(that.scrtzEligCd);
        if ( c != 0 ) return c;
        c = this.securedFl.compareTo(that.securedFl);
        if ( c != 0 ) return c;
        c = this.scrtzEffDate.compareTo(that.scrtzEffDate);
        if ( c != 0 ) return c;
        c = Integer.compare(this.scrtzParticRt, that.scrtzParticRt);
        if ( c != 0 ) return c;
        c = this.scrtzPoolId.compareTo(that.scrtzPoolId);
        if ( c != 0 ) return c;
        c = this.adbBillMethCode.compareTo(that.adbBillMethCode);
        if ( c != 0 ) return c;
        c = this.lateFeeBillInd.compareTo(that.lateFeeBillInd);
        if ( c != 0 ) return c;
        c = this.edocsInd.compareTo(that.edocsInd);
        if ( c != 0 ) return c;
        c = this.edocsStartDate.compareTo(that.edocsStartDate);
        if ( c != 0 ) return c;
        c = this.edocsPrtEndDate.compareTo(that.edocsPrtEndDate);
        if ( c != 0 ) return c;
        c = Integer.compare(this.auxCarrNo, that.auxCarrNo);
        if ( c != 0 ) return c;
        c = this.curtPrepaidFlag.compareTo(that.curtPrepaidFlag);
        if ( c != 0 ) return c;
        c = this.stmtSortCode.compareTo(that.stmtSortCode);
        if ( c != 0 ) return c;
        c = this.stmtPageBrkFlag.compareTo(that.stmtPageBrkFlag);
        if ( c != 0 ) return c;
        c = this.recvCmPrtFlag.compareTo(that.recvCmPrtFlag);
        if ( c != 0 ) return c;
        c = this.slStmtPrtFl.compareTo(that.slStmtPrtFl);
        if ( c != 0 ) return c;
        c = Integer.compare(this.epdCbgnDay1No, that.epdCbgnDay1No);
        if ( c != 0 ) return c;
        c = Integer.compare(this.epdCbgnDay2No, that.epdCbgnDay2No);
        if ( c != 0 ) return c;
        c = Integer.compare(this.epdCbgnDay3No, that.epdCbgnDay3No);
        if ( c != 0 ) return c;
        c = Integer.compare(this.epdCbgnDay4No, that.epdCbgnDay4No);
        if ( c != 0 ) return c;
        c = Integer.compare(this.epdCdueDay1No, that.epdCdueDay1No);
        if ( c != 0 ) return c;
        c = Integer.compare(this.epdCdueDay2No, that.epdCdueDay2No);
        if ( c != 0 ) return c;
        c = Integer.compare(this.epdCdueDay3No, that.epdCdueDay3No);
        if ( c != 0 ) return c;
        c = Integer.compare(this.epdCdueDay4No, that.epdCdueDay4No);
        if ( c != 0 ) return c;
        c = Integer.compare(this.epdCendDay1No, that.epdCendDay1No);
        if ( c != 0 ) return c;
        c = Integer.compare(this.epdCendDay2No, that.epdCendDay2No);
        if ( c != 0 ) return c;
        c = Integer.compare(this.epdCendDay3No, that.epdCendDay3No);
        if ( c != 0 ) return c;
        c = Integer.compare(this.epdCendDay4No, that.epdCendDay4No);
        if ( c != 0 ) return c;
        c = this.refundDistFdFl.compareTo(that.refundDistFdFl);
        if ( c != 0 ) return c;
        c = Integer.compare(this.cashAlgCode, that.cashAlgCode);
        if ( c != 0 ) return c;
        c = this.autoStlmntFl.compareTo(that.autoStlmntFl);
        if ( c != 0 ) return c;
        c = this.tranStmtFrmtCd.compareTo(that.tranStmtFrmtCd);
        if ( c != 0 ) return c;
        c = this.defNoPayFlag.compareTo(that.defNoPayFlag);
        if ( c != 0 ) return c;
        c = this.defComposRtFlag.compareTo(that.defComposRtFlag);
        if ( c != 0 ) return c;
        c = Integer.compare(this.defBankCode, that.defBankCode);
        if ( c != 0 ) return c;
        c = this.defTypeInd.compareTo(that.defTypeInd);
        if ( c != 0 ) return c;
        c = Integer.compare(this.defMinprimeRate, that.defMinprimeRate);
        if ( c != 0 ) return c;
        c = Integer.compare(this.defMaxprimeRate, that.defMaxprimeRate);
        if ( c != 0 ) return c;
        c = Integer.compare(this.straightRate, that.straightRate);
        if ( c != 0 ) return c;
        c = this.prtNoteStmtInd.compareTo(that.prtNoteStmtInd);
        if ( c != 0 ) return c;
        c = this.segmentCd.compareTo(that.segmentCd);
        if ( c != 0 ) return c;
        c = this.highRiskFl.compareTo(that.highRiskFl);
        if ( c != 0 ) return c;
        c = this.kmvId.compareTo(that.kmvId);
        if ( c != 0 ) return c;
        c = this.extIdTypeCd.compareTo(that.extIdTypeCd);
        if ( c != 0 ) return c;
        c = this.extIdNo.compareTo(that.extIdNo);
        if ( c != 0 ) return c;
        c = this.ofstTypeCd.compareTo(that.ofstTypeCd);
        if ( c != 0 ) return c;
        c = this.lineDetailFl.compareTo(that.lineDetailFl);
        if ( c != 0 ) return c;
        c = this.fixpSkipMo01Fl.compareTo(that.fixpSkipMo01Fl);
        if ( c != 0 ) return c;
        c = this.fixpSkipMo02Fl.compareTo(that.fixpSkipMo02Fl);
        if ( c != 0 ) return c;
        c = this.fixpSkipMo03Fl.compareTo(that.fixpSkipMo03Fl);
        if ( c != 0 ) return c;
        c = this.fixpSkipMo04Fl.compareTo(that.fixpSkipMo04Fl);
        if ( c != 0 ) return c;
        c = this.fixpSkipMo05Fl.compareTo(that.fixpSkipMo05Fl);
        if ( c != 0 ) return c;
        c = this.fixpSkipMo06Fl.compareTo(that.fixpSkipMo06Fl);
        if ( c != 0 ) return c;
        c = this.fixpSkipMo07Fl.compareTo(that.fixpSkipMo07Fl);
        if ( c != 0 ) return c;
        c = this.fixpSkipMo08Fl.compareTo(that.fixpSkipMo08Fl);
        if ( c != 0 ) return c;
        c = this.fixpSkipMo09Fl.compareTo(that.fixpSkipMo09Fl);
        if ( c != 0 ) return c;
        c = this.fixpSkipMo10Fl.compareTo(that.fixpSkipMo10Fl);
        if ( c != 0 ) return c;
        c = this.fixpSkipMo11Fl.compareTo(that.fixpSkipMo11Fl);
        if ( c != 0 ) return c;
        c = this.fixpSkipMo12Fl.compareTo(that.fixpSkipMo12Fl);
        if ( c != 0 ) return c;
        c = this.distPrchEmailNm.compareTo(that.distPrchEmailNm);
        if ( c != 0 ) return c;
        c = this.rstrFl.compareTo(that.rstrFl);
        if ( c != 0 ) return c;
        c = this.starFl.compareTo(that.starFl);
        if ( c != 0 ) return c;
        c = this.bnkrpFl.compareTo(that.bnkrpFl);
        if ( c != 0 ) return c;
        c = this.regSweepFl.compareTo(that.regSweepFl);
        if ( c != 0 ) return c;
        c = this.putReasonCd.compareTo(that.putReasonCd);
        if ( c != 0 ) return c;
        c = this.putReasonDt.compareTo(that.putReasonDt);
        if ( c != 0 ) return c;
        c = this.bankWatchFl.compareTo(that.bankWatchFl);
        if ( c != 0 ) return c;
        c = Integer.compare(this.ovlnTolerRt, that.ovlnTolerRt);
        if ( c != 0 ) return c;
        c = this.otbHeldPayFl.compareTo(that.otbHeldPayFl);
        if ( c != 0 ) return c;
        c = this.otbUnapplCashFl.compareTo(that.otbUnapplCashFl);
        if ( c != 0 ) return c;
        c = this.otbUnidCashFl.compareTo(that.otbUnidCashFl);
        if ( c != 0 ) return c;
        c = this.otbDefFl.compareTo(that.otbDefFl);
        if ( c != 0 ) return c;
        c = Integer.compare(this.pcsDefApprAmt, that.pcsDefApprAmt);
        if ( c != 0 ) return c;
        c = this.otbExpoExclInd.compareTo(that.otbExpoExclInd);
        if ( c != 0 ) return c;
        c = this.finanStmtInrmDt.compareTo(that.finanStmtInrmDt);
        if ( c != 0 ) return c;
        c = Integer.compare(this.finanStmtInrmCt, that.finanStmtInrmCt);
        if ( c != 0 ) return c;
        c = this.finanStmtExtnDt.compareTo(that.finanStmtExtnDt);
        if ( c != 0 ) return c;
        c = this.publicPrivInd.compareTo(that.publicPrivInd);
        if ( c != 0 ) return c;
        c = this.lastRevExtnDt.compareTo(that.lastRevExtnDt);
        if ( c != 0 ) return c;
        c = this.finanStmtQualCd.compareTo(that.finanStmtQualCd);
        if ( c != 0 ) return c;
        c = this.endUserNm.compareTo(that.endUserNm);
        if ( c != 0 ) return c;
        c = this.mraCustId.compareTo(that.mraCustId);
        if ( c != 0 ) return c;
        c = this.defOfstPrincFl.compareTo(that.defOfstPrincFl);
        if ( c != 0 ) return c;
        c = this.defActivPageFl.compareTo(that.defActivPageFl);
        if ( c != 0 ) return c;
        c = this.undwrGdlnExcpFl.compareTo(that.undwrGdlnExcpFl);
        if ( c != 0 ) return c;
        c = this.dlrPreauthFl.compareTo(that.dlrPreauthFl);
        if ( c != 0 ) return c;
        c = this.preauthAllowFl.compareTo(that.preauthAllowFl);
        if ( c != 0 ) return c;
        c = this.ediStmtTypeCd.compareTo(that.ediStmtTypeCd);
        if ( c != 0 ) return c;
        c = this.ediMfgDistFlag.compareTo(that.ediMfgDistFlag);
        if ( c != 0 ) return c;
        c = this.epdCmFl.compareTo(that.epdCmFl);
        if ( c != 0 ) return c;
        c = this.mmtsCmprsFrmtFl.compareTo(that.mmtsCmprsFrmtFl);
        if ( c != 0 ) return c;
        c = this.prpCrtWavRprFl.compareTo(that.prpCrtWavRprFl);
        if ( c != 0 ) return c;
        c = Integer.compare(this.comsBigBufferCt, that.comsBigBufferCt);
        if ( c != 0 ) return c;
        c = this.dlvVrfctnFl.compareTo(that.dlvVrfctnFl);
        if ( c != 0 ) return c;
        c = this.rcpCrtTaxInvFl.compareTo(that.rcpCrtTaxInvFl);
        if ( c != 0 ) return c;
        c = this.overseasDistFl.compareTo(that.overseasDistFl);
        if ( c != 0 ) return c;
        c = this.mobileNo.compareTo(that.mobileNo);
        if ( c != 0 ) return c;
        c = this.servLvlCatCd.compareTo(that.servLvlCatCd);
        if ( c != 0 ) return c;
        c = this.partyId.compareTo(that.partyId);
        if ( c != 0 ) return c;
        c = Integer.compare(this.touchlessSyncId, that.touchlessSyncId);
        if ( c != 0 ) return c;
        c = this.priorChrgOffDt.compareTo(that.priorChrgOffDt);
        if ( c != 0 ) return c;
        c = Integer.compare(this.totChrgOffAmt, that.totChrgOffAmt);
        if ( c != 0 ) return c;
        c = this.ddOvrdSweepFl.compareTo(that.ddOvrdSweepFl);
        if ( c != 0 ) return c;
        c = this.prevScrtzPoolId.compareTo(that.prevScrtzPoolId);
        if ( c != 0 ) return c;
        c = this.lastSpecRevDate.compareTo(that.lastSpecRevDate);
        if ( c != 0 ) return c;
        c = this.loanCommitFl.compareTo(that.loanCommitFl);
        if ( c != 0 ) return c;
        c = this.loanCommitRenDt.compareTo(that.loanCommitRenDt);
        if ( c != 0 ) return c;
        c = this.ckDeliveryCd.compareTo(that.ckDeliveryCd);
        if ( c != 0 ) return c;
        c = Integer.compare(this.wcisId, that.wcisId);
        if ( c != 0 ) return c;
        c = this.naicsCd.compareTo(that.naicsCd);
        if ( c != 0 ) return c;
        c = this.custNoteTx.compareTo(that.custNoteTx);
        if ( c != 0 ) return c;
        c = this.naceCd.compareTo(that.naceCd);
        if ( c != 0 ) return c;
        c = this.collatSegmentDs.compareTo(that.collatSegmentDs);
        if ( c != 0 ) return c;
        c = this.auditTs.compareTo(that.auditTs);
        if ( c != 0 ) return c;
        c = this.auditLogonId.compareTo(that.auditLogonId);
        if ( c != 0 ) return c;
        c = this.auditProcessTx.compareTo(that.auditProcessTx);
        if ( c != 0 ) return c;
        c = this.auditDeleteFl.compareTo(that.auditDeleteFl);
        if ( c != 0 ) return c;
        c = Integer.compare(this.netRelianceAmt, that.netRelianceAmt);
        if ( c != 0 ) return c;
        c = this.lastAmlRevDt.compareTo(that.lastAmlRevDt);
        if ( c != 0 ) return c;
        c = this.ecrrCd.compareTo(that.ecrrCd);
        if ( c != 0 ) return c;
        c = this.craCd.compareTo(that.craCd);
        if ( c != 0 ) return c;
        c = this.craEffDt.compareTo(that.craEffDt);
        if ( c != 0 ) return c;
        c = this.craEndDt.compareTo(that.craEndDt);
        if ( c != 0 ) return c;
        c = Integer.compare(this.hardCredLnAmt, that.hardCredLnAmt);
        if ( c != 0 ) return c;
        c = this.workoutFl.compareTo(that.workoutFl);
        if ( c != 0 ) return c;
        c = this.workoutResolveCd.compareTo(that.workoutResolveCd);
        if ( c != 0 ) return c;
        c = this.origEffDt.compareTo(that.origEffDt);
        if ( c != 0 ) return c;
        c = this.acquireDt.compareTo(that.acquireDt);
        if ( c != 0 ) return c;
        c = Integer.compare(this.rooftopCnt, that.rooftopCnt);
        if ( c != 0 ) return c;
        c = this.cmrclDsclrFl.compareTo(that.cmrclDsclrFl);
        if ( c != 0 ) return c;
        c = this.distAgreeDt.compareTo(that.distAgreeDt);
        if ( c != 0 ) return c;
        c = this.wfbnaDocFl.compareTo(that.wfbnaDocFl);
        if ( c != 0 ) return c;
        c = this.custDescTx.compareTo(that.custDescTx);
        if ( c != 0 ) return c;
        c = this.currEffDt.compareTo(that.currEffDt);
        if ( c != 0 ) return c;
        c = this.retailStlmntFl.compareTo(that.retailStlmntFl);
        if ( c != 0 ) return c;
        c = this.governLawCd.compareTo(that.governLawCd);
        if ( c != 0 ) return c;
        c = Integer.compare(this.totalEmplCnt, that.totalEmplCnt);
        if ( c != 0 ) return c;
        c = Integer.compare(this.totalAssetAmt, that.totalAssetAmt);
        if ( c != 0 ) return c;
        c = this.agreeRestateDt.compareTo(that.agreeRestateDt);
        if ( c != 0 ) return c;
        c = this.totalEmplDt.compareTo(that.totalEmplDt);
        if ( c != 0 ) return c;
        c = this.totalAssetDt.compareTo(that.totalAssetDt);
        if ( c != 0 ) return c;
        c = this.annualSalesDt.compareTo(that.annualSalesDt);
        if ( c != 0 ) return c;
        c = Integer.compare(this.hardClPydownAmt, that.hardClPydownAmt);
        if ( c != 0 ) return c;
        c = Integer.compare(this.wcisCupId, that.wcisCupId);
        if ( c != 0 ) return c;
        c = Integer.compare(this.wcisSupId, that.wcisSupId);
        if ( c != 0 ) return c;
        c = Integer.compare(this.hardTempClAmt, that.hardTempClAmt);
        if ( c != 0 ) return c;
        c = Integer.compare(this.hardTempPdwnAmt, that.hardTempPdwnAmt);
        if ( c != 0 ) return c;
        c = this.stockExchMicId.compareTo(that.stockExchMicId);
        if ( c != 0 ) return c;
        c = this.stockTickerId.compareTo(that.stockTickerId);
        if ( c != 0 ) return c;
        c = this.riskCountryCd.compareTo(that.riskCountryCd);
        if ( c != 0 ) return c;
        c = Integer.compare(this.totRecovAmt, that.totRecovAmt);
        if ( c != 0 ) return c;
        c = this.taxIdTypeCd.compareTo(that.taxIdTypeCd);
        if ( c != 0 ) return c;
        c = this.bnkrpDt.compareTo(that.bnkrpDt);
        if ( c != 0 ) return c;
        c = this.apiHApprUpdFl.compareTo(that.apiHApprUpdFl);
        if ( c == 0 && !(that.canEqual(this) && this.canEqual(that)) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
    }
    
    private static final ExternalDecimalAsIntField CUST_NO = factory.getExternalDecimalAsIntField(5, true);
    private static final StringField CUST_TYPE_CODE = factory.getStringField(2);
    private static final StringField CUST_STATUS_CODE = factory.getStringField(5);
    private static final ExternalDecimalAsIntField AFFIL_CUST_NO = factory.getExternalDecimalAsIntField(5, true);
    private static final ExternalDecimalAsIntField CNTL_ENT_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final StringField BILL_ACCT_REP_CODE = factory.getStringField(4);
    private static final StringField BUS_CODE = factory.getStringField(3);
    private static final ExternalDecimalAsIntField COLL_BRANCH_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final ExternalDecimalAsIntField COLL_DELAY_CNT = factory.getExternalDecimalAsIntField(3, true);
    private static final StringField RATE_360_FLAG = factory.getStringField(2);
    private static final StringField ADV_PAY_FLAG = factory.getStringField(2);
    private static final StringField RECOURSE_FLAG = factory.getStringField(2);
    private static final ExternalDecimalAsIntField SHIP_AUTH_AMT = factory.getExternalDecimalAsIntField(7, true);
    private static final StringField BILL_CUST_FLAG = factory.getStringField(2);
    private static final StringField SL_CYCLE_NO = factory.getStringField(2);
    private static final StringField SHIP_AUTH_FLAG = factory.getStringField(2);
    private static final StringField FINAN_STMT_DATE = factory.getStringField(8);
    private static final DateTimeFormatter FINAN_STMT_DATE_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final StringField DUNN_SIC_CODE = factory.getStringField(9);
    private static final StringField DUNN_NO = factory.getStringField(10);
    private static final ExternalDecimalAsIntField BUS_ESTAB_YEAR_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final ExternalDecimalAsIntField COMM_BEGIN_DAY_1_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final ExternalDecimalAsIntField COMM_END_DAY_1_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final ExternalDecimalAsIntField COMM_DUE_DAY_1_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final ExternalDecimalAsIntField COMM_BEGIN_DAY_2_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final ExternalDecimalAsIntField COMM_END_DAY_2_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final ExternalDecimalAsIntField COMM_DUE_DAY_2_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final ExternalDecimalAsIntField COMM_BEGIN_DAY_3_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final ExternalDecimalAsIntField COMM_END_DAY_3_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final ExternalDecimalAsIntField COMM_DUE_DAY_3_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final ExternalDecimalAsIntField COMM_BEGIN_DAY_4_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final ExternalDecimalAsIntField COMM_END_DAY_4_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final ExternalDecimalAsIntField COMM_DUE_DAY_4_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final StringField REPUR_FLAG = factory.getStringField(2);
    private static final StringField STRIP_PASS_FLAG = factory.getStringField(2);
    private static final ExternalDecimalAsIntField REV_FREQ_CODE = factory.getExternalDecimalAsIntField(3, true);
    private static final StringField TRUST_PRTFORM_CODE = factory.getStringField(2);
    private static final StringField TRUST_PRT_CODE = factory.getStringField(2);
    private static final ExternalDecimalAsIntField TAX_ID_UPD_CNT = factory.getExternalDecimalAsIntField(3, true);
    private static final StringField TAX_ID_NO = factory.getStringField(10);
    private static final StringField INT_CRED_EXP_DATE = factory.getStringField(8);
    private static final DateTimeFormatter INT_CRED_EXP_DATE_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final ExternalDecimalAsIntField TCFC_START_YEAR_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final StringField APPR_TYPE_CODE = factory.getStringField(2);
    private static final StringField BILL_CONS_FLAG = factory.getStringField(2);
    private static final StringField BILL_SUB_DIST_FLAG = factory.getStringField(2);
    private static final StringField LAST_REV_DATE = factory.getStringField(8);
    private static final DateTimeFormatter LAST_REV_DATE_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final StringField INS_FLAG = factory.getStringField(2);
    private static final ExternalDecimalAsIntField COVERAGE_INS_AMT = factory.getExternalDecimalAsIntField(7, true);
    private static final StringField POLICY_EXP_DATE = factory.getStringField(8);
    private static final DateTimeFormatter POLICY_EXP_DATE_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final ExternalDecimalAsIntField OVRD_INS_RATE = factory.getExternalDecimalAsIntField(5, true);
    private static final ExternalDecimalAsIntField INS_RATE_CODE = factory.getExternalDecimalAsIntField(3, true);
    private static final StringField SOT_UPD_DATE = factory.getStringField(8);
    private static final DateTimeFormatter SOT_UPD_DATE_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final StringField REPO_UPD_DATE = factory.getStringField(8);
    private static final DateTimeFormatter REPO_UPD_DATE_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final ExternalDecimalAsIntField REPO_AMT = factory.getExternalDecimalAsIntField(7, true);
    private static final ExternalDecimalAsIntField SOT_BAL_DUE_AMT = factory.getExternalDecimalAsIntField(7, true);
    private static final ExternalDecimalAsIntField RESERVE_FUND_2_AMT = factory.getExternalDecimalAsIntField(7, true);
    private static final ExternalDecimalAsIntField RESERVE_FUND_1_AMT = factory.getExternalDecimalAsIntField(7, true);
    private static final ExternalDecimalAsIntField INCT_PAY_BRANCH_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final StringField INCT_BASED_ON_CODE = factory.getStringField(2);
    private static final StringField INCT_PAY_FREQ_CODE = factory.getStringField(2);
    private static final StringField INCT_PAID_DATE = factory.getStringField(8);
    private static final DateTimeFormatter INCT_PAID_DATE_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final ExternalDecimalAsIntField BILL_ADDR_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final ExternalDecimalAsIntField PAY_ADDR_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final StringField PHONE_NO = factory.getStringField(16);
    private static final StringField FAX_NO = factory.getStringField(16);
    private static final ExternalDecimalAsIntField FC_BRANCH_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final ExternalDecimalAsIntField FC_CYCLE_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final StringField FC_SORT_SEQ_CODE = factory.getStringField(3);
    private static final StringField FC_TYPE_CODE = factory.getStringField(2);
    private static final StringField FC_LAST_DATE = factory.getStringField(8);
    private static final DateTimeFormatter FC_LAST_DATE_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final StringField FC_LAST_INITIALS = factory.getStringField(4);
    private static final StringField EST_LOSS_INITIALS = factory.getStringField(4);
    private static final ExternalDecimalAsIntField EST_LOSS_AMT = factory.getExternalDecimalAsIntField(7, true);
    private static final ExternalDecimalAsIntField DIST_MFG_OS_AMT = factory.getExternalDecimalAsIntField(9, true);
    private static final StringField UCC_ST_FILING_NO = factory.getStringField(11);
    private static final StringField UCC_ST_EXP_DATE = factory.getStringField(8);
    private static final DateTimeFormatter UCC_ST_EXP_DATE_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final StringField UCC_CNTY_FILING_NO = factory.getStringField(11);
    private static final StringField UCC_CNTY_EXP_DATE = factory.getStringField(8);
    private static final DateTimeFormatter UCC_CNTY_EXP_DATE_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final StringField INIT_CHRG_RPT_FLAG = factory.getStringField(2);
    private static final StringField COLL_DELAY_IND = factory.getStringField(2);
    private static final ExternalDecimalAsIntField SL_GRACE_DAYS_CNT = factory.getExternalDecimalAsIntField(3, true);
    private static final ExternalDecimalAsIntField CRED_SCORE_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final StringField INDUSTRY_CODE = factory.getStringField(3);
    private static final StringField DLR_REP_CODE = factory.getStringField(4);
    private static final StringField BOS_AUDIT_DATE = factory.getStringField(8);
    private static final DateTimeFormatter BOS_AUDIT_DATE_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final ExternalDecimalAsIntField BOS_CYCLE_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final StringField BANK_AUDIT_DATE = factory.getStringField(8);
    private static final DateTimeFormatter BANK_AUDIT_DATE_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final ExternalDecimalAsIntField BANK_CYCLE_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final ExternalDecimalAsIntField ADMIN_FLAT_AMT = factory.getExternalDecimalAsIntField(5, true);
    private static final ExternalDecimalAsIntField ADMIN_RATE = factory.getExternalDecimalAsIntField(4, true);
    private static final ExternalDecimalAsIntField ADMIN_MIN_OS_AMT = factory.getExternalDecimalAsIntField(6, true);
    private static final ExternalDecimalAsIntField LOSS_RESERVE_RATE = factory.getExternalDecimalAsIntField(5, true);
    private static final ExternalDecimalAsIntField LOSS_RESERVE_AMT = factory.getExternalDecimalAsIntField(7, true);
    private static final ExternalDecimalAsIntField CNTL_BRANCH_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final StringField FSR_REP_CODE = factory.getStringField(4);
    private static final StringField LAST_FSR_REP_CODE = factory.getStringField(4);
    private static final StringField TITLE_CODE = factory.getStringField(3);
    private static final StringField SHIP_CODE_REQ_FLAG = factory.getStringField(2);
    private static final ExternalDecimalAsIntField PLB_BRANCH_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final ExternalDecimalAsIntField PLB_PAY_ADDR_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final ExternalDecimalAsIntField ANNUAL_SALES_AMT = factory.getExternalDecimalAsIntField(8, true);
    private static final ExternalDecimalAsIntField COGS_AMT = factory.getExternalDecimalAsIntField(8, true);
    private static final StringField BANK_NAME = factory.getStringField(16);
    private static final StringField LEAD_SOURCE_CODE = factory.getStringField(3);
    private static final ExternalDecimalAsIntField LEAD_CUST_NO = factory.getExternalDecimalAsIntField(5, true);
    private static final StringField PROJ_CODE = factory.getStringField(5);
    private static final StringField SALES_COGS_DATE = factory.getStringField(8);
    private static final DateTimeFormatter SALES_COGS_DATE_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final StringField CR_RV_REQST_IND = factory.getStringField(2);
    private static final StringField CR_RV_REQST_DATE = factory.getStringField(8);
    private static final DateTimeFormatter CR_RV_REQST_DATE_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final StringField CR_RV_DLR_REP_CODE = factory.getStringField(4);
    private static final ExternalDecimalAsIntField EST_CRED_LN_AMT = factory.getExternalDecimalAsIntField(7, true);
    private static final StringField FIN_SOURCE_1_CODE = factory.getStringField(5);
    private static final StringField FIN_SOURCE_2_CODE = factory.getStringField(5);
    private static final StringField FIN_SOURCE_3_CODE = factory.getStringField(5);
    private static final StringField FIN_SOURCE_4_CODE = factory.getStringField(5);
    private static final StringField COMPUT_SYSTEM_IND = factory.getStringField(2);
    private static final StringField CNTL_INVN_FLAG = factory.getStringField(2);
    private static final StringField CNTL_ACCTRECV_FLAG = factory.getStringField(2);
    private static final StringField CNTL_ACCT_PBL_FLAG = factory.getStringField(2);
    private static final StringField MODEM_FLAG = factory.getStringField(2);
    private static final StringField SCFC_RECVD_DATE = factory.getStringField(8);
    private static final DateTimeFormatter SCFC_RECVD_DATE_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final ExternalDecimalAsIntField SEASONAL_CODE = factory.getExternalDecimalAsIntField(3, true);
    private static final ExternalDecimalAsIntField FC_JAN_CNT = factory.getExternalDecimalAsIntField(2, true);
    private static final ExternalDecimalAsIntField FC_FEB_CNT = factory.getExternalDecimalAsIntField(2, true);
    private static final ExternalDecimalAsIntField FC_MAR_CNT = factory.getExternalDecimalAsIntField(2, true);
    private static final ExternalDecimalAsIntField FC_APR_CNT = factory.getExternalDecimalAsIntField(2, true);
    private static final ExternalDecimalAsIntField FC_MAY_CNT = factory.getExternalDecimalAsIntField(2, true);
    private static final ExternalDecimalAsIntField FC_JUN_CNT = factory.getExternalDecimalAsIntField(2, true);
    private static final ExternalDecimalAsIntField FC_JUL_CNT = factory.getExternalDecimalAsIntField(2, true);
    private static final ExternalDecimalAsIntField FC_AUG_CNT = factory.getExternalDecimalAsIntField(2, true);
    private static final ExternalDecimalAsIntField FC_SEP_CNT = factory.getExternalDecimalAsIntField(2, true);
    private static final ExternalDecimalAsIntField FC_OCT_CNT = factory.getExternalDecimalAsIntField(2, true);
    private static final ExternalDecimalAsIntField FC_NOV_CNT = factory.getExternalDecimalAsIntField(2, true);
    private static final ExternalDecimalAsIntField FC_DEC_CNT = factory.getExternalDecimalAsIntField(2, true);
    private static final StringField FC_TYPE_EFF_DATE = factory.getStringField(8);
    private static final DateTimeFormatter FC_TYPE_EFF_DATE_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final StringField FC_TYPE_LOGON_ID = factory.getStringField(9);
    private static final ExternalDecimalAsIntField FC_REQ_CNT = factory.getExternalDecimalAsIntField(3, true);
    private static final ExternalDecimalAsIntField LAST_YEAR_FC_CNT = factory.getExternalDecimalAsIntField(3, true);
    private static final StringField SCFC_PRT_IND = factory.getStringField(2);
    private static final StringField SCFC_INCL_SL_FLAG = factory.getStringField(2);
    private static final StringField NEXT_SCHED_FC_DATE = factory.getStringField(8);
    private static final DateTimeFormatter NEXT_SCHED_FC_DATE_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final StringField PRT_ALL_TRUST_FLAG = factory.getStringField(2);
    private static final StringField CREATE_DATE = factory.getStringField(8);
    private static final DateTimeFormatter CREATE_DATE_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final StringField FC_FREQ_EFF_DT = factory.getStringField(8);
    private static final DateTimeFormatter FC_FREQ_EFF_DT_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final StringField FC_FREQ_LOGON_ID = factory.getStringField(9);
    private static final ExternalDecimalAsIntField EQUITY_CYC_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final StringField EQUITY_DATE = factory.getStringField(8);
    private static final DateTimeFormatter EQUITY_DATE_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final ExternalDecimalAsIntField INVN_CYC_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final StringField INVN_DATE = factory.getStringField(8);
    private static final DateTimeFormatter INVN_DATE_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final ExternalDecimalAsIntField RECCL_AMT = factory.getExternalDecimalAsIntField(7, true);
    private static final StringField SCFC_TRK_NON_FLAG = factory.getStringField(2);
    private static final StringField SCFC_SENT_01_DATE = factory.getStringField(8);
    private static final DateTimeFormatter SCFC_SENT_01_DATE_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final StringField SCFC_SENT_02_DATE = factory.getStringField(8);
    private static final DateTimeFormatter SCFC_SENT_02_DATE_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final StringField PBL_FUND_DAY_IND = factory.getStringField(2);
    private static final StringField PCS_PURGE_FLAG = factory.getStringField(2);
    private static final StringField OVLN_TOLER_FLAG = factory.getStringField(2);
    private static final ExternalDecimalAsIntField APPR_PURGE_MIN_AMT = factory.getExternalDecimalAsIntField(7, true);
    private static final ExternalDecimalAsIntField APPR_PURGE_MIN_PCT = factory.getExternalDecimalAsIntField(4, true);
    private static final ExternalDecimalAsIntField APPR_PURGE_DAY_CNT = factory.getExternalDecimalAsIntField(3, true);
    private static final StringField CRED_HOLD_DATE = factory.getStringField(8);
    private static final DateTimeFormatter CRED_HOLD_DATE_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final StringField OVRD_FUND_DATE_IND = factory.getStringField(2);
    private static final StringField NO_TRUST_DTL_FLAG = factory.getStringField(2);
    private static final StringField PURCH_CONTACT_NAME = factory.getStringField(26);
    private static final StringField PURCH_PHONE_NO = factory.getStringField(16);
    private static final StringField PURCH_FAX_NO = factory.getStringField(16);
    private static final StringField ADMIN_LOGON_ID = factory.getStringField(9);
    private static final StringField SERIAL_NO_FLAG = factory.getStringField(2);
    private static final StringField PURCH_UPD_DATE = factory.getStringField(8);
    private static final DateTimeFormatter PURCH_UPD_DATE_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final StringField PURCH_UPD_LOGON_ID = factory.getStringField(9);
    private static final StringField EXPLODE_IND = factory.getStringField(2);
    private static final StringField FILING_TYPE_CODE = factory.getStringField(4);
    private static final ExternalDecimalAsIntField EPD_DELAY_DAYS_CNT = factory.getExternalDecimalAsIntField(3, true);
    private static final StringField SALES_ZIP_CODE = factory.getStringField(16);
    private static final StringField FIN_ST_ENTRY_DATE = factory.getStringField(8);
    private static final DateTimeFormatter FIN_ST_ENTRY_DATE_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final StringField TRST_SPEC_PRT_DATE = factory.getStringField(8);
    private static final DateTimeFormatter TRST_SPEC_PRT_DATE_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final StringField TRST_SPEC_PRT_IND = factory.getStringField(2);
    private static final StringField CRED_PRT_FLAG = factory.getStringField(2);
    private static final StringField CRED_PRT_FREQ_CODE = factory.getStringField(2);
    private static final StringField ST_PROV_CODE = factory.getStringField(26);
    private static final StringField ZIP_POSTAL_CODE = factory.getStringField(16);
    private static final StringField COUNTRY_CODE = factory.getStringField(4);
    private static final StringField ADDR_1_NAME = factory.getStringField(103);
    private static final StringField ADDR_2_NAME = factory.getStringField(103);
    private static final StringField CITY_NAME = factory.getStringField(53);
    private static final StringField COUNTY_NAME = factory.getStringField(53);
    private static final StringField LEGAL_NAME = factory.getStringField(203);
    private static final StringField DBA_NAME = factory.getStringField(73);
    private static final StringField CONTACT_NAME = factory.getStringField(53);
    private static final StringField CONTACT_TITLE = factory.getStringField(53);
    private static final StringField LOC_DESC = factory.getStringField(53);
    private static final ExternalDecimalAsIntField CASH_PRIORITY_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final StringField XC_SOURCE_FLAG = factory.getStringField(2);
    private static final StringField XC_ELIGIBLE_FLAG = factory.getStringField(2);
    private static final StringField XC_CRED_STAT_CODE = factory.getStringField(3);
    private static final StringField SL_WEEKDAY_FLAG = factory.getStringField(2);
    private static final StringField PRT_CRED_MEMO_IND = factory.getStringField(2);
    private static final StringField CM_APPLY_IND = factory.getStringField(2);
    private static final StringField CM_FUND_FREQ_CODE = factory.getStringField(2);
    private static final StringField CM_FUND_DAY_CODE = factory.getStringField(3);
    private static final StringField PRT_CURRENCY_FLAG = factory.getStringField(2);
    private static final StringField ATTRN_OVRD_FLAG = factory.getStringField(2);
    private static final StringField ATTRN_EXP_DATE = factory.getStringField(8);
    private static final DateTimeFormatter ATTRN_EXP_DATE_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final StringField SHORT_NAME = factory.getStringField(16);
    private static final StringField TRST_SPEC_PRT_CODE = factory.getStringField(2);
    private static final StringField EMAIL_ADDR_NAME = factory.getStringField(131);
    private static final StringField PDE_SORT_SEQ_CODE = factory.getStringField(3);
    private static final StringField MSTR_INV_PRT_CODE = factory.getStringField(2);
    private static final StringField MSTR_INV_REQ_IND = factory.getStringField(2);
    private static final StringField TRUST_EDI_CODE = factory.getStringField(2);
    private static final StringField VAT_RGSTR_NO = factory.getStringField(15);
    private static final StringField VAT_ELECTION_IND = factory.getStringField(2);
    private static final StringField LANGUAGE_CODE = factory.getStringField(7);
    private static final StringField HOLD_NEG_PBL_IND = factory.getStringField(2);
    private static final StringField FAX_STUBS_FLAG = factory.getStringField(2);
    private static final StringField POL_CRED_MEMO_FLAG = factory.getStringField(2);
    private static final ExternalDecimalAsIntField EPD_GRACE_DAYS_CNT = factory.getExternalDecimalAsIntField(3, true);
    private static final StringField EPD_FUND_FREQ_CODE = factory.getStringField(2);
    private static final ExternalDecimalAsIntField EPD_FUND_DAYS_CNT = factory.getExternalDecimalAsIntField(3, true);
    private static final StringField CRED_CLAIM_FLAG = factory.getStringField(2);
    private static final ExternalDecimalAsIntField CRED_CLAIM_DAY_CNT = factory.getExternalDecimalAsIntField(3, true);
    private static final ExternalDecimalAsIntField RMB_CM_DISC_PGM_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final StringField SKU_ID_FLAG = factory.getStringField(2);
    private static final StringField PRT_CRED_NOTE_IND = factory.getStringField(2);
    private static final StringField UCC_ORG_NO = factory.getStringField(21);
    private static final StringField UCC_ORG_ST = factory.getStringField(3);
    private static final StringField SYSTEM_CREATE_FLAG = factory.getStringField(2);
    private static final StringField CURRENCY_CODE = factory.getStringField(4);
    private static final StringField AUTO_CASH_FLAG = factory.getStringField(2);
    private static final StringField INCOMPL_CASH_FLAG = factory.getStringField(2);
    private static final ExternalDecimalAsIntField POD_PRES_DAY_CNT = factory.getExternalDecimalAsIntField(3, true);
    private static final StringField POD_PRES_DATE_FLAG = factory.getStringField(2);
    private static final StringField TRUE_UP_EARLY_FLAG = factory.getStringField(2);
    private static final StringField TRUE_UP_LATE_FLAG = factory.getStringField(2);
    private static final ExternalDecimalAsIntField TDF_RISK_PCT = factory.getExternalDecimalAsIntField(4, true);
    private static final ExternalDecimalAsIntField RISK_FEE_RATE = factory.getExternalDecimalAsIntField(5, true);
    private static final StringField RISK_FEE_FIU_FLAG = factory.getStringField(2);
    private static final StringField PORT_TYPE_CODE = factory.getStringField(4);
    private static final StringField SERV_TRUST_FLAG = factory.getStringField(2);
    private static final ExternalDecimalAsIntField LOSS_RESERVE_2_RATE = factory.getExternalDecimalAsIntField(5, true);
    private static final ExternalDecimalAsIntField GROSSUP_PCT = factory.getExternalDecimalAsIntField(4, true);
    private static final StringField ADB_DISC_IND = factory.getStringField(2);
    private static final ExternalDecimalAsIntField DIST_FUND_OS_AMT = factory.getExternalDecimalAsIntField(8, true);
    private static final StringField DLR_CHRG_TDF_FLAG = factory.getStringField(2);
    private static final StringField MISC_ID = factory.getStringField(21);
    private static final StringField SERV_FEE_FREQ_IND = factory.getStringField(2);
    private static final ExternalDecimalAsIntField COLL_FEE_RATE = factory.getExternalDecimalAsIntField(5, true);
    private static final ExternalDecimalAsIntField FUND_HOLD_PCT = factory.getExternalDecimalAsIntField(4, true);
    private static final ExternalDecimalAsIntField FUND_LIMIT_AMT = factory.getExternalDecimalAsIntField(9, true);
    private static final ExternalDecimalAsIntField SERV_FEE_BRANCH_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final StringField TRUE_UP_INT_FLAG = factory.getStringField(2);
    private static final ExternalDecimalAsIntField CURT_GRACE_DAY_CNT = factory.getExternalDecimalAsIntField(3, true);
    private static final ExternalDecimalAsIntField ANR_MONTHS_CNT = factory.getExternalDecimalAsIntField(3, true);
    private static final StringField MSTR_AGREE_DATE = factory.getStringField(8);
    private static final DateTimeFormatter MSTR_AGREE_DATE_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final StringField CM_APPLY_RULE_CODE = factory.getStringField(3);
    private static final StringField CM_APPLY_EXCP_CODE = factory.getStringField(2);
    private static final StringField SCRTZ_ELIG_CD = factory.getStringField(2);
    private static final StringField SECURED_FL = factory.getStringField(2);
    private static final StringField SCRTZ_EFF_DATE = factory.getStringField(8);
    private static final DateTimeFormatter SCRTZ_EFF_DATE_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final ExternalDecimalAsIntField SCRTZ_PARTIC_RT = factory.getExternalDecimalAsIntField(5, true);
    private static final StringField SCRTZ_POOL_ID = factory.getStringField(3);
    private static final StringField ADB_BILL_METH_CODE = factory.getStringField(2);
    private static final StringField LATE_FEE_BILL_IND = factory.getStringField(2);
    private static final StringField EDOCS_IND = factory.getStringField(2);
    private static final StringField EDOCS_START_DATE = factory.getStringField(8);
    private static final DateTimeFormatter EDOCS_START_DATE_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final StringField EDOCS_PRT_END_DATE = factory.getStringField(8);
    private static final DateTimeFormatter EDOCS_PRT_END_DATE_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final ExternalDecimalAsIntField AUX_CARR_NO = factory.getExternalDecimalAsIntField(5, true);
    private static final StringField CURT_PREPAID_FLAG = factory.getStringField(2);
    private static final StringField STMT_SORT_CODE = factory.getStringField(2);
    private static final StringField STMT_PAGE_BRK_FLAG = factory.getStringField(2);
    private static final StringField RECV_CM_PRT_FLAG = factory.getStringField(2);
    private static final StringField SL_STMT_PRT_FL = factory.getStringField(2);
    private static final ExternalDecimalAsIntField EPD_CBGN_DAY_1_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final ExternalDecimalAsIntField EPD_CBGN_DAY_2_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final ExternalDecimalAsIntField EPD_CBGN_DAY_3_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final ExternalDecimalAsIntField EPD_CBGN_DAY_4_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final ExternalDecimalAsIntField EPD_CDUE_DAY_1_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final ExternalDecimalAsIntField EPD_CDUE_DAY_2_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final ExternalDecimalAsIntField EPD_CDUE_DAY_3_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final ExternalDecimalAsIntField EPD_CDUE_DAY_4_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final ExternalDecimalAsIntField EPD_CEND_DAY_1_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final ExternalDecimalAsIntField EPD_CEND_DAY_2_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final ExternalDecimalAsIntField EPD_CEND_DAY_3_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final ExternalDecimalAsIntField EPD_CEND_DAY_4_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final StringField REFUND_DIST_FD_FL = factory.getStringField(2);
    private static final ExternalDecimalAsIntField CASH_ALG_CODE = factory.getExternalDecimalAsIntField(3, true);
    private static final StringField AUTO_STLMNT_FL = factory.getStringField(2);
    private static final StringField TRAN_STMT_FRMT_CD = factory.getStringField(2);
    private static final StringField DEF_NO_PAY_FLAG = factory.getStringField(2);
    private static final StringField DEF_COMPOS_RT_FLAG = factory.getStringField(2);
    private static final ExternalDecimalAsIntField DEF_BANK_CODE = factory.getExternalDecimalAsIntField(3, true);
    private static final StringField DEF_TYPE_IND = factory.getStringField(2);
    private static final ExternalDecimalAsIntField DEF_MINPRIME_RATE = factory.getExternalDecimalAsIntField(5, true);
    private static final ExternalDecimalAsIntField DEF_MAXPRIME_RATE = factory.getExternalDecimalAsIntField(5, true);
    private static final ExternalDecimalAsIntField STRAIGHT_RATE = factory.getExternalDecimalAsIntField(5, true);
    private static final StringField PRT_NOTE_STMT_IND = factory.getStringField(2);
    private static final StringField SEGMENT_CD = factory.getStringField(13);
    private static final StringField HIGH_RISK_FL = factory.getStringField(2);
    private static final StringField KMV_ID = factory.getStringField(10);
    private static final StringField EXT_ID_TYPE_CD = factory.getStringField(4);
    private static final StringField EXT_ID_NO = factory.getStringField(13);
    private static final StringField OFST_TYPE_CD = factory.getStringField(2);
    private static final StringField LINE_DETAIL_FL = factory.getStringField(2);
    private static final StringField FIXP_SKIP_MO_01_FL = factory.getStringField(2);
    private static final StringField FIXP_SKIP_MO_02_FL = factory.getStringField(2);
    private static final StringField FIXP_SKIP_MO_03_FL = factory.getStringField(2);
    private static final StringField FIXP_SKIP_MO_04_FL = factory.getStringField(2);
    private static final StringField FIXP_SKIP_MO_05_FL = factory.getStringField(2);
    private static final StringField FIXP_SKIP_MO_06_FL = factory.getStringField(2);
    private static final StringField FIXP_SKIP_MO_07_FL = factory.getStringField(2);
    private static final StringField FIXP_SKIP_MO_08_FL = factory.getStringField(2);
    private static final StringField FIXP_SKIP_MO_09_FL = factory.getStringField(2);
    private static final StringField FIXP_SKIP_MO_10_FL = factory.getStringField(2);
    private static final StringField FIXP_SKIP_MO_11_FL = factory.getStringField(2);
    private static final StringField FIXP_SKIP_MO_12_FL = factory.getStringField(2);
    private static final StringField DIST_PRCH_EMAIL_NM = factory.getStringField(65);
    private static final StringField RSTR_FL = factory.getStringField(2);
    private static final StringField STAR_FL = factory.getStringField(2);
    private static final StringField BNKRP_FL = factory.getStringField(2);
    private static final StringField REG_SWEEP_FL = factory.getStringField(2);
    private static final StringField PUT_REASON_CD = factory.getStringField(5);
    private static final StringField PUT_REASON_DT = factory.getStringField(8);
    private static final DateTimeFormatter PUT_REASON_DT_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final StringField BANK_WATCH_FL = factory.getStringField(2);
    private static final ExternalDecimalAsIntField OVLN_TOLER_RT = factory.getExternalDecimalAsIntField(5, true);
    private static final StringField OTB_HELD_PAY_FL = factory.getStringField(2);
    private static final StringField OTB_UNAPPL_CASH_FL = factory.getStringField(2);
    private static final StringField OTB_UNID_CASH_FL = factory.getStringField(2);
    private static final StringField OTB_DEF_FL = factory.getStringField(2);
    private static final ExternalDecimalAsIntField PCS_DEF_APPR_AMT = factory.getExternalDecimalAsIntField(8, true);
    private static final StringField OTB_EXPO_EXCL_IND = factory.getStringField(2);
    private static final StringField FINAN_STMT_INRM_DT = factory.getStringField(8);
    private static final DateTimeFormatter FINAN_STMT_INRM_DT_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final ExternalDecimalAsIntField FINAN_STMT_INRM_CT = factory.getExternalDecimalAsIntField(3, true);
    private static final StringField FINAN_STMT_EXTN_DT = factory.getStringField(8);
    private static final DateTimeFormatter FINAN_STMT_EXTN_DT_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final StringField PUBLIC_PRIV_IND = factory.getStringField(4);
    private static final StringField LAST_REV_EXTN_DT = factory.getStringField(8);
    private static final DateTimeFormatter LAST_REV_EXTN_DT_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final StringField FINAN_STMT_QUAL_CD = factory.getStringField(2);
    private static final StringField END_USER_NM = factory.getStringField(133);
    private static final StringField MRA_CUST_ID = factory.getStringField(11);
    private static final StringField DEF_OFST_PRINC_FL = factory.getStringField(2);
    private static final StringField DEF_ACTIV_PAGE_FL = factory.getStringField(2);
    private static final StringField UNDWR_GDLN_EXCP_FL = factory.getStringField(2);
    private static final StringField DLR_PREAUTH_FL = factory.getStringField(2);
    private static final StringField PREAUTH_ALLOW_FL = factory.getStringField(2);
    private static final StringField EDI_STMT_TYPE_CD = factory.getStringField(3);
    private static final StringField EDI_MFG_DIST_FLAG = factory.getStringField(2);
    private static final StringField EPD_CM_FL = factory.getStringField(2);
    private static final StringField MMTS_CMPRS_FRMT_FL = factory.getStringField(2);
    private static final StringField PRP_CRT_WAV_RPR_FL = factory.getStringField(2);
    private static final ExternalDecimalAsIntField COMS_BIG_BUFFER_CT = factory.getExternalDecimalAsIntField(3, true);
    private static final StringField DLV_VRFCTN_FL = factory.getStringField(2);
    private static final StringField RCP_CRT_TAX_INV_FL = factory.getStringField(2);
    private static final StringField OVERSEAS_DIST_FL = factory.getStringField(2);
    private static final StringField MOBILE_NO = factory.getStringField(16);
    private static final StringField SERV_LVL_CAT_CD = factory.getStringField(9);
    private static final StringField PARTY_ID = factory.getStringField(11);
    private static final ExternalDecimalAsIntField TOUCHLESS_SYNC_ID = factory.getExternalDecimalAsIntField(5, true);
    private static final StringField PRIOR_CHRG_OFF_DT = factory.getStringField(8);
    private static final DateTimeFormatter PRIOR_CHRG_OFF_DT_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final ExternalDecimalAsIntField TOT_CHRG_OFF_AMT = factory.getExternalDecimalAsIntField(9, true);
    private static final StringField DD_OVRD_SWEEP_FL = factory.getStringField(2);
    private static final StringField PREV_SCRTZ_POOL_ID = factory.getStringField(3);
    private static final StringField LAST_SPEC_REV_DATE = factory.getStringField(8);
    private static final DateTimeFormatter LAST_SPEC_REV_DATE_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final StringField LOAN_COMMIT_FL = factory.getStringField(2);
    private static final StringField LOAN_COMMIT_REN_DT = factory.getStringField(8);
    private static final DateTimeFormatter LOAN_COMMIT_REN_DT_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final StringField CK_DELIVERY_CD = factory.getStringField(2);
    private static final ExternalDecimalAsIntField WCIS_ID = factory.getExternalDecimalAsIntField(7, true);
    private static final StringField NAICS_CD = factory.getStringField(7);
    private static final StringField CUST_NOTE_TX = factory.getStringField(73);
    private static final StringField NACE_CD = factory.getStringField(10);
    private static final StringField COLLAT_SEGMENT_DS = factory.getStringField(103);
    private static final StringField AUDIT_TS = factory.getStringField(14);
    private static final DateTimeFormatter AUDIT_TS_FMT = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    private static final StringField AUDIT_LOGON_ID = factory.getStringField(9);
    private static final StringField AUDIT_PROCESS_TX = factory.getStringField(63);
    private static final StringField AUDIT_DELETE_FL = factory.getStringField(2);
    private static final ExternalDecimalAsIntField NET_RELIANCE_AMT = factory.getExternalDecimalAsIntField(9, true);
    private static final StringField LAST_AML_REV_DT = factory.getStringField(8);
    private static final DateTimeFormatter LAST_AML_REV_DT_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final StringField ECRR_CD = factory.getStringField(2);
    private static final StringField CRA_CD = factory.getStringField(3);
    private static final StringField CRA_EFF_DT = factory.getStringField(8);
    private static final DateTimeFormatter CRA_EFF_DT_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final StringField CRA_END_DT = factory.getStringField(8);
    private static final DateTimeFormatter CRA_END_DT_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final ExternalDecimalAsIntField HARD_CRED_LN_AMT = factory.getExternalDecimalAsIntField(9, true);
    private static final StringField WORKOUT_FL = factory.getStringField(2);
    private static final StringField WORKOUT_RESOLVE_CD = factory.getStringField(3);
    private static final StringField ORIG_EFF_DT = factory.getStringField(8);
    private static final DateTimeFormatter ORIG_EFF_DT_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final StringField ACQUIRE_DT = factory.getStringField(8);
    private static final DateTimeFormatter ACQUIRE_DT_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final ExternalDecimalAsIntField ROOFTOP_CNT = factory.getExternalDecimalAsIntField(3, true);
    private static final StringField CMRCL_DSCLR_FL = factory.getStringField(2);
    private static final StringField DIST_AGREE_DT = factory.getStringField(8);
    private static final DateTimeFormatter DIST_AGREE_DT_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final StringField WFBNA_DOC_FL = factory.getStringField(2);
    private static final StringField CUST_DESC_TX = factory.getStringField(73);
    private static final StringField CURR_EFF_DT = factory.getStringField(8);
    private static final DateTimeFormatter CURR_EFF_DT_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final StringField RETAIL_STLMNT_FL = factory.getStringField(2);
    private static final StringField GOVERN_LAW_CD = factory.getStringField(4);
    private static final ExternalDecimalAsIntField TOTAL_EMPL_CNT = factory.getExternalDecimalAsIntField(5, true);
    private static final ExternalDecimalAsIntField TOTAL_ASSET_AMT = factory.getExternalDecimalAsIntField(9, true);
    private static final StringField AGREE_RESTATE_DT = factory.getStringField(8);
    private static final DateTimeFormatter AGREE_RESTATE_DT_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final StringField TOTAL_EMPL_DT = factory.getStringField(8);
    private static final DateTimeFormatter TOTAL_EMPL_DT_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final StringField TOTAL_ASSET_DT = factory.getStringField(8);
    private static final DateTimeFormatter TOTAL_ASSET_DT_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final StringField ANNUAL_SALES_DT = factory.getStringField(8);
    private static final DateTimeFormatter ANNUAL_SALES_DT_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final ExternalDecimalAsIntField HARD_CL_PYDOWN_AMT = factory.getExternalDecimalAsIntField(9, true);
    private static final ExternalDecimalAsIntField WCIS_CUP_ID = factory.getExternalDecimalAsIntField(7, true);
    private static final ExternalDecimalAsIntField WCIS_SUP_ID = factory.getExternalDecimalAsIntField(7, true);
    private static final ExternalDecimalAsIntField HARD_TEMP_CL_AMT = factory.getExternalDecimalAsIntField(9, true);
    private static final ExternalDecimalAsIntField HARD_TEMP_PDWN_AMT = factory.getExternalDecimalAsIntField(9, true);
    private static final StringField STOCK_EXCH_MIC_ID = factory.getStringField(5);
    private static final StringField STOCK_TICKER_ID = factory.getStringField(6);
    private static final StringField RISK_COUNTRY_CD = factory.getStringField(4);
    private static final ExternalDecimalAsIntField TOT_RECOV_AMT = factory.getExternalDecimalAsIntField(9, true);
    private static final StringField TAX_ID_TYPE_CD = factory.getStringField(2);
    private static final StringField BNKRP_DT = factory.getStringField(8);
    private static final DateTimeFormatter BNKRP_DT_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final StringField API_H_APPR_UPD_FL = factory.getStringField(2);
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code Vwmcu00} object
     * @param bytes  preallocated byte array to store the object serialization
     * @param offset offset in the byte array where serialization should begin
     * @return the byte array, updated with the newly added data formatted as in the {@code VWMCU00} record
     * @see "VWMCU00 record at VWMCU00.CPY:9"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        CUST_NO.putInt(custNo, bytes, offset);
        CUST_TYPE_CODE.putString(custTypeCode, bytes, offset);
        CUST_STATUS_CODE.putString(custStatusCode, bytes, offset);
        AFFIL_CUST_NO.putInt(affilCustNo, bytes, offset);
        CNTL_ENT_NO.putInt(cntlEntNo, bytes, offset);
        BILL_ACCT_REP_CODE.putString(billAcctRepCode, bytes, offset);
        BUS_CODE.putString(busCode, bytes, offset);
        COLL_BRANCH_NO.putInt(collBranchNo, bytes, offset);
        COLL_DELAY_CNT.putInt(collDelayCnt, bytes, offset);
        RATE_360_FLAG.putString(rate360Flag, bytes, offset);
        ADV_PAY_FLAG.putString(advPayFlag, bytes, offset);
        RECOURSE_FLAG.putString(recourseFlag, bytes, offset);
        SHIP_AUTH_AMT.putInt(shipAuthAmt, bytes, offset);
        BILL_CUST_FLAG.putString(billCustFlag, bytes, offset);
        SL_CYCLE_NO.putString(slCycleNo, bytes, offset);
        SHIP_AUTH_FLAG.putString(shipAuthFlag, bytes, offset);
        FINAN_STMT_DATE.putString(finanStmtDate.toLocalDate().format(FINAN_STMT_DATE_FMT), bytes, offset);
        DUNN_SIC_CODE.putString(dunnSicCode, bytes, offset);
        DUNN_NO.putString(dunnNo, bytes, offset);
        BUS_ESTAB_YEAR_NO.putInt(busEstabYearNo, bytes, offset);
        COMM_BEGIN_DAY_1_NO.putInt(commBeginDay1No, bytes, offset);
        COMM_END_DAY_1_NO.putInt(commEndDay1No, bytes, offset);
        COMM_DUE_DAY_1_NO.putInt(commDueDay1No, bytes, offset);
        COMM_BEGIN_DAY_2_NO.putInt(commBeginDay2No, bytes, offset);
        COMM_END_DAY_2_NO.putInt(commEndDay2No, bytes, offset);
        COMM_DUE_DAY_2_NO.putInt(commDueDay2No, bytes, offset);
        COMM_BEGIN_DAY_3_NO.putInt(commBeginDay3No, bytes, offset);
        COMM_END_DAY_3_NO.putInt(commEndDay3No, bytes, offset);
        COMM_DUE_DAY_3_NO.putInt(commDueDay3No, bytes, offset);
        COMM_BEGIN_DAY_4_NO.putInt(commBeginDay4No, bytes, offset);
        COMM_END_DAY_4_NO.putInt(commEndDay4No, bytes, offset);
        COMM_DUE_DAY_4_NO.putInt(commDueDay4No, bytes, offset);
        REPUR_FLAG.putString(repurFlag, bytes, offset);
        STRIP_PASS_FLAG.putString(stripPassFlag, bytes, offset);
        REV_FREQ_CODE.putInt(revFreqCode, bytes, offset);
        TRUST_PRTFORM_CODE.putString(trustPrtformCode, bytes, offset);
        TRUST_PRT_CODE.putString(trustPrtCode, bytes, offset);
        TAX_ID_UPD_CNT.putInt(taxIdUpdCnt, bytes, offset);
        TAX_ID_NO.putString(taxIdNo, bytes, offset);
        INT_CRED_EXP_DATE.putString(intCredExpDate.toLocalDate().format(INT_CRED_EXP_DATE_FMT), bytes, offset);
        TCFC_START_YEAR_NO.putInt(tcfcStartYearNo, bytes, offset);
        APPR_TYPE_CODE.putString(apprTypeCode, bytes, offset);
        BILL_CONS_FLAG.putString(billConsFlag, bytes, offset);
        BILL_SUB_DIST_FLAG.putString(billSubDistFlag, bytes, offset);
        LAST_REV_DATE.putString(lastRevDate.toLocalDate().format(LAST_REV_DATE_FMT), bytes, offset);
        INS_FLAG.putString(insFlag, bytes, offset);
        COVERAGE_INS_AMT.putInt(coverageInsAmt, bytes, offset);
        POLICY_EXP_DATE.putString(policyExpDate.toLocalDate().format(POLICY_EXP_DATE_FMT), bytes, offset);
        OVRD_INS_RATE.putInt(ovrdInsRate, bytes, offset);
        INS_RATE_CODE.putInt(insRateCode, bytes, offset);
        SOT_UPD_DATE.putString(sotUpdDate.toLocalDate().format(SOT_UPD_DATE_FMT), bytes, offset);
        REPO_UPD_DATE.putString(repoUpdDate.toLocalDate().format(REPO_UPD_DATE_FMT), bytes, offset);
        REPO_AMT.putInt(repoAmt, bytes, offset);
        SOT_BAL_DUE_AMT.putInt(sotBalDueAmt, bytes, offset);
        RESERVE_FUND_2_AMT.putInt(reserveFund2Amt, bytes, offset);
        RESERVE_FUND_1_AMT.putInt(reserveFund1Amt, bytes, offset);
        INCT_PAY_BRANCH_NO.putInt(inctPayBranchNo, bytes, offset);
        INCT_BASED_ON_CODE.putString(inctBasedOnCode, bytes, offset);
        INCT_PAY_FREQ_CODE.putString(inctPayFreqCode, bytes, offset);
        INCT_PAID_DATE.putString(inctPaidDate.toLocalDate().format(INCT_PAID_DATE_FMT), bytes, offset);
        BILL_ADDR_NO.putInt(billAddrNo, bytes, offset);
        PAY_ADDR_NO.putInt(payAddrNo, bytes, offset);
        PHONE_NO.putString(phoneNo, bytes, offset);
        FAX_NO.putString(faxNo, bytes, offset);
        FC_BRANCH_NO.putInt(fcBranchNo, bytes, offset);
        FC_CYCLE_NO.putInt(fcCycleNo, bytes, offset);
        FC_SORT_SEQ_CODE.putString(fcSortSeqCode, bytes, offset);
        FC_TYPE_CODE.putString(fcTypeCode, bytes, offset);
        FC_LAST_DATE.putString(fcLastDate.toLocalDate().format(FC_LAST_DATE_FMT), bytes, offset);
        FC_LAST_INITIALS.putString(fcLastInitials, bytes, offset);
        EST_LOSS_INITIALS.putString(estLossInitials, bytes, offset);
        EST_LOSS_AMT.putInt(estLossAmt, bytes, offset);
        DIST_MFG_OS_AMT.putInt(distMfgOsAmt, bytes, offset);
        UCC_ST_FILING_NO.putString(uccStFilingNo, bytes, offset);
        UCC_ST_EXP_DATE.putString(uccStExpDate.toLocalDate().format(UCC_ST_EXP_DATE_FMT), bytes, offset);
        UCC_CNTY_FILING_NO.putString(uccCntyFilingNo, bytes, offset);
        UCC_CNTY_EXP_DATE.putString(uccCntyExpDate.toLocalDate().format(UCC_CNTY_EXP_DATE_FMT), bytes, offset);
        INIT_CHRG_RPT_FLAG.putString(initChrgRptFlag, bytes, offset);
        COLL_DELAY_IND.putString(collDelayInd, bytes, offset);
        SL_GRACE_DAYS_CNT.putInt(slGraceDaysCnt, bytes, offset);
        CRED_SCORE_NO.putInt(credScoreNo, bytes, offset);
        INDUSTRY_CODE.putString(industryCode, bytes, offset);
        DLR_REP_CODE.putString(dlrRepCode, bytes, offset);
        BOS_AUDIT_DATE.putString(bosAuditDate.toLocalDate().format(BOS_AUDIT_DATE_FMT), bytes, offset);
        BOS_CYCLE_NO.putInt(bosCycleNo, bytes, offset);
        BANK_AUDIT_DATE.putString(bankAuditDate.toLocalDate().format(BANK_AUDIT_DATE_FMT), bytes, offset);
        BANK_CYCLE_NO.putInt(bankCycleNo, bytes, offset);
        ADMIN_FLAT_AMT.putInt(adminFlatAmt, bytes, offset);
        ADMIN_RATE.putInt(adminRate, bytes, offset);
        ADMIN_MIN_OS_AMT.putInt(adminMinOsAmt, bytes, offset);
        LOSS_RESERVE_RATE.putInt(lossReserveRate, bytes, offset);
        LOSS_RESERVE_AMT.putInt(lossReserveAmt, bytes, offset);
        CNTL_BRANCH_NO.putInt(cntlBranchNo, bytes, offset);
        FSR_REP_CODE.putString(fsrRepCode, bytes, offset);
        LAST_FSR_REP_CODE.putString(lastFsrRepCode, bytes, offset);
        TITLE_CODE.putString(titleCode, bytes, offset);
        SHIP_CODE_REQ_FLAG.putString(shipCodeReqFlag, bytes, offset);
        PLB_BRANCH_NO.putInt(plbBranchNo, bytes, offset);
        PLB_PAY_ADDR_NO.putInt(plbPayAddrNo, bytes, offset);
        ANNUAL_SALES_AMT.putInt(annualSalesAmt, bytes, offset);
        COGS_AMT.putInt(cogsAmt, bytes, offset);
        BANK_NAME.putString(bankName, bytes, offset);
        LEAD_SOURCE_CODE.putString(leadSourceCode, bytes, offset);
        LEAD_CUST_NO.putInt(leadCustNo, bytes, offset);
        PROJ_CODE.putString(projCode, bytes, offset);
        SALES_COGS_DATE.putString(salesCogsDate.toLocalDate().format(SALES_COGS_DATE_FMT), bytes, offset);
        CR_RV_REQST_IND.putString(crRvReqstInd, bytes, offset);
        CR_RV_REQST_DATE.putString(crRvReqstDate.toLocalDate().format(CR_RV_REQST_DATE_FMT), bytes, offset);
        CR_RV_DLR_REP_CODE.putString(crRvDlrRepCode, bytes, offset);
        EST_CRED_LN_AMT.putInt(estCredLnAmt, bytes, offset);
        FIN_SOURCE_1_CODE.putString(finSource1Code, bytes, offset);
        FIN_SOURCE_2_CODE.putString(finSource2Code, bytes, offset);
        FIN_SOURCE_3_CODE.putString(finSource3Code, bytes, offset);
        FIN_SOURCE_4_CODE.putString(finSource4Code, bytes, offset);
        COMPUT_SYSTEM_IND.putString(computSystemInd, bytes, offset);
        CNTL_INVN_FLAG.putString(cntlInvnFlag, bytes, offset);
        CNTL_ACCTRECV_FLAG.putString(cntlAcctrecvFlag, bytes, offset);
        CNTL_ACCT_PBL_FLAG.putString(cntlAcctPblFlag, bytes, offset);
        MODEM_FLAG.putString(modemFlag, bytes, offset);
        SCFC_RECVD_DATE.putString(scfcRecvdDate.toLocalDate().format(SCFC_RECVD_DATE_FMT), bytes, offset);
        SEASONAL_CODE.putInt(seasonalCode, bytes, offset);
        FC_JAN_CNT.putInt(fcJanCnt, bytes, offset);
        FC_FEB_CNT.putInt(fcFebCnt, bytes, offset);
        FC_MAR_CNT.putInt(fcMarCnt, bytes, offset);
        FC_APR_CNT.putInt(fcAprCnt, bytes, offset);
        FC_MAY_CNT.putInt(fcMayCnt, bytes, offset);
        FC_JUN_CNT.putInt(fcJunCnt, bytes, offset);
        FC_JUL_CNT.putInt(fcJulCnt, bytes, offset);
        FC_AUG_CNT.putInt(fcAugCnt, bytes, offset);
        FC_SEP_CNT.putInt(fcSepCnt, bytes, offset);
        FC_OCT_CNT.putInt(fcOctCnt, bytes, offset);
        FC_NOV_CNT.putInt(fcNovCnt, bytes, offset);
        FC_DEC_CNT.putInt(fcDecCnt, bytes, offset);
        FC_TYPE_EFF_DATE.putString(fcTypeEffDate.toLocalDate().format(FC_TYPE_EFF_DATE_FMT), bytes, offset);
        FC_TYPE_LOGON_ID.putString(fcTypeLogonId, bytes, offset);
        FC_REQ_CNT.putInt(fcReqCnt, bytes, offset);
        LAST_YEAR_FC_CNT.putInt(lastYearFcCnt, bytes, offset);
        SCFC_PRT_IND.putString(scfcPrtInd, bytes, offset);
        SCFC_INCL_SL_FLAG.putString(scfcInclSlFlag, bytes, offset);
        NEXT_SCHED_FC_DATE.putString(nextSchedFcDate.toLocalDate().format(NEXT_SCHED_FC_DATE_FMT), bytes, offset);
        PRT_ALL_TRUST_FLAG.putString(prtAllTrustFlag, bytes, offset);
        CREATE_DATE.putString(createDate.toLocalDate().format(CREATE_DATE_FMT), bytes, offset);
        FC_FREQ_EFF_DT.putString(fcFreqEffDt.toLocalDate().format(FC_FREQ_EFF_DT_FMT), bytes, offset);
        FC_FREQ_LOGON_ID.putString(fcFreqLogonId, bytes, offset);
        EQUITY_CYC_NO.putInt(equityCycNo, bytes, offset);
        EQUITY_DATE.putString(equityDate.toLocalDate().format(EQUITY_DATE_FMT), bytes, offset);
        INVN_CYC_NO.putInt(invnCycNo, bytes, offset);
        INVN_DATE.putString(invnDate.toLocalDate().format(INVN_DATE_FMT), bytes, offset);
        RECCL_AMT.putInt(recclAmt, bytes, offset);
        SCFC_TRK_NON_FLAG.putString(scfcTrkNonFlag, bytes, offset);
        SCFC_SENT_01_DATE.putString(scfcSent01Date.toLocalDate().format(SCFC_SENT_01_DATE_FMT), bytes, offset);
        SCFC_SENT_02_DATE.putString(scfcSent02Date.toLocalDate().format(SCFC_SENT_02_DATE_FMT), bytes, offset);
        PBL_FUND_DAY_IND.putString(pblFundDayInd, bytes, offset);
        PCS_PURGE_FLAG.putString(pcsPurgeFlag, bytes, offset);
        OVLN_TOLER_FLAG.putString(ovlnTolerFlag, bytes, offset);
        APPR_PURGE_MIN_AMT.putInt(apprPurgeMinAmt, bytes, offset);
        APPR_PURGE_MIN_PCT.putInt(apprPurgeMinPct, bytes, offset);
        APPR_PURGE_DAY_CNT.putInt(apprPurgeDayCnt, bytes, offset);
        CRED_HOLD_DATE.putString(credHoldDate.toLocalDate().format(CRED_HOLD_DATE_FMT), bytes, offset);
        OVRD_FUND_DATE_IND.putString(ovrdFundDateInd, bytes, offset);
        NO_TRUST_DTL_FLAG.putString(noTrustDtlFlag, bytes, offset);
        PURCH_CONTACT_NAME.putString(purchContactName, bytes, offset);
        PURCH_PHONE_NO.putString(purchPhoneNo, bytes, offset);
        PURCH_FAX_NO.putString(purchFaxNo, bytes, offset);
        ADMIN_LOGON_ID.putString(adminLogonId, bytes, offset);
        SERIAL_NO_FLAG.putString(serialNoFlag, bytes, offset);
        PURCH_UPD_DATE.putString(purchUpdDate.toLocalDate().format(PURCH_UPD_DATE_FMT), bytes, offset);
        PURCH_UPD_LOGON_ID.putString(purchUpdLogonId, bytes, offset);
        EXPLODE_IND.putString(explodeInd, bytes, offset);
        FILING_TYPE_CODE.putString(filingTypeCode, bytes, offset);
        EPD_DELAY_DAYS_CNT.putInt(epdDelayDaysCnt, bytes, offset);
        SALES_ZIP_CODE.putString(salesZipCode, bytes, offset);
        FIN_ST_ENTRY_DATE.putString(finStEntryDate.toLocalDate().format(FIN_ST_ENTRY_DATE_FMT), bytes, offset);
        TRST_SPEC_PRT_DATE.putString(trstSpecPrtDate.toLocalDate().format(TRST_SPEC_PRT_DATE_FMT), bytes, offset);
        TRST_SPEC_PRT_IND.putString(trstSpecPrtInd, bytes, offset);
        CRED_PRT_FLAG.putString(credPrtFlag, bytes, offset);
        CRED_PRT_FREQ_CODE.putString(credPrtFreqCode, bytes, offset);
        ST_PROV_CODE.putString(stProvCode, bytes, offset);
        ZIP_POSTAL_CODE.putString(zipPostalCode, bytes, offset);
        COUNTRY_CODE.putString(countryCode, bytes, offset);
        ADDR_1_NAME.putString(addr1Name, bytes, offset);
        ADDR_2_NAME.putString(addr2Name, bytes, offset);
        CITY_NAME.putString(cityName, bytes, offset);
        COUNTY_NAME.putString(countyName, bytes, offset);
        LEGAL_NAME.putString(legalName, bytes, offset);
        DBA_NAME.putString(dbaName, bytes, offset);
        CONTACT_NAME.putString(contactName, bytes, offset);
        CONTACT_TITLE.putString(contactTitle, bytes, offset);
        LOC_DESC.putString(locDesc, bytes, offset);
        CASH_PRIORITY_NO.putInt(cashPriorityNo, bytes, offset);
        XC_SOURCE_FLAG.putString(xcSourceFlag, bytes, offset);
        XC_ELIGIBLE_FLAG.putString(xcEligibleFlag, bytes, offset);
        XC_CRED_STAT_CODE.putString(xcCredStatCode, bytes, offset);
        SL_WEEKDAY_FLAG.putString(slWeekdayFlag, bytes, offset);
        PRT_CRED_MEMO_IND.putString(prtCredMemoInd, bytes, offset);
        CM_APPLY_IND.putString(cmApplyInd, bytes, offset);
        CM_FUND_FREQ_CODE.putString(cmFundFreqCode, bytes, offset);
        CM_FUND_DAY_CODE.putString(cmFundDayCode, bytes, offset);
        PRT_CURRENCY_FLAG.putString(prtCurrencyFlag, bytes, offset);
        ATTRN_OVRD_FLAG.putString(attrnOvrdFlag, bytes, offset);
        ATTRN_EXP_DATE.putString(attrnExpDate.toLocalDate().format(ATTRN_EXP_DATE_FMT), bytes, offset);
        SHORT_NAME.putString(shortName, bytes, offset);
        TRST_SPEC_PRT_CODE.putString(trstSpecPrtCode, bytes, offset);
        EMAIL_ADDR_NAME.putString(emailAddrName, bytes, offset);
        PDE_SORT_SEQ_CODE.putString(pdeSortSeqCode, bytes, offset);
        MSTR_INV_PRT_CODE.putString(mstrInvPrtCode, bytes, offset);
        MSTR_INV_REQ_IND.putString(mstrInvReqInd, bytes, offset);
        TRUST_EDI_CODE.putString(trustEdiCode, bytes, offset);
        VAT_RGSTR_NO.putString(vatRgstrNo, bytes, offset);
        VAT_ELECTION_IND.putString(vatElectionInd, bytes, offset);
        LANGUAGE_CODE.putString(languageCode, bytes, offset);
        HOLD_NEG_PBL_IND.putString(holdNegPblInd, bytes, offset);
        FAX_STUBS_FLAG.putString(faxStubsFlag, bytes, offset);
        POL_CRED_MEMO_FLAG.putString(polCredMemoFlag, bytes, offset);
        EPD_GRACE_DAYS_CNT.putInt(epdGraceDaysCnt, bytes, offset);
        EPD_FUND_FREQ_CODE.putString(epdFundFreqCode, bytes, offset);
        EPD_FUND_DAYS_CNT.putInt(epdFundDaysCnt, bytes, offset);
        CRED_CLAIM_FLAG.putString(credClaimFlag, bytes, offset);
        CRED_CLAIM_DAY_CNT.putInt(credClaimDayCnt, bytes, offset);
        RMB_CM_DISC_PGM_NO.putInt(rmbCmDiscPgmNo, bytes, offset);
        SKU_ID_FLAG.putString(skuIdFlag, bytes, offset);
        PRT_CRED_NOTE_IND.putString(prtCredNoteInd, bytes, offset);
        UCC_ORG_NO.putString(uccOrgNo, bytes, offset);
        UCC_ORG_ST.putString(uccOrgSt, bytes, offset);
        SYSTEM_CREATE_FLAG.putString(systemCreateFlag, bytes, offset);
        CURRENCY_CODE.putString(currencyCode, bytes, offset);
        AUTO_CASH_FLAG.putString(autoCashFlag, bytes, offset);
        INCOMPL_CASH_FLAG.putString(incomplCashFlag, bytes, offset);
        POD_PRES_DAY_CNT.putInt(podPresDayCnt, bytes, offset);
        POD_PRES_DATE_FLAG.putString(podPresDateFlag, bytes, offset);
        TRUE_UP_EARLY_FLAG.putString(trueUpEarlyFlag, bytes, offset);
        TRUE_UP_LATE_FLAG.putString(trueUpLateFlag, bytes, offset);
        TDF_RISK_PCT.putInt(tdfRiskPct, bytes, offset);
        RISK_FEE_RATE.putInt(riskFeeRate, bytes, offset);
        RISK_FEE_FIU_FLAG.putString(riskFeeFiuFlag, bytes, offset);
        PORT_TYPE_CODE.putString(portTypeCode, bytes, offset);
        SERV_TRUST_FLAG.putString(servTrustFlag, bytes, offset);
        LOSS_RESERVE_2_RATE.putInt(lossReserve2Rate, bytes, offset);
        GROSSUP_PCT.putInt(grossupPct, bytes, offset);
        ADB_DISC_IND.putString(adbDiscInd, bytes, offset);
        DIST_FUND_OS_AMT.putInt(distFundOsAmt, bytes, offset);
        DLR_CHRG_TDF_FLAG.putString(dlrChrgTdfFlag, bytes, offset);
        MISC_ID.putString(miscId, bytes, offset);
        SERV_FEE_FREQ_IND.putString(servFeeFreqInd, bytes, offset);
        COLL_FEE_RATE.putInt(collFeeRate, bytes, offset);
        FUND_HOLD_PCT.putInt(fundHoldPct, bytes, offset);
        FUND_LIMIT_AMT.putInt(fundLimitAmt, bytes, offset);
        SERV_FEE_BRANCH_NO.putInt(servFeeBranchNo, bytes, offset);
        TRUE_UP_INT_FLAG.putString(trueUpIntFlag, bytes, offset);
        CURT_GRACE_DAY_CNT.putInt(curtGraceDayCnt, bytes, offset);
        ANR_MONTHS_CNT.putInt(anrMonthsCnt, bytes, offset);
        MSTR_AGREE_DATE.putString(mstrAgreeDate.toLocalDate().format(MSTR_AGREE_DATE_FMT), bytes, offset);
        CM_APPLY_RULE_CODE.putString(cmApplyRuleCode, bytes, offset);
        CM_APPLY_EXCP_CODE.putString(cmApplyExcpCode, bytes, offset);
        SCRTZ_ELIG_CD.putString(scrtzEligCd, bytes, offset);
        SECURED_FL.putString(securedFl, bytes, offset);
        SCRTZ_EFF_DATE.putString(scrtzEffDate.toLocalDate().format(SCRTZ_EFF_DATE_FMT), bytes, offset);
        SCRTZ_PARTIC_RT.putInt(scrtzParticRt, bytes, offset);
        SCRTZ_POOL_ID.putString(scrtzPoolId, bytes, offset);
        ADB_BILL_METH_CODE.putString(adbBillMethCode, bytes, offset);
        LATE_FEE_BILL_IND.putString(lateFeeBillInd, bytes, offset);
        EDOCS_IND.putString(edocsInd, bytes, offset);
        EDOCS_START_DATE.putString(edocsStartDate.toLocalDate().format(EDOCS_START_DATE_FMT), bytes, offset);
        EDOCS_PRT_END_DATE.putString(edocsPrtEndDate.toLocalDate().format(EDOCS_PRT_END_DATE_FMT), bytes, offset);
        AUX_CARR_NO.putInt(auxCarrNo, bytes, offset);
        CURT_PREPAID_FLAG.putString(curtPrepaidFlag, bytes, offset);
        STMT_SORT_CODE.putString(stmtSortCode, bytes, offset);
        STMT_PAGE_BRK_FLAG.putString(stmtPageBrkFlag, bytes, offset);
        RECV_CM_PRT_FLAG.putString(recvCmPrtFlag, bytes, offset);
        SL_STMT_PRT_FL.putString(slStmtPrtFl, bytes, offset);
        EPD_CBGN_DAY_1_NO.putInt(epdCbgnDay1No, bytes, offset);
        EPD_CBGN_DAY_2_NO.putInt(epdCbgnDay2No, bytes, offset);
        EPD_CBGN_DAY_3_NO.putInt(epdCbgnDay3No, bytes, offset);
        EPD_CBGN_DAY_4_NO.putInt(epdCbgnDay4No, bytes, offset);
        EPD_CDUE_DAY_1_NO.putInt(epdCdueDay1No, bytes, offset);
        EPD_CDUE_DAY_2_NO.putInt(epdCdueDay2No, bytes, offset);
        EPD_CDUE_DAY_3_NO.putInt(epdCdueDay3No, bytes, offset);
        EPD_CDUE_DAY_4_NO.putInt(epdCdueDay4No, bytes, offset);
        EPD_CEND_DAY_1_NO.putInt(epdCendDay1No, bytes, offset);
        EPD_CEND_DAY_2_NO.putInt(epdCendDay2No, bytes, offset);
        EPD_CEND_DAY_3_NO.putInt(epdCendDay3No, bytes, offset);
        EPD_CEND_DAY_4_NO.putInt(epdCendDay4No, bytes, offset);
        REFUND_DIST_FD_FL.putString(refundDistFdFl, bytes, offset);
        CASH_ALG_CODE.putInt(cashAlgCode, bytes, offset);
        AUTO_STLMNT_FL.putString(autoStlmntFl, bytes, offset);
        TRAN_STMT_FRMT_CD.putString(tranStmtFrmtCd, bytes, offset);
        DEF_NO_PAY_FLAG.putString(defNoPayFlag, bytes, offset);
        DEF_COMPOS_RT_FLAG.putString(defComposRtFlag, bytes, offset);
        DEF_BANK_CODE.putInt(defBankCode, bytes, offset);
        DEF_TYPE_IND.putString(defTypeInd, bytes, offset);
        DEF_MINPRIME_RATE.putInt(defMinprimeRate, bytes, offset);
        DEF_MAXPRIME_RATE.putInt(defMaxprimeRate, bytes, offset);
        STRAIGHT_RATE.putInt(straightRate, bytes, offset);
        PRT_NOTE_STMT_IND.putString(prtNoteStmtInd, bytes, offset);
        SEGMENT_CD.putString(segmentCd, bytes, offset);
        HIGH_RISK_FL.putString(highRiskFl, bytes, offset);
        KMV_ID.putString(kmvId, bytes, offset);
        EXT_ID_TYPE_CD.putString(extIdTypeCd, bytes, offset);
        EXT_ID_NO.putString(extIdNo, bytes, offset);
        OFST_TYPE_CD.putString(ofstTypeCd, bytes, offset);
        LINE_DETAIL_FL.putString(lineDetailFl, bytes, offset);
        FIXP_SKIP_MO_01_FL.putString(fixpSkipMo01Fl, bytes, offset);
        FIXP_SKIP_MO_02_FL.putString(fixpSkipMo02Fl, bytes, offset);
        FIXP_SKIP_MO_03_FL.putString(fixpSkipMo03Fl, bytes, offset);
        FIXP_SKIP_MO_04_FL.putString(fixpSkipMo04Fl, bytes, offset);
        FIXP_SKIP_MO_05_FL.putString(fixpSkipMo05Fl, bytes, offset);
        FIXP_SKIP_MO_06_FL.putString(fixpSkipMo06Fl, bytes, offset);
        FIXP_SKIP_MO_07_FL.putString(fixpSkipMo07Fl, bytes, offset);
        FIXP_SKIP_MO_08_FL.putString(fixpSkipMo08Fl, bytes, offset);
        FIXP_SKIP_MO_09_FL.putString(fixpSkipMo09Fl, bytes, offset);
        FIXP_SKIP_MO_10_FL.putString(fixpSkipMo10Fl, bytes, offset);
        FIXP_SKIP_MO_11_FL.putString(fixpSkipMo11Fl, bytes, offset);
        FIXP_SKIP_MO_12_FL.putString(fixpSkipMo12Fl, bytes, offset);
        DIST_PRCH_EMAIL_NM.putString(distPrchEmailNm, bytes, offset);
        RSTR_FL.putString(rstrFl, bytes, offset);
        STAR_FL.putString(starFl, bytes, offset);
        BNKRP_FL.putString(bnkrpFl, bytes, offset);
        REG_SWEEP_FL.putString(regSweepFl, bytes, offset);
        PUT_REASON_CD.putString(putReasonCd, bytes, offset);
        PUT_REASON_DT.putString(putReasonDt.toLocalDate().format(PUT_REASON_DT_FMT), bytes, offset);
        BANK_WATCH_FL.putString(bankWatchFl, bytes, offset);
        OVLN_TOLER_RT.putInt(ovlnTolerRt, bytes, offset);
        OTB_HELD_PAY_FL.putString(otbHeldPayFl, bytes, offset);
        OTB_UNAPPL_CASH_FL.putString(otbUnapplCashFl, bytes, offset);
        OTB_UNID_CASH_FL.putString(otbUnidCashFl, bytes, offset);
        OTB_DEF_FL.putString(otbDefFl, bytes, offset);
        PCS_DEF_APPR_AMT.putInt(pcsDefApprAmt, bytes, offset);
        OTB_EXPO_EXCL_IND.putString(otbExpoExclInd, bytes, offset);
        FINAN_STMT_INRM_DT.putString(finanStmtInrmDt.toLocalDate().format(FINAN_STMT_INRM_DT_FMT), bytes, offset);
        FINAN_STMT_INRM_CT.putInt(finanStmtInrmCt, bytes, offset);
        FINAN_STMT_EXTN_DT.putString(finanStmtExtnDt.toLocalDate().format(FINAN_STMT_EXTN_DT_FMT), bytes, offset);
        PUBLIC_PRIV_IND.putString(publicPrivInd, bytes, offset);
        LAST_REV_EXTN_DT.putString(lastRevExtnDt.toLocalDate().format(LAST_REV_EXTN_DT_FMT), bytes, offset);
        FINAN_STMT_QUAL_CD.putString(finanStmtQualCd, bytes, offset);
        END_USER_NM.putString(endUserNm, bytes, offset);
        MRA_CUST_ID.putString(mraCustId, bytes, offset);
        DEF_OFST_PRINC_FL.putString(defOfstPrincFl, bytes, offset);
        DEF_ACTIV_PAGE_FL.putString(defActivPageFl, bytes, offset);
        UNDWR_GDLN_EXCP_FL.putString(undwrGdlnExcpFl, bytes, offset);
        DLR_PREAUTH_FL.putString(dlrPreauthFl, bytes, offset);
        PREAUTH_ALLOW_FL.putString(preauthAllowFl, bytes, offset);
        EDI_STMT_TYPE_CD.putString(ediStmtTypeCd, bytes, offset);
        EDI_MFG_DIST_FLAG.putString(ediMfgDistFlag, bytes, offset);
        EPD_CM_FL.putString(epdCmFl, bytes, offset);
        MMTS_CMPRS_FRMT_FL.putString(mmtsCmprsFrmtFl, bytes, offset);
        PRP_CRT_WAV_RPR_FL.putString(prpCrtWavRprFl, bytes, offset);
        COMS_BIG_BUFFER_CT.putInt(comsBigBufferCt, bytes, offset);
        DLV_VRFCTN_FL.putString(dlvVrfctnFl, bytes, offset);
        RCP_CRT_TAX_INV_FL.putString(rcpCrtTaxInvFl, bytes, offset);
        OVERSEAS_DIST_FL.putString(overseasDistFl, bytes, offset);
        MOBILE_NO.putString(mobileNo, bytes, offset);
        SERV_LVL_CAT_CD.putString(servLvlCatCd, bytes, offset);
        PARTY_ID.putString(partyId, bytes, offset);
        TOUCHLESS_SYNC_ID.putInt(touchlessSyncId, bytes, offset);
        PRIOR_CHRG_OFF_DT.putString(priorChrgOffDt.toLocalDate().format(PRIOR_CHRG_OFF_DT_FMT), bytes, offset);
        TOT_CHRG_OFF_AMT.putInt(totChrgOffAmt, bytes, offset);
        DD_OVRD_SWEEP_FL.putString(ddOvrdSweepFl, bytes, offset);
        PREV_SCRTZ_POOL_ID.putString(prevScrtzPoolId, bytes, offset);
        LAST_SPEC_REV_DATE.putString(lastSpecRevDate.toLocalDate().format(LAST_SPEC_REV_DATE_FMT), bytes, offset);
        LOAN_COMMIT_FL.putString(loanCommitFl, bytes, offset);
        LOAN_COMMIT_REN_DT.putString(loanCommitRenDt.toLocalDate().format(LOAN_COMMIT_REN_DT_FMT), bytes, offset);
        CK_DELIVERY_CD.putString(ckDeliveryCd, bytes, offset);
        WCIS_ID.putInt(wcisId, bytes, offset);
        NAICS_CD.putString(naicsCd, bytes, offset);
        CUST_NOTE_TX.putString(custNoteTx, bytes, offset);
        NACE_CD.putString(naceCd, bytes, offset);
        COLLAT_SEGMENT_DS.putString(collatSegmentDs, bytes, offset);
        AUDIT_TS.putString(auditTs.toLocalDate().format(AUDIT_TS_FMT), bytes, offset);
        AUDIT_LOGON_ID.putString(auditLogonId, bytes, offset);
        AUDIT_PROCESS_TX.putString(auditProcessTx, bytes, offset);
        AUDIT_DELETE_FL.putString(auditDeleteFl, bytes, offset);
        NET_RELIANCE_AMT.putInt(netRelianceAmt, bytes, offset);
        LAST_AML_REV_DT.putString(lastAmlRevDt.toLocalDate().format(LAST_AML_REV_DT_FMT), bytes, offset);
        ECRR_CD.putString(ecrrCd, bytes, offset);
        CRA_CD.putString(craCd, bytes, offset);
        CRA_EFF_DT.putString(craEffDt.toLocalDate().format(CRA_EFF_DT_FMT), bytes, offset);
        CRA_END_DT.putString(craEndDt.toLocalDate().format(CRA_END_DT_FMT), bytes, offset);
        HARD_CRED_LN_AMT.putInt(hardCredLnAmt, bytes, offset);
        WORKOUT_FL.putString(workoutFl, bytes, offset);
        WORKOUT_RESOLVE_CD.putString(workoutResolveCd, bytes, offset);
        ORIG_EFF_DT.putString(origEffDt.toLocalDate().format(ORIG_EFF_DT_FMT), bytes, offset);
        ACQUIRE_DT.putString(acquireDt.toLocalDate().format(ACQUIRE_DT_FMT), bytes, offset);
        ROOFTOP_CNT.putInt(rooftopCnt, bytes, offset);
        CMRCL_DSCLR_FL.putString(cmrclDsclrFl, bytes, offset);
        DIST_AGREE_DT.putString(distAgreeDt.toLocalDate().format(DIST_AGREE_DT_FMT), bytes, offset);
        WFBNA_DOC_FL.putString(wfbnaDocFl, bytes, offset);
        CUST_DESC_TX.putString(custDescTx, bytes, offset);
        CURR_EFF_DT.putString(currEffDt.toLocalDate().format(CURR_EFF_DT_FMT), bytes, offset);
        RETAIL_STLMNT_FL.putString(retailStlmntFl, bytes, offset);
        GOVERN_LAW_CD.putString(governLawCd, bytes, offset);
        TOTAL_EMPL_CNT.putInt(totalEmplCnt, bytes, offset);
        TOTAL_ASSET_AMT.putInt(totalAssetAmt, bytes, offset);
        AGREE_RESTATE_DT.putString(agreeRestateDt.toLocalDate().format(AGREE_RESTATE_DT_FMT), bytes, offset);
        TOTAL_EMPL_DT.putString(totalEmplDt.toLocalDate().format(TOTAL_EMPL_DT_FMT), bytes, offset);
        TOTAL_ASSET_DT.putString(totalAssetDt.toLocalDate().format(TOTAL_ASSET_DT_FMT), bytes, offset);
        ANNUAL_SALES_DT.putString(annualSalesDt.toLocalDate().format(ANNUAL_SALES_DT_FMT), bytes, offset);
        HARD_CL_PYDOWN_AMT.putInt(hardClPydownAmt, bytes, offset);
        WCIS_CUP_ID.putInt(wcisCupId, bytes, offset);
        WCIS_SUP_ID.putInt(wcisSupId, bytes, offset);
        HARD_TEMP_CL_AMT.putInt(hardTempClAmt, bytes, offset);
        HARD_TEMP_PDWN_AMT.putInt(hardTempPdwnAmt, bytes, offset);
        STOCK_EXCH_MIC_ID.putString(stockExchMicId, bytes, offset);
        STOCK_TICKER_ID.putString(stockTickerId, bytes, offset);
        RISK_COUNTRY_CD.putString(riskCountryCd, bytes, offset);
        TOT_RECOV_AMT.putInt(totRecovAmt, bytes, offset);
        TAX_ID_TYPE_CD.putString(taxIdTypeCd, bytes, offset);
        BNKRP_DT.putString(bnkrpDt.toLocalDate().format(BNKRP_DT_FMT), bytes, offset);
        API_H_APPR_UPD_FL.putString(apiHApprUpdFl, bytes, offset);
        return bytes;
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code Vwmcu00} object,
     * starting at the beginning of the array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes(byte[] bytes) {
        return getBytes(bytes, 0);
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code Vwmcu00} object,
     * allocating a new array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes() {
        return getBytes(new byte[numBytes()]);
    }
    
    /**
     * Retrieves a COBOL-format string representation of the {@code Vwmcu00} object
     * @return the result of {@link #getBytes()} interpreted using the IBM-1047 EBCDIC encoding
     */
    public final String toByteString() {
        return new String(getBytes(), encoding);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array
     * @param bytes  byte array formatted as in the {@code VWMCU00} record; will be space-padded to length if necessary
     * @param offset offset in the byte array where deserialization should begin
     * @see "VWMCU00 record at VWMCU00.CPY:9"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        custNo = CUST_NO.getInt(bytes, offset);
        custTypeCode = CUST_TYPE_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        custStatusCode = CUST_STATUS_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        affilCustNo = AFFIL_CUST_NO.getInt(bytes, offset);
        cntlEntNo = CNTL_ENT_NO.getInt(bytes, offset);
        billAcctRepCode = BILL_ACCT_REP_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        busCode = BUS_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        collBranchNo = COLL_BRANCH_NO.getInt(bytes, offset);
        collDelayCnt = COLL_DELAY_CNT.getInt(bytes, offset);
        rate360Flag = RATE_360_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        advPayFlag = ADV_PAY_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        recourseFlag = RECOURSE_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        shipAuthAmt = SHIP_AUTH_AMT.getInt(bytes, offset);
        billCustFlag = BILL_CUST_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        slCycleNo = SL_CYCLE_NO.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        shipAuthFlag = SHIP_AUTH_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        finanStmtDate = Date.valueOf(LocalDate.parse(FINAN_STMT_DATE.getString(bytes, offset), FINAN_STMT_DATE_FMT));
        dunnSicCode = DUNN_SIC_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        dunnNo = DUNN_NO.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        busEstabYearNo = BUS_ESTAB_YEAR_NO.getInt(bytes, offset);
        commBeginDay1No = COMM_BEGIN_DAY_1_NO.getInt(bytes, offset);
        commEndDay1No = COMM_END_DAY_1_NO.getInt(bytes, offset);
        commDueDay1No = COMM_DUE_DAY_1_NO.getInt(bytes, offset);
        commBeginDay2No = COMM_BEGIN_DAY_2_NO.getInt(bytes, offset);
        commEndDay2No = COMM_END_DAY_2_NO.getInt(bytes, offset);
        commDueDay2No = COMM_DUE_DAY_2_NO.getInt(bytes, offset);
        commBeginDay3No = COMM_BEGIN_DAY_3_NO.getInt(bytes, offset);
        commEndDay3No = COMM_END_DAY_3_NO.getInt(bytes, offset);
        commDueDay3No = COMM_DUE_DAY_3_NO.getInt(bytes, offset);
        commBeginDay4No = COMM_BEGIN_DAY_4_NO.getInt(bytes, offset);
        commEndDay4No = COMM_END_DAY_4_NO.getInt(bytes, offset);
        commDueDay4No = COMM_DUE_DAY_4_NO.getInt(bytes, offset);
        repurFlag = REPUR_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        stripPassFlag = STRIP_PASS_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        revFreqCode = REV_FREQ_CODE.getInt(bytes, offset);
        trustPrtformCode = TRUST_PRTFORM_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        trustPrtCode = TRUST_PRT_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        taxIdUpdCnt = TAX_ID_UPD_CNT.getInt(bytes, offset);
        taxIdNo = TAX_ID_NO.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        intCredExpDate = Date.valueOf(LocalDate.parse(INT_CRED_EXP_DATE.getString(bytes, offset), INT_CRED_EXP_DATE_FMT));
        tcfcStartYearNo = TCFC_START_YEAR_NO.getInt(bytes, offset);
        apprTypeCode = APPR_TYPE_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        billConsFlag = BILL_CONS_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        billSubDistFlag = BILL_SUB_DIST_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        lastRevDate = Date.valueOf(LocalDate.parse(LAST_REV_DATE.getString(bytes, offset), LAST_REV_DATE_FMT));
        insFlag = INS_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        coverageInsAmt = COVERAGE_INS_AMT.getInt(bytes, offset);
        policyExpDate = Date.valueOf(LocalDate.parse(POLICY_EXP_DATE.getString(bytes, offset), POLICY_EXP_DATE_FMT));
        ovrdInsRate = OVRD_INS_RATE.getInt(bytes, offset);
        insRateCode = INS_RATE_CODE.getInt(bytes, offset);
        sotUpdDate = Date.valueOf(LocalDate.parse(SOT_UPD_DATE.getString(bytes, offset), SOT_UPD_DATE_FMT));
        repoUpdDate = Date.valueOf(LocalDate.parse(REPO_UPD_DATE.getString(bytes, offset), REPO_UPD_DATE_FMT));
        repoAmt = REPO_AMT.getInt(bytes, offset);
        sotBalDueAmt = SOT_BAL_DUE_AMT.getInt(bytes, offset);
        reserveFund2Amt = RESERVE_FUND_2_AMT.getInt(bytes, offset);
        reserveFund1Amt = RESERVE_FUND_1_AMT.getInt(bytes, offset);
        inctPayBranchNo = INCT_PAY_BRANCH_NO.getInt(bytes, offset);
        inctBasedOnCode = INCT_BASED_ON_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        inctPayFreqCode = INCT_PAY_FREQ_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        inctPaidDate = Date.valueOf(LocalDate.parse(INCT_PAID_DATE.getString(bytes, offset), INCT_PAID_DATE_FMT));
        billAddrNo = BILL_ADDR_NO.getInt(bytes, offset);
        payAddrNo = PAY_ADDR_NO.getInt(bytes, offset);
        phoneNo = PHONE_NO.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        faxNo = FAX_NO.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        fcBranchNo = FC_BRANCH_NO.getInt(bytes, offset);
        fcCycleNo = FC_CYCLE_NO.getInt(bytes, offset);
        fcSortSeqCode = FC_SORT_SEQ_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        fcTypeCode = FC_TYPE_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        fcLastDate = Date.valueOf(LocalDate.parse(FC_LAST_DATE.getString(bytes, offset), FC_LAST_DATE_FMT));
        fcLastInitials = FC_LAST_INITIALS.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        estLossInitials = EST_LOSS_INITIALS.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        estLossAmt = EST_LOSS_AMT.getInt(bytes, offset);
        distMfgOsAmt = DIST_MFG_OS_AMT.getInt(bytes, offset);
        uccStFilingNo = UCC_ST_FILING_NO.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        uccStExpDate = Date.valueOf(LocalDate.parse(UCC_ST_EXP_DATE.getString(bytes, offset), UCC_ST_EXP_DATE_FMT));
        uccCntyFilingNo = UCC_CNTY_FILING_NO.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        uccCntyExpDate = Date.valueOf(LocalDate.parse(UCC_CNTY_EXP_DATE.getString(bytes, offset), UCC_CNTY_EXP_DATE_FMT));
        initChrgRptFlag = INIT_CHRG_RPT_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        collDelayInd = COLL_DELAY_IND.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        slGraceDaysCnt = SL_GRACE_DAYS_CNT.getInt(bytes, offset);
        credScoreNo = CRED_SCORE_NO.getInt(bytes, offset);
        industryCode = INDUSTRY_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        dlrRepCode = DLR_REP_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        bosAuditDate = Date.valueOf(LocalDate.parse(BOS_AUDIT_DATE.getString(bytes, offset), BOS_AUDIT_DATE_FMT));
        bosCycleNo = BOS_CYCLE_NO.getInt(bytes, offset);
        bankAuditDate = Date.valueOf(LocalDate.parse(BANK_AUDIT_DATE.getString(bytes, offset), BANK_AUDIT_DATE_FMT));
        bankCycleNo = BANK_CYCLE_NO.getInt(bytes, offset);
        adminFlatAmt = ADMIN_FLAT_AMT.getInt(bytes, offset);
        adminRate = ADMIN_RATE.getInt(bytes, offset);
        adminMinOsAmt = ADMIN_MIN_OS_AMT.getInt(bytes, offset);
        lossReserveRate = LOSS_RESERVE_RATE.getInt(bytes, offset);
        lossReserveAmt = LOSS_RESERVE_AMT.getInt(bytes, offset);
        cntlBranchNo = CNTL_BRANCH_NO.getInt(bytes, offset);
        fsrRepCode = FSR_REP_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        lastFsrRepCode = LAST_FSR_REP_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        titleCode = TITLE_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        shipCodeReqFlag = SHIP_CODE_REQ_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        plbBranchNo = PLB_BRANCH_NO.getInt(bytes, offset);
        plbPayAddrNo = PLB_PAY_ADDR_NO.getInt(bytes, offset);
        annualSalesAmt = ANNUAL_SALES_AMT.getInt(bytes, offset);
        cogsAmt = COGS_AMT.getInt(bytes, offset);
        bankName = BANK_NAME.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        leadSourceCode = LEAD_SOURCE_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        leadCustNo = LEAD_CUST_NO.getInt(bytes, offset);
        projCode = PROJ_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        salesCogsDate = Date.valueOf(LocalDate.parse(SALES_COGS_DATE.getString(bytes, offset), SALES_COGS_DATE_FMT));
        crRvReqstInd = CR_RV_REQST_IND.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        crRvReqstDate = Date.valueOf(LocalDate.parse(CR_RV_REQST_DATE.getString(bytes, offset), CR_RV_REQST_DATE_FMT));
        crRvDlrRepCode = CR_RV_DLR_REP_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        estCredLnAmt = EST_CRED_LN_AMT.getInt(bytes, offset);
        finSource1Code = FIN_SOURCE_1_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        finSource2Code = FIN_SOURCE_2_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        finSource3Code = FIN_SOURCE_3_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        finSource4Code = FIN_SOURCE_4_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        computSystemInd = COMPUT_SYSTEM_IND.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        cntlInvnFlag = CNTL_INVN_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        cntlAcctrecvFlag = CNTL_ACCTRECV_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        cntlAcctPblFlag = CNTL_ACCT_PBL_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        modemFlag = MODEM_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        scfcRecvdDate = Date.valueOf(LocalDate.parse(SCFC_RECVD_DATE.getString(bytes, offset), SCFC_RECVD_DATE_FMT));
        seasonalCode = SEASONAL_CODE.getInt(bytes, offset);
        fcJanCnt = FC_JAN_CNT.getInt(bytes, offset);
        fcFebCnt = FC_FEB_CNT.getInt(bytes, offset);
        fcMarCnt = FC_MAR_CNT.getInt(bytes, offset);
        fcAprCnt = FC_APR_CNT.getInt(bytes, offset);
        fcMayCnt = FC_MAY_CNT.getInt(bytes, offset);
        fcJunCnt = FC_JUN_CNT.getInt(bytes, offset);
        fcJulCnt = FC_JUL_CNT.getInt(bytes, offset);
        fcAugCnt = FC_AUG_CNT.getInt(bytes, offset);
        fcSepCnt = FC_SEP_CNT.getInt(bytes, offset);
        fcOctCnt = FC_OCT_CNT.getInt(bytes, offset);
        fcNovCnt = FC_NOV_CNT.getInt(bytes, offset);
        fcDecCnt = FC_DEC_CNT.getInt(bytes, offset);
        fcTypeEffDate = Date.valueOf(LocalDate.parse(FC_TYPE_EFF_DATE.getString(bytes, offset), FC_TYPE_EFF_DATE_FMT));
        fcTypeLogonId = FC_TYPE_LOGON_ID.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        fcReqCnt = FC_REQ_CNT.getInt(bytes, offset);
        lastYearFcCnt = LAST_YEAR_FC_CNT.getInt(bytes, offset);
        scfcPrtInd = SCFC_PRT_IND.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        scfcInclSlFlag = SCFC_INCL_SL_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        nextSchedFcDate = Date.valueOf(LocalDate.parse(NEXT_SCHED_FC_DATE.getString(bytes, offset), NEXT_SCHED_FC_DATE_FMT));
        prtAllTrustFlag = PRT_ALL_TRUST_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        createDate = Date.valueOf(LocalDate.parse(CREATE_DATE.getString(bytes, offset), CREATE_DATE_FMT));
        fcFreqEffDt = Date.valueOf(LocalDate.parse(FC_FREQ_EFF_DT.getString(bytes, offset), FC_FREQ_EFF_DT_FMT));
        fcFreqLogonId = FC_FREQ_LOGON_ID.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        equityCycNo = EQUITY_CYC_NO.getInt(bytes, offset);
        equityDate = Date.valueOf(LocalDate.parse(EQUITY_DATE.getString(bytes, offset), EQUITY_DATE_FMT));
        invnCycNo = INVN_CYC_NO.getInt(bytes, offset);
        invnDate = Date.valueOf(LocalDate.parse(INVN_DATE.getString(bytes, offset), INVN_DATE_FMT));
        recclAmt = RECCL_AMT.getInt(bytes, offset);
        scfcTrkNonFlag = SCFC_TRK_NON_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        scfcSent01Date = Date.valueOf(LocalDate.parse(SCFC_SENT_01_DATE.getString(bytes, offset), SCFC_SENT_01_DATE_FMT));
        scfcSent02Date = Date.valueOf(LocalDate.parse(SCFC_SENT_02_DATE.getString(bytes, offset), SCFC_SENT_02_DATE_FMT));
        pblFundDayInd = PBL_FUND_DAY_IND.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        pcsPurgeFlag = PCS_PURGE_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        ovlnTolerFlag = OVLN_TOLER_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        apprPurgeMinAmt = APPR_PURGE_MIN_AMT.getInt(bytes, offset);
        apprPurgeMinPct = APPR_PURGE_MIN_PCT.getInt(bytes, offset);
        apprPurgeDayCnt = APPR_PURGE_DAY_CNT.getInt(bytes, offset);
        credHoldDate = Date.valueOf(LocalDate.parse(CRED_HOLD_DATE.getString(bytes, offset), CRED_HOLD_DATE_FMT));
        ovrdFundDateInd = OVRD_FUND_DATE_IND.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        noTrustDtlFlag = NO_TRUST_DTL_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        purchContactName = PURCH_CONTACT_NAME.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        purchPhoneNo = PURCH_PHONE_NO.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        purchFaxNo = PURCH_FAX_NO.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        adminLogonId = ADMIN_LOGON_ID.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        serialNoFlag = SERIAL_NO_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        purchUpdDate = Date.valueOf(LocalDate.parse(PURCH_UPD_DATE.getString(bytes, offset), PURCH_UPD_DATE_FMT));
        purchUpdLogonId = PURCH_UPD_LOGON_ID.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        explodeInd = EXPLODE_IND.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        filingTypeCode = FILING_TYPE_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        epdDelayDaysCnt = EPD_DELAY_DAYS_CNT.getInt(bytes, offset);
        salesZipCode = SALES_ZIP_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        finStEntryDate = Date.valueOf(LocalDate.parse(FIN_ST_ENTRY_DATE.getString(bytes, offset), FIN_ST_ENTRY_DATE_FMT));
        trstSpecPrtDate = Date.valueOf(LocalDate.parse(TRST_SPEC_PRT_DATE.getString(bytes, offset), TRST_SPEC_PRT_DATE_FMT));
        trstSpecPrtInd = TRST_SPEC_PRT_IND.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        credPrtFlag = CRED_PRT_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        credPrtFreqCode = CRED_PRT_FREQ_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        stProvCode = ST_PROV_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        zipPostalCode = ZIP_POSTAL_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        countryCode = COUNTRY_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        addr1Name = ADDR_1_NAME.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        addr2Name = ADDR_2_NAME.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        cityName = CITY_NAME.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        countyName = COUNTY_NAME.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        legalName = LEGAL_NAME.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        dbaName = DBA_NAME.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        contactName = CONTACT_NAME.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        contactTitle = CONTACT_TITLE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        locDesc = LOC_DESC.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        cashPriorityNo = CASH_PRIORITY_NO.getInt(bytes, offset);
        xcSourceFlag = XC_SOURCE_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        xcEligibleFlag = XC_ELIGIBLE_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        xcCredStatCode = XC_CRED_STAT_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        slWeekdayFlag = SL_WEEKDAY_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        prtCredMemoInd = PRT_CRED_MEMO_IND.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        cmApplyInd = CM_APPLY_IND.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        cmFundFreqCode = CM_FUND_FREQ_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        cmFundDayCode = CM_FUND_DAY_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        prtCurrencyFlag = PRT_CURRENCY_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        attrnOvrdFlag = ATTRN_OVRD_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        attrnExpDate = Date.valueOf(LocalDate.parse(ATTRN_EXP_DATE.getString(bytes, offset), ATTRN_EXP_DATE_FMT));
        shortName = SHORT_NAME.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        trstSpecPrtCode = TRST_SPEC_PRT_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        emailAddrName = EMAIL_ADDR_NAME.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        pdeSortSeqCode = PDE_SORT_SEQ_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        mstrInvPrtCode = MSTR_INV_PRT_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        mstrInvReqInd = MSTR_INV_REQ_IND.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        trustEdiCode = TRUST_EDI_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        vatRgstrNo = VAT_RGSTR_NO.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        vatElectionInd = VAT_ELECTION_IND.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        languageCode = LANGUAGE_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        holdNegPblInd = HOLD_NEG_PBL_IND.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        faxStubsFlag = FAX_STUBS_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        polCredMemoFlag = POL_CRED_MEMO_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        epdGraceDaysCnt = EPD_GRACE_DAYS_CNT.getInt(bytes, offset);
        epdFundFreqCode = EPD_FUND_FREQ_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        epdFundDaysCnt = EPD_FUND_DAYS_CNT.getInt(bytes, offset);
        credClaimFlag = CRED_CLAIM_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        credClaimDayCnt = CRED_CLAIM_DAY_CNT.getInt(bytes, offset);
        rmbCmDiscPgmNo = RMB_CM_DISC_PGM_NO.getInt(bytes, offset);
        skuIdFlag = SKU_ID_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        prtCredNoteInd = PRT_CRED_NOTE_IND.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        uccOrgNo = UCC_ORG_NO.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        uccOrgSt = UCC_ORG_ST.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        systemCreateFlag = SYSTEM_CREATE_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        currencyCode = CURRENCY_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        autoCashFlag = AUTO_CASH_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        incomplCashFlag = INCOMPL_CASH_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        podPresDayCnt = POD_PRES_DAY_CNT.getInt(bytes, offset);
        podPresDateFlag = POD_PRES_DATE_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        trueUpEarlyFlag = TRUE_UP_EARLY_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        trueUpLateFlag = TRUE_UP_LATE_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        tdfRiskPct = TDF_RISK_PCT.getInt(bytes, offset);
        riskFeeRate = RISK_FEE_RATE.getInt(bytes, offset);
        riskFeeFiuFlag = RISK_FEE_FIU_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        portTypeCode = PORT_TYPE_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        servTrustFlag = SERV_TRUST_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        lossReserve2Rate = LOSS_RESERVE_2_RATE.getInt(bytes, offset);
        grossupPct = GROSSUP_PCT.getInt(bytes, offset);
        adbDiscInd = ADB_DISC_IND.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        distFundOsAmt = DIST_FUND_OS_AMT.getInt(bytes, offset);
        dlrChrgTdfFlag = DLR_CHRG_TDF_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        miscId = MISC_ID.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        servFeeFreqInd = SERV_FEE_FREQ_IND.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        collFeeRate = COLL_FEE_RATE.getInt(bytes, offset);
        fundHoldPct = FUND_HOLD_PCT.getInt(bytes, offset);
        fundLimitAmt = FUND_LIMIT_AMT.getInt(bytes, offset);
        servFeeBranchNo = SERV_FEE_BRANCH_NO.getInt(bytes, offset);
        trueUpIntFlag = TRUE_UP_INT_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        curtGraceDayCnt = CURT_GRACE_DAY_CNT.getInt(bytes, offset);
        anrMonthsCnt = ANR_MONTHS_CNT.getInt(bytes, offset);
        mstrAgreeDate = Date.valueOf(LocalDate.parse(MSTR_AGREE_DATE.getString(bytes, offset), MSTR_AGREE_DATE_FMT));
        cmApplyRuleCode = CM_APPLY_RULE_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        cmApplyExcpCode = CM_APPLY_EXCP_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        scrtzEligCd = SCRTZ_ELIG_CD.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        securedFl = SECURED_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        scrtzEffDate = Date.valueOf(LocalDate.parse(SCRTZ_EFF_DATE.getString(bytes, offset), SCRTZ_EFF_DATE_FMT));
        scrtzParticRt = SCRTZ_PARTIC_RT.getInt(bytes, offset);
        scrtzPoolId = SCRTZ_POOL_ID.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        adbBillMethCode = ADB_BILL_METH_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        lateFeeBillInd = LATE_FEE_BILL_IND.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        edocsInd = EDOCS_IND.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        edocsStartDate = Date.valueOf(LocalDate.parse(EDOCS_START_DATE.getString(bytes, offset), EDOCS_START_DATE_FMT));
        edocsPrtEndDate = Date.valueOf(LocalDate.parse(EDOCS_PRT_END_DATE.getString(bytes, offset), EDOCS_PRT_END_DATE_FMT));
        auxCarrNo = AUX_CARR_NO.getInt(bytes, offset);
        curtPrepaidFlag = CURT_PREPAID_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        stmtSortCode = STMT_SORT_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        stmtPageBrkFlag = STMT_PAGE_BRK_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        recvCmPrtFlag = RECV_CM_PRT_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        slStmtPrtFl = SL_STMT_PRT_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        epdCbgnDay1No = EPD_CBGN_DAY_1_NO.getInt(bytes, offset);
        epdCbgnDay2No = EPD_CBGN_DAY_2_NO.getInt(bytes, offset);
        epdCbgnDay3No = EPD_CBGN_DAY_3_NO.getInt(bytes, offset);
        epdCbgnDay4No = EPD_CBGN_DAY_4_NO.getInt(bytes, offset);
        epdCdueDay1No = EPD_CDUE_DAY_1_NO.getInt(bytes, offset);
        epdCdueDay2No = EPD_CDUE_DAY_2_NO.getInt(bytes, offset);
        epdCdueDay3No = EPD_CDUE_DAY_3_NO.getInt(bytes, offset);
        epdCdueDay4No = EPD_CDUE_DAY_4_NO.getInt(bytes, offset);
        epdCendDay1No = EPD_CEND_DAY_1_NO.getInt(bytes, offset);
        epdCendDay2No = EPD_CEND_DAY_2_NO.getInt(bytes, offset);
        epdCendDay3No = EPD_CEND_DAY_3_NO.getInt(bytes, offset);
        epdCendDay4No = EPD_CEND_DAY_4_NO.getInt(bytes, offset);
        refundDistFdFl = REFUND_DIST_FD_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        cashAlgCode = CASH_ALG_CODE.getInt(bytes, offset);
        autoStlmntFl = AUTO_STLMNT_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        tranStmtFrmtCd = TRAN_STMT_FRMT_CD.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        defNoPayFlag = DEF_NO_PAY_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        defComposRtFlag = DEF_COMPOS_RT_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        defBankCode = DEF_BANK_CODE.getInt(bytes, offset);
        defTypeInd = DEF_TYPE_IND.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        defMinprimeRate = DEF_MINPRIME_RATE.getInt(bytes, offset);
        defMaxprimeRate = DEF_MAXPRIME_RATE.getInt(bytes, offset);
        straightRate = STRAIGHT_RATE.getInt(bytes, offset);
        prtNoteStmtInd = PRT_NOTE_STMT_IND.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        segmentCd = SEGMENT_CD.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        highRiskFl = HIGH_RISK_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        kmvId = KMV_ID.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        extIdTypeCd = EXT_ID_TYPE_CD.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        extIdNo = EXT_ID_NO.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        ofstTypeCd = OFST_TYPE_CD.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        lineDetailFl = LINE_DETAIL_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        fixpSkipMo01Fl = FIXP_SKIP_MO_01_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        fixpSkipMo02Fl = FIXP_SKIP_MO_02_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        fixpSkipMo03Fl = FIXP_SKIP_MO_03_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        fixpSkipMo04Fl = FIXP_SKIP_MO_04_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        fixpSkipMo05Fl = FIXP_SKIP_MO_05_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        fixpSkipMo06Fl = FIXP_SKIP_MO_06_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        fixpSkipMo07Fl = FIXP_SKIP_MO_07_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        fixpSkipMo08Fl = FIXP_SKIP_MO_08_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        fixpSkipMo09Fl = FIXP_SKIP_MO_09_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        fixpSkipMo10Fl = FIXP_SKIP_MO_10_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        fixpSkipMo11Fl = FIXP_SKIP_MO_11_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        fixpSkipMo12Fl = FIXP_SKIP_MO_12_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        distPrchEmailNm = DIST_PRCH_EMAIL_NM.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        rstrFl = RSTR_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        starFl = STAR_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        bnkrpFl = BNKRP_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        regSweepFl = REG_SWEEP_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        putReasonCd = PUT_REASON_CD.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        putReasonDt = Date.valueOf(LocalDate.parse(PUT_REASON_DT.getString(bytes, offset), PUT_REASON_DT_FMT));
        bankWatchFl = BANK_WATCH_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        ovlnTolerRt = OVLN_TOLER_RT.getInt(bytes, offset);
        otbHeldPayFl = OTB_HELD_PAY_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        otbUnapplCashFl = OTB_UNAPPL_CASH_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        otbUnidCashFl = OTB_UNID_CASH_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        otbDefFl = OTB_DEF_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        pcsDefApprAmt = PCS_DEF_APPR_AMT.getInt(bytes, offset);
        otbExpoExclInd = OTB_EXPO_EXCL_IND.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        finanStmtInrmDt = Date.valueOf(LocalDate.parse(FINAN_STMT_INRM_DT.getString(bytes, offset), FINAN_STMT_INRM_DT_FMT));
        finanStmtInrmCt = FINAN_STMT_INRM_CT.getInt(bytes, offset);
        finanStmtExtnDt = Date.valueOf(LocalDate.parse(FINAN_STMT_EXTN_DT.getString(bytes, offset), FINAN_STMT_EXTN_DT_FMT));
        publicPrivInd = PUBLIC_PRIV_IND.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        lastRevExtnDt = Date.valueOf(LocalDate.parse(LAST_REV_EXTN_DT.getString(bytes, offset), LAST_REV_EXTN_DT_FMT));
        finanStmtQualCd = FINAN_STMT_QUAL_CD.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        endUserNm = END_USER_NM.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        mraCustId = MRA_CUST_ID.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        defOfstPrincFl = DEF_OFST_PRINC_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        defActivPageFl = DEF_ACTIV_PAGE_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        undwrGdlnExcpFl = UNDWR_GDLN_EXCP_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        dlrPreauthFl = DLR_PREAUTH_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        preauthAllowFl = PREAUTH_ALLOW_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        ediStmtTypeCd = EDI_STMT_TYPE_CD.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        ediMfgDistFlag = EDI_MFG_DIST_FLAG.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        epdCmFl = EPD_CM_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        mmtsCmprsFrmtFl = MMTS_CMPRS_FRMT_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        prpCrtWavRprFl = PRP_CRT_WAV_RPR_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        comsBigBufferCt = COMS_BIG_BUFFER_CT.getInt(bytes, offset);
        dlvVrfctnFl = DLV_VRFCTN_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        rcpCrtTaxInvFl = RCP_CRT_TAX_INV_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        overseasDistFl = OVERSEAS_DIST_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        mobileNo = MOBILE_NO.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        servLvlCatCd = SERV_LVL_CAT_CD.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        partyId = PARTY_ID.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        touchlessSyncId = TOUCHLESS_SYNC_ID.getInt(bytes, offset);
        priorChrgOffDt = Date.valueOf(LocalDate.parse(PRIOR_CHRG_OFF_DT.getString(bytes, offset), PRIOR_CHRG_OFF_DT_FMT));
        totChrgOffAmt = TOT_CHRG_OFF_AMT.getInt(bytes, offset);
        ddOvrdSweepFl = DD_OVRD_SWEEP_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        prevScrtzPoolId = PREV_SCRTZ_POOL_ID.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        lastSpecRevDate = Date.valueOf(LocalDate.parse(LAST_SPEC_REV_DATE.getString(bytes, offset), LAST_SPEC_REV_DATE_FMT));
        loanCommitFl = LOAN_COMMIT_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        loanCommitRenDt = Date.valueOf(LocalDate.parse(LOAN_COMMIT_REN_DT.getString(bytes, offset), LOAN_COMMIT_REN_DT_FMT));
        ckDeliveryCd = CK_DELIVERY_CD.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        wcisId = WCIS_ID.getInt(bytes, offset);
        naicsCd = NAICS_CD.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        custNoteTx = CUST_NOTE_TX.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        naceCd = NACE_CD.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        collatSegmentDs = COLLAT_SEGMENT_DS.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        auditTs = Date.valueOf(LocalDate.parse(AUDIT_TS.getString(bytes, offset), AUDIT_TS_FMT));
        auditLogonId = AUDIT_LOGON_ID.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        auditProcessTx = AUDIT_PROCESS_TX.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        auditDeleteFl = AUDIT_DELETE_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        netRelianceAmt = NET_RELIANCE_AMT.getInt(bytes, offset);
        lastAmlRevDt = Date.valueOf(LocalDate.parse(LAST_AML_REV_DT.getString(bytes, offset), LAST_AML_REV_DT_FMT));
        ecrrCd = ECRR_CD.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        craCd = CRA_CD.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        craEffDt = Date.valueOf(LocalDate.parse(CRA_EFF_DT.getString(bytes, offset), CRA_EFF_DT_FMT));
        craEndDt = Date.valueOf(LocalDate.parse(CRA_END_DT.getString(bytes, offset), CRA_END_DT_FMT));
        hardCredLnAmt = HARD_CRED_LN_AMT.getInt(bytes, offset);
        workoutFl = WORKOUT_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        workoutResolveCd = WORKOUT_RESOLVE_CD.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        origEffDt = Date.valueOf(LocalDate.parse(ORIG_EFF_DT.getString(bytes, offset), ORIG_EFF_DT_FMT));
        acquireDt = Date.valueOf(LocalDate.parse(ACQUIRE_DT.getString(bytes, offset), ACQUIRE_DT_FMT));
        rooftopCnt = ROOFTOP_CNT.getInt(bytes, offset);
        cmrclDsclrFl = CMRCL_DSCLR_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        distAgreeDt = Date.valueOf(LocalDate.parse(DIST_AGREE_DT.getString(bytes, offset), DIST_AGREE_DT_FMT));
        wfbnaDocFl = WFBNA_DOC_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        custDescTx = CUST_DESC_TX.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        currEffDt = Date.valueOf(LocalDate.parse(CURR_EFF_DT.getString(bytes, offset), CURR_EFF_DT_FMT));
        retailStlmntFl = RETAIL_STLMNT_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        governLawCd = GOVERN_LAW_CD.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        totalEmplCnt = TOTAL_EMPL_CNT.getInt(bytes, offset);
        totalAssetAmt = TOTAL_ASSET_AMT.getInt(bytes, offset);
        agreeRestateDt = Date.valueOf(LocalDate.parse(AGREE_RESTATE_DT.getString(bytes, offset), AGREE_RESTATE_DT_FMT));
        totalEmplDt = Date.valueOf(LocalDate.parse(TOTAL_EMPL_DT.getString(bytes, offset), TOTAL_EMPL_DT_FMT));
        totalAssetDt = Date.valueOf(LocalDate.parse(TOTAL_ASSET_DT.getString(bytes, offset), TOTAL_ASSET_DT_FMT));
        annualSalesDt = Date.valueOf(LocalDate.parse(ANNUAL_SALES_DT.getString(bytes, offset), ANNUAL_SALES_DT_FMT));
        hardClPydownAmt = HARD_CL_PYDOWN_AMT.getInt(bytes, offset);
        wcisCupId = WCIS_CUP_ID.getInt(bytes, offset);
        wcisSupId = WCIS_SUP_ID.getInt(bytes, offset);
        hardTempClAmt = HARD_TEMP_CL_AMT.getInt(bytes, offset);
        hardTempPdwnAmt = HARD_TEMP_PDWN_AMT.getInt(bytes, offset);
        stockExchMicId = STOCK_EXCH_MIC_ID.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        stockTickerId = STOCK_TICKER_ID.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        riskCountryCd = RISK_COUNTRY_CD.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        totRecovAmt = TOT_RECOV_AMT.getInt(bytes, offset);
        taxIdTypeCd = TAX_ID_TYPE_CD.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        bnkrpDt = Date.valueOf(LocalDate.parse(BNKRP_DT.getString(bytes, offset), BNKRP_DT_FMT));
        apiHApprUpdFl = API_H_APPR_UPD_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
    }
    
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array,
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(byte[] bytes) {
        setBytes(bytes, 0);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format string.
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(String bytes) {
        setBytes(bytes.getBytes(encoding));
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
