package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.BinaryAsIntField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class LegalName implements Cloneable, Comparable<LegalName> {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private int legalNameLen;
    private String legalNameText = "";
    
    /** Initialize fields to non-null default values */
    public LegalName() {}
    
    /** Initialize all fields to provided values */
    public LegalName(int legalNameLen, String legalNameText) {
        this.legalNameLen = legalNameLen;
        this.legalNameText = legalNameText;
    }
    
    @Override
    public LegalName clone() throws CloneNotSupportedException {
        LegalName cloned = (LegalName) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code LegalName} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected LegalName(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code LegalName} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected LegalName(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code LegalName} object
     * @see #setBytes(byte[], int)
     */
    public static LegalName fromBytes(byte[] bytes, int offset) {
        return new LegalName(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code LegalName} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static LegalName fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code LegalName} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static LegalName fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public int getLegalNameLen() {
        return this.legalNameLen;
    }
    
    public void setLegalNameLen(int legalNameLen) {
        this.legalNameLen = legalNameLen;
    }
    
    public String getLegalNameText() {
        return this.legalNameText;
    }
    
    public void setLegalNameText(String legalNameText) {
        this.legalNameText = legalNameText;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        legalNameLen = 0;
        legalNameText = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder();
        s.append("{ legalNameLen=\"");
        s.append(getLegalNameLen());
        s.append("\"");
        s.append(", legalNameText=\"");
        s.append(getLegalNameText());
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(LegalName that) {
        return this.legalNameLen == that.legalNameLen &&
            this.legalNameText.equals(that.legalNameText);
    }
    
    /** Per-field equality comparison; only returns equal if argument is the same class */
    @Override
    public boolean equals(Object that) {
        return (that instanceof LegalName other) && other.canEqual(this) && this.equals(other);
    }
    
    /** Does {@code that} have the same runtime type as {@code this}? */
    public boolean canEqual(Object that) {
        return that instanceof LegalName;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * result + Integer.hashCode(legalNameLen);
        result = 31 * result + Objects.hashCode(legalNameText);
        return result;
    }
    
    @Override
    public int compareTo(LegalName that) {
        int c = 0;
        c = Integer.compare(this.legalNameLen, that.legalNameLen);
        if ( c != 0 ) return c;
        c = this.legalNameText.compareTo(that.legalNameText);
        if ( c == 0 && !(that.canEqual(this) && this.canEqual(that)) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
    }
    
    private static final BinaryAsIntField LEGAL_NAME_LEN = factory.getBinaryAsIntField(4, true);
    private static final StringField LEGAL_NAME_TEXT = factory.getStringField(100);
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code LegalName} object
     * @param bytes  preallocated byte array to store the object serialization
     * @param offset offset in the byte array where serialization should begin
     * @return the byte array, updated with the newly added data formatted as in the {@code LEGAL-NAME} record
     * @see "LEGAL-NAME record at VWMCU00.CPY:616"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        LEGAL_NAME_LEN.putInt(legalNameLen, bytes, offset);
        LEGAL_NAME_TEXT.putString(legalNameText, bytes, offset);
        return bytes;
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code LegalName} object,
     * starting at the beginning of the array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes(byte[] bytes) {
        return getBytes(bytes, 0);
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code LegalName} object,
     * allocating a new array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes() {
        return getBytes(new byte[numBytes()]);
    }
    
    /**
     * Retrieves a COBOL-format string representation of the {@code LegalName} object
     * @return the result of {@link #getBytes()} interpreted using the IBM-1047 EBCDIC encoding
     */
    public final String toByteString() {
        return new String(getBytes(), encoding);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array
     * @param bytes  byte array formatted as in the {@code LEGAL-NAME} record; will be space-padded to length if necessary
     * @param offset offset in the byte array where deserialization should begin
     * @see "LEGAL-NAME record at VWMCU00.CPY:616"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        legalNameLen = LEGAL_NAME_LEN.getInt(bytes, offset);
        legalNameText = LEGAL_NAME_TEXT.getString(bytes, offset).replaceFirst("\\s+\\z", "");
    }
    
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array,
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(byte[] bytes) {
        setBytes(bytes, 0);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format string.
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(String bytes) {
        setBytes(bytes.getBytes(encoding));
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
