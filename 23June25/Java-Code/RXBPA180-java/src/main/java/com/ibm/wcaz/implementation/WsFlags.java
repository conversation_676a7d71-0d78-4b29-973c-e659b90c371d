package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;

public class WsFlags implements Cloneable, Comparable<WsFlags> {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private static final char WS_END_OF_FILE_VALUE = 'Y';
    private static final char PROCESS_IN_AIMS_VALUE = 'Y';
    private static final char NOT_PROCESS_IN_AIMS_VALUE = 'N';
    private static final char DLR_XREF_FOUND_VALUE = 'Y';
    private static final char DLR_XREF_NOT_FOUND_VALUE = 'N';
    
    private char wsEofSw = 'N';
    private char wsAaaAimsFlag = 'N';
    private char wsAaaDlrxrefFlag = 'N';
    
    /** Initialize fields to non-null default values */
    public WsFlags() {}
    
    /** Initialize all fields to provided values */
    public WsFlags(char wsEofSw, char wsAaaAimsFlag, char wsAaaDlrxrefFlag) {
        this.wsEofSw = wsEofSw;
        this.wsAaaAimsFlag = wsAaaAimsFlag;
        this.wsAaaDlrxrefFlag = wsAaaDlrxrefFlag;
    }
    
    @Override
    public WsFlags clone() throws CloneNotSupportedException {
        WsFlags cloned = (WsFlags) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code WsFlags} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected WsFlags(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code WsFlags} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected WsFlags(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code WsFlags} object
     * @see #setBytes(byte[], int)
     */
    public static WsFlags fromBytes(byte[] bytes, int offset) {
        return new WsFlags(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code WsFlags} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static WsFlags fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code WsFlags} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static WsFlags fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public char getWsEofSw() {
        return this.wsEofSw;
    }
    
    public void setWsEofSw(char wsEofSw) {
        this.wsEofSw = wsEofSw;
    }
    
    public boolean isWsEndOfFile() {
        return wsEofSw == WS_END_OF_FILE_VALUE;
    }
    
    public void setWsEndOfFile() {
        wsEofSw = WS_END_OF_FILE_VALUE;
    }
    
    public char getWsAaaAimsFlag() {
        return this.wsAaaAimsFlag;
    }
    
    public void setWsAaaAimsFlag(char wsAaaAimsFlag) {
        this.wsAaaAimsFlag = wsAaaAimsFlag;
    }
    
    public boolean isProcessInAims() {
        return wsAaaAimsFlag == PROCESS_IN_AIMS_VALUE;
    }
    
    public void setProcessInAims() {
        wsAaaAimsFlag = PROCESS_IN_AIMS_VALUE;
    }
    
    public boolean isNotProcessInAims() {
        return wsAaaAimsFlag == NOT_PROCESS_IN_AIMS_VALUE;
    }
    
    public void setNotProcessInAims() {
        wsAaaAimsFlag = NOT_PROCESS_IN_AIMS_VALUE;
    }
    
    public char getWsAaaDlrxrefFlag() {
        return this.wsAaaDlrxrefFlag;
    }
    
    public void setWsAaaDlrxrefFlag(char wsAaaDlrxrefFlag) {
        this.wsAaaDlrxrefFlag = wsAaaDlrxrefFlag;
    }
    
    public boolean isDlrXrefFound() {
        return wsAaaDlrxrefFlag == DLR_XREF_FOUND_VALUE;
    }
    
    public void setDlrXrefFound() {
        wsAaaDlrxrefFlag = DLR_XREF_FOUND_VALUE;
    }
    
    public boolean isDlrXrefNotFound() {
        return wsAaaDlrxrefFlag == DLR_XREF_NOT_FOUND_VALUE;
    }
    
    public void setDlrXrefNotFound() {
        wsAaaDlrxrefFlag = DLR_XREF_NOT_FOUND_VALUE;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        wsEofSw = ' ';
        wsAaaAimsFlag = ' ';
        wsAaaDlrxrefFlag = ' ';
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder();
        s.append("{ wsEofSw=\"");
        s.append(getWsEofSw());
        s.append("\"");
        s.append(", wsAaaAimsFlag=\"");
        s.append(getWsAaaAimsFlag());
        s.append("\"");
        s.append(", wsAaaDlrxrefFlag=\"");
        s.append(getWsAaaDlrxrefFlag());
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(WsFlags that) {
        return this.wsEofSw == that.wsEofSw &&
            this.wsAaaAimsFlag == that.wsAaaAimsFlag &&
            this.wsAaaDlrxrefFlag == that.wsAaaDlrxrefFlag;
    }
    
    /** Per-field equality comparison; only returns equal if argument is the same class */
    @Override
    public boolean equals(Object that) {
        return (that instanceof WsFlags other) && other.canEqual(this) && this.equals(other);
    }
    
    /** Does {@code that} have the same runtime type as {@code this}? */
    public boolean canEqual(Object that) {
        return that instanceof WsFlags;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * result + Character.hashCode(wsEofSw);
        result = 31 * result + Character.hashCode(wsAaaAimsFlag);
        result = 31 * result + Character.hashCode(wsAaaDlrxrefFlag);
        return result;
    }
    
    @Override
    public int compareTo(WsFlags that) {
        int c = 0;
        c = Character.compare(this.wsEofSw, that.wsEofSw);
        if ( c != 0 ) return c;
        c = Character.compare(this.wsAaaAimsFlag, that.wsAaaAimsFlag);
        if ( c != 0 ) return c;
        c = Character.compare(this.wsAaaDlrxrefFlag, that.wsAaaDlrxrefFlag);
        if ( c == 0 && !(that.canEqual(this) && this.canEqual(that)) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
    }
    
    private static final StringField WS_EOF_SW = factory.getStringField(1);
    private static final StringField WS_AAA_AIMS_FLAG = factory.getStringField(1);
    private static final StringField WS_AAA_DLRXREF_FLAG = factory.getStringField(1);
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code WsFlags} object
     * @param bytes  preallocated byte array to store the object serialization
     * @param offset offset in the byte array where serialization should begin
     * @return the byte array, updated with the newly added data formatted as in the {@code WS-FLAGS} record
     * @see "WS-FLAGS record at RXBPA180.cbl:93"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        WS_EOF_SW.putString(Character.toString(wsEofSw), bytes, offset);
        WS_AAA_AIMS_FLAG.putString(Character.toString(wsAaaAimsFlag), bytes, offset);
        WS_AAA_DLRXREF_FLAG.putString(Character.toString(wsAaaDlrxrefFlag), bytes, offset);
        return bytes;
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code WsFlags} object,
     * starting at the beginning of the array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes(byte[] bytes) {
        return getBytes(bytes, 0);
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code WsFlags} object,
     * allocating a new array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes() {
        return getBytes(new byte[numBytes()]);
    }
    
    /**
     * Retrieves a COBOL-format string representation of the {@code WsFlags} object
     * @return the result of {@link #getBytes()} interpreted using the IBM-1047 EBCDIC encoding
     */
    public final String toByteString() {
        return new String(getBytes(), encoding);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array
     * @param bytes  byte array formatted as in the {@code WS-FLAGS} record; will be space-padded to length if necessary
     * @param offset offset in the byte array where deserialization should begin
     * @see "WS-FLAGS record at RXBPA180.cbl:93"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        wsEofSw = WS_EOF_SW.getString(bytes, offset).charAt(0);
        wsAaaAimsFlag = WS_AAA_AIMS_FLAG.getString(bytes, offset).charAt(0);
        wsAaaDlrxrefFlag = WS_AAA_DLRXREF_FLAG.getString(bytes, offset).charAt(0);
    }
    
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array,
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(byte[] bytes) {
        setBytes(bytes, 0);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format string.
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(String bytes) {
        setBytes(bytes.getBytes(encoding));
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
