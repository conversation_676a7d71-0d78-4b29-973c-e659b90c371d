package com.ibm.wcaz.datamodel;

import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.ExternalDecimalAsIntField;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.sql.Date;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Objects;

public class Vwmtrli implements Cloneable, Comparable<Vwmtrli> {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private int dlrNo;
    private String trustNo = "";
    private int trustLineNo;
    private int balDueAmt;
    private int netLineAmt;
    private String prodCode = "";
    private int dlrLocNo;
    private Date lastPayDate = new Date(0);
    private String modelNo = "";
    private int modelNoChgCnt;
    private String serialNo = "";
    private int serialNoChgCnt;
    private String maintText = "";
    private Date lastAppliedDate = new Date(0);
    private int serviceChrgAmt;
    private String itemColor = "";
    private int epdAddlRate;
    private int epdExclAmt;
    private String skuId = "";
    private int brandIdNo;
    private String lineTypeCode = "";
    private int distNo;
    private int curMeBalDueAmt;
    private int prvMeBalDueAmt;
    private String lineDetailFl = "";
    private Date lastPostDate = new Date(0);
    private String dlrStockNo = "";
    private int dlrChrgsPaidAmt;
    private int trustLnUniqueId;
    private Date lastInspectDt = new Date(0);
    private String modelNoYear = "";
    private String itemMakeDs = "";
    private Date auditTs = new Date(0);
    private String auditLogonId = "";
    private String auditProcessTx = "";
    private String auditDeleteFl = "";
    
    /** Initialize fields to non-null default values */
    public Vwmtrli() {}
    
    /** Initialize all fields to provided values */
    public Vwmtrli(int dlrNo, String trustNo, int trustLineNo, int balDueAmt, int netLineAmt, String prodCode, int dlrLocNo, Date lastPayDate, String modelNo, int modelNoChgCnt, String serialNo, int serialNoChgCnt, String maintText, Date lastAppliedDate, int serviceChrgAmt, String itemColor, int epdAddlRate, int epdExclAmt, String skuId, int brandIdNo, String lineTypeCode, int distNo, int curMeBalDueAmt, int prvMeBalDueAmt, String lineDetailFl, Date lastPostDate, String dlrStockNo, int dlrChrgsPaidAmt, int trustLnUniqueId, Date lastInspectDt, String modelNoYear, String itemMakeDs, Date auditTs, String auditLogonId, String auditProcessTx, String auditDeleteFl) {
        this.dlrNo = dlrNo;
        this.trustNo = trustNo;
        this.trustLineNo = trustLineNo;
        this.balDueAmt = balDueAmt;
        this.netLineAmt = netLineAmt;
        this.prodCode = prodCode;
        this.dlrLocNo = dlrLocNo;
        this.lastPayDate = lastPayDate;
        this.modelNo = modelNo;
        this.modelNoChgCnt = modelNoChgCnt;
        this.serialNo = serialNo;
        this.serialNoChgCnt = serialNoChgCnt;
        this.maintText = maintText;
        this.lastAppliedDate = lastAppliedDate;
        this.serviceChrgAmt = serviceChrgAmt;
        this.itemColor = itemColor;
        this.epdAddlRate = epdAddlRate;
        this.epdExclAmt = epdExclAmt;
        this.skuId = skuId;
        this.brandIdNo = brandIdNo;
        this.lineTypeCode = lineTypeCode;
        this.distNo = distNo;
        this.curMeBalDueAmt = curMeBalDueAmt;
        this.prvMeBalDueAmt = prvMeBalDueAmt;
        this.lineDetailFl = lineDetailFl;
        this.lastPostDate = lastPostDate;
        this.dlrStockNo = dlrStockNo;
        this.dlrChrgsPaidAmt = dlrChrgsPaidAmt;
        this.trustLnUniqueId = trustLnUniqueId;
        this.lastInspectDt = lastInspectDt;
        this.modelNoYear = modelNoYear;
        this.itemMakeDs = itemMakeDs;
        this.auditTs = auditTs;
        this.auditLogonId = auditLogonId;
        this.auditProcessTx = auditProcessTx;
        this.auditDeleteFl = auditDeleteFl;
    }
    
    @Override
    public Vwmtrli clone() throws CloneNotSupportedException {
        Vwmtrli cloned = (Vwmtrli) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code Vwmtrli} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected Vwmtrli(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code Vwmtrli} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected Vwmtrli(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code Vwmtrli} object
     * @see #setBytes(byte[], int)
     */
    public static Vwmtrli fromBytes(byte[] bytes, int offset) {
        return new Vwmtrli(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code Vwmtrli} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static Vwmtrli fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code Vwmtrli} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static Vwmtrli fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public int getDlrNo() {
        return this.dlrNo;
    }
    
    public void setDlrNo(int dlrNo) {
        this.dlrNo = dlrNo;
    }
    
    public String getTrustNo() {
        return this.trustNo;
    }
    
    public void setTrustNo(String trustNo) {
        this.trustNo = trustNo;
    }
    
    public int getTrustLineNo() {
        return this.trustLineNo;
    }
    
    public void setTrustLineNo(int trustLineNo) {
        this.trustLineNo = trustLineNo;
    }
    
    public int getBalDueAmt() {
        return this.balDueAmt;
    }
    
    public void setBalDueAmt(int balDueAmt) {
        this.balDueAmt = balDueAmt;
    }
    
    public int getNetLineAmt() {
        return this.netLineAmt;
    }
    
    public void setNetLineAmt(int netLineAmt) {
        this.netLineAmt = netLineAmt;
    }
    
    public String getProdCode() {
        return this.prodCode;
    }
    
    public void setProdCode(String prodCode) {
        this.prodCode = prodCode;
    }
    
    public int getDlrLocNo() {
        return this.dlrLocNo;
    }
    
    public void setDlrLocNo(int dlrLocNo) {
        this.dlrLocNo = dlrLocNo;
    }
    
    public Date getLastPayDate() {
        return this.lastPayDate;
    }
    
    public void setLastPayDate(Date lastPayDate) {
        this.lastPayDate = lastPayDate;
    }
    
    public String getModelNo() {
        return this.modelNo;
    }
    
    public void setModelNo(String modelNo) {
        this.modelNo = modelNo;
    }
    
    public int getModelNoChgCnt() {
        return this.modelNoChgCnt;
    }
    
    public void setModelNoChgCnt(int modelNoChgCnt) {
        this.modelNoChgCnt = modelNoChgCnt;
    }
    
    public String getSerialNo() {
        return this.serialNo;
    }
    
    public void setSerialNo(String serialNo) {
        this.serialNo = serialNo;
    }
    
    public int getSerialNoChgCnt() {
        return this.serialNoChgCnt;
    }
    
    public void setSerialNoChgCnt(int serialNoChgCnt) {
        this.serialNoChgCnt = serialNoChgCnt;
    }
    
    public String getMaintText() {
        return this.maintText;
    }
    
    public void setMaintText(String maintText) {
        this.maintText = maintText;
    }
    
    public Date getLastAppliedDate() {
        return this.lastAppliedDate;
    }
    
    public void setLastAppliedDate(Date lastAppliedDate) {
        this.lastAppliedDate = lastAppliedDate;
    }
    
    public int getServiceChrgAmt() {
        return this.serviceChrgAmt;
    }
    
    public void setServiceChrgAmt(int serviceChrgAmt) {
        this.serviceChrgAmt = serviceChrgAmt;
    }
    
    public String getItemColor() {
        return this.itemColor;
    }
    
    public void setItemColor(String itemColor) {
        this.itemColor = itemColor;
    }
    
    public int getEpdAddlRate() {
        return this.epdAddlRate;
    }
    
    public void setEpdAddlRate(int epdAddlRate) {
        this.epdAddlRate = epdAddlRate;
    }
    
    public int getEpdExclAmt() {
        return this.epdExclAmt;
    }
    
    public void setEpdExclAmt(int epdExclAmt) {
        this.epdExclAmt = epdExclAmt;
    }
    
    public String getSkuId() {
        return this.skuId;
    }
    
    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }
    
    public int getBrandIdNo() {
        return this.brandIdNo;
    }
    
    public void setBrandIdNo(int brandIdNo) {
        this.brandIdNo = brandIdNo;
    }
    
    public String getLineTypeCode() {
        return this.lineTypeCode;
    }
    
    public void setLineTypeCode(String lineTypeCode) {
        this.lineTypeCode = lineTypeCode;
    }
    
    public int getDistNo() {
        return this.distNo;
    }
    
    public void setDistNo(int distNo) {
        this.distNo = distNo;
    }
    
    public int getCurMeBalDueAmt() {
        return this.curMeBalDueAmt;
    }
    
    public void setCurMeBalDueAmt(int curMeBalDueAmt) {
        this.curMeBalDueAmt = curMeBalDueAmt;
    }
    
    public int getPrvMeBalDueAmt() {
        return this.prvMeBalDueAmt;
    }
    
    public void setPrvMeBalDueAmt(int prvMeBalDueAmt) {
        this.prvMeBalDueAmt = prvMeBalDueAmt;
    }
    
    public String getLineDetailFl() {
        return this.lineDetailFl;
    }
    
    public void setLineDetailFl(String lineDetailFl) {
        this.lineDetailFl = lineDetailFl;
    }
    
    public Date getLastPostDate() {
        return this.lastPostDate;
    }
    
    public void setLastPostDate(Date lastPostDate) {
        this.lastPostDate = lastPostDate;
    }
    
    public String getDlrStockNo() {
        return this.dlrStockNo;
    }
    
    public void setDlrStockNo(String dlrStockNo) {
        this.dlrStockNo = dlrStockNo;
    }
    
    public int getDlrChrgsPaidAmt() {
        return this.dlrChrgsPaidAmt;
    }
    
    public void setDlrChrgsPaidAmt(int dlrChrgsPaidAmt) {
        this.dlrChrgsPaidAmt = dlrChrgsPaidAmt;
    }
    
    public int getTrustLnUniqueId() {
        return this.trustLnUniqueId;
    }
    
    public void setTrustLnUniqueId(int trustLnUniqueId) {
        this.trustLnUniqueId = trustLnUniqueId;
    }
    
    public Date getLastInspectDt() {
        return this.lastInspectDt;
    }
    
    public void setLastInspectDt(Date lastInspectDt) {
        this.lastInspectDt = lastInspectDt;
    }
    
    public String getModelNoYear() {
        return this.modelNoYear;
    }
    
    public void setModelNoYear(String modelNoYear) {
        this.modelNoYear = modelNoYear;
    }
    
    public String getItemMakeDs() {
        return this.itemMakeDs;
    }
    
    public void setItemMakeDs(String itemMakeDs) {
        this.itemMakeDs = itemMakeDs;
    }
    
    public Date getAuditTs() {
        return this.auditTs;
    }
    
    public void setAuditTs(Date auditTs) {
        this.auditTs = auditTs;
    }
    
    public String getAuditLogonId() {
        return this.auditLogonId;
    }
    
    public void setAuditLogonId(String auditLogonId) {
        this.auditLogonId = auditLogonId;
    }
    
    public String getAuditProcessTx() {
        return this.auditProcessTx;
    }
    
    public void setAuditProcessTx(String auditProcessTx) {
        this.auditProcessTx = auditProcessTx;
    }
    
    public String getAuditDeleteFl() {
        return this.auditDeleteFl;
    }
    
    public void setAuditDeleteFl(String auditDeleteFl) {
        this.auditDeleteFl = auditDeleteFl;
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder();
        s.append("{ dlrNo=\"");
        s.append(getDlrNo());
        s.append("\"");
        s.append(", trustNo=\"");
        s.append(getTrustNo());
        s.append("\"");
        s.append(", trustLineNo=\"");
        s.append(getTrustLineNo());
        s.append("\"");
        s.append(", balDueAmt=\"");
        s.append(getBalDueAmt());
        s.append("\"");
        s.append(", netLineAmt=\"");
        s.append(getNetLineAmt());
        s.append("\"");
        s.append(", prodCode=\"");
        s.append(getProdCode());
        s.append("\"");
        s.append(", dlrLocNo=\"");
        s.append(getDlrLocNo());
        s.append("\"");
        s.append(", lastPayDate=\"");
        s.append(getLastPayDate());
        s.append("\"");
        s.append(", modelNo=\"");
        s.append(getModelNo());
        s.append("\"");
        s.append(", modelNoChgCnt=\"");
        s.append(getModelNoChgCnt());
        s.append("\"");
        s.append(", serialNo=\"");
        s.append(getSerialNo());
        s.append("\"");
        s.append(", serialNoChgCnt=\"");
        s.append(getSerialNoChgCnt());
        s.append("\"");
        s.append(", maintText=\"");
        s.append(getMaintText());
        s.append("\"");
        s.append(", lastAppliedDate=\"");
        s.append(getLastAppliedDate());
        s.append("\"");
        s.append(", serviceChrgAmt=\"");
        s.append(getServiceChrgAmt());
        s.append("\"");
        s.append(", itemColor=\"");
        s.append(getItemColor());
        s.append("\"");
        s.append(", epdAddlRate=\"");
        s.append(getEpdAddlRate());
        s.append("\"");
        s.append(", epdExclAmt=\"");
        s.append(getEpdExclAmt());
        s.append("\"");
        s.append(", skuId=\"");
        s.append(getSkuId());
        s.append("\"");
        s.append(", brandIdNo=\"");
        s.append(getBrandIdNo());
        s.append("\"");
        s.append(", lineTypeCode=\"");
        s.append(getLineTypeCode());
        s.append("\"");
        s.append(", distNo=\"");
        s.append(getDistNo());
        s.append("\"");
        s.append(", curMeBalDueAmt=\"");
        s.append(getCurMeBalDueAmt());
        s.append("\"");
        s.append(", prvMeBalDueAmt=\"");
        s.append(getPrvMeBalDueAmt());
        s.append("\"");
        s.append(", lineDetailFl=\"");
        s.append(getLineDetailFl());
        s.append("\"");
        s.append(", lastPostDate=\"");
        s.append(getLastPostDate());
        s.append("\"");
        s.append(", dlrStockNo=\"");
        s.append(getDlrStockNo());
        s.append("\"");
        s.append(", dlrChrgsPaidAmt=\"");
        s.append(getDlrChrgsPaidAmt());
        s.append("\"");
        s.append(", trustLnUniqueId=\"");
        s.append(getTrustLnUniqueId());
        s.append("\"");
        s.append(", lastInspectDt=\"");
        s.append(getLastInspectDt());
        s.append("\"");
        s.append(", modelNoYear=\"");
        s.append(getModelNoYear());
        s.append("\"");
        s.append(", itemMakeDs=\"");
        s.append(getItemMakeDs());
        s.append("\"");
        s.append(", auditTs=\"");
        s.append(getAuditTs());
        s.append("\"");
        s.append(", auditLogonId=\"");
        s.append(getAuditLogonId());
        s.append("\"");
        s.append(", auditProcessTx=\"");
        s.append(getAuditProcessTx());
        s.append("\"");
        s.append(", auditDeleteFl=\"");
        s.append(getAuditDeleteFl());
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(Vwmtrli that) {
        return this.dlrNo == that.dlrNo &&
            this.trustNo.equals(that.trustNo) &&
            this.trustLineNo == that.trustLineNo &&
            this.balDueAmt == that.balDueAmt &&
            this.netLineAmt == that.netLineAmt &&
            this.prodCode.equals(that.prodCode) &&
            this.dlrLocNo == that.dlrLocNo &&
            this.lastPayDate.equals(that.lastPayDate) &&
            this.modelNo.equals(that.modelNo) &&
            this.modelNoChgCnt == that.modelNoChgCnt &&
            this.serialNo.equals(that.serialNo) &&
            this.serialNoChgCnt == that.serialNoChgCnt &&
            this.maintText.equals(that.maintText) &&
            this.lastAppliedDate.equals(that.lastAppliedDate) &&
            this.serviceChrgAmt == that.serviceChrgAmt &&
            this.itemColor.equals(that.itemColor) &&
            this.epdAddlRate == that.epdAddlRate &&
            this.epdExclAmt == that.epdExclAmt &&
            this.skuId.equals(that.skuId) &&
            this.brandIdNo == that.brandIdNo &&
            this.lineTypeCode.equals(that.lineTypeCode) &&
            this.distNo == that.distNo &&
            this.curMeBalDueAmt == that.curMeBalDueAmt &&
            this.prvMeBalDueAmt == that.prvMeBalDueAmt &&
            this.lineDetailFl.equals(that.lineDetailFl) &&
            this.lastPostDate.equals(that.lastPostDate) &&
            this.dlrStockNo.equals(that.dlrStockNo) &&
            this.dlrChrgsPaidAmt == that.dlrChrgsPaidAmt &&
            this.trustLnUniqueId == that.trustLnUniqueId &&
            this.lastInspectDt.equals(that.lastInspectDt) &&
            this.modelNoYear.equals(that.modelNoYear) &&
            this.itemMakeDs.equals(that.itemMakeDs) &&
            this.auditTs.equals(that.auditTs) &&
            this.auditLogonId.equals(that.auditLogonId) &&
            this.auditProcessTx.equals(that.auditProcessTx) &&
            this.auditDeleteFl.equals(that.auditDeleteFl);
    }
    
    /** Per-field equality comparison; only returns equal if argument is the same class */
    @Override
    public boolean equals(Object that) {
        return (that instanceof Vwmtrli other) && other.canEqual(this) && this.equals(other);
    }
    
    /** Does {@code that} have the same runtime type as {@code this}? */
    public boolean canEqual(Object that) {
        return that instanceof Vwmtrli;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * result + Integer.hashCode(dlrNo);
        result = 31 * result + Objects.hashCode(trustNo);
        result = 31 * result + Integer.hashCode(trustLineNo);
        result = 31 * result + Integer.hashCode(balDueAmt);
        result = 31 * result + Integer.hashCode(netLineAmt);
        result = 31 * result + Objects.hashCode(prodCode);
        result = 31 * result + Integer.hashCode(dlrLocNo);
        result = 31 * result + Objects.hashCode(lastPayDate);
        result = 31 * result + Objects.hashCode(modelNo);
        result = 31 * result + Integer.hashCode(modelNoChgCnt);
        result = 31 * result + Objects.hashCode(serialNo);
        result = 31 * result + Integer.hashCode(serialNoChgCnt);
        result = 31 * result + Objects.hashCode(maintText);
        result = 31 * result + Objects.hashCode(lastAppliedDate);
        result = 31 * result + Integer.hashCode(serviceChrgAmt);
        result = 31 * result + Objects.hashCode(itemColor);
        result = 31 * result + Integer.hashCode(epdAddlRate);
        result = 31 * result + Integer.hashCode(epdExclAmt);
        result = 31 * result + Objects.hashCode(skuId);
        result = 31 * result + Integer.hashCode(brandIdNo);
        result = 31 * result + Objects.hashCode(lineTypeCode);
        result = 31 * result + Integer.hashCode(distNo);
        result = 31 * result + Integer.hashCode(curMeBalDueAmt);
        result = 31 * result + Integer.hashCode(prvMeBalDueAmt);
        result = 31 * result + Objects.hashCode(lineDetailFl);
        result = 31 * result + Objects.hashCode(lastPostDate);
        result = 31 * result + Objects.hashCode(dlrStockNo);
        result = 31 * result + Integer.hashCode(dlrChrgsPaidAmt);
        result = 31 * result + Integer.hashCode(trustLnUniqueId);
        result = 31 * result + Objects.hashCode(lastInspectDt);
        result = 31 * result + Objects.hashCode(modelNoYear);
        result = 31 * result + Objects.hashCode(itemMakeDs);
        result = 31 * result + Objects.hashCode(auditTs);
        result = 31 * result + Objects.hashCode(auditLogonId);
        result = 31 * result + Objects.hashCode(auditProcessTx);
        result = 31 * result + Objects.hashCode(auditDeleteFl);
        return result;
    }
    
    @Override
    public int compareTo(Vwmtrli that) {
        int c = 0;
        c = Integer.compare(this.dlrNo, that.dlrNo);
        if ( c != 0 ) return c;
        c = this.trustNo.compareTo(that.trustNo);
        if ( c != 0 ) return c;
        c = Integer.compare(this.trustLineNo, that.trustLineNo);
        if ( c != 0 ) return c;
        c = Integer.compare(this.balDueAmt, that.balDueAmt);
        if ( c != 0 ) return c;
        c = Integer.compare(this.netLineAmt, that.netLineAmt);
        if ( c != 0 ) return c;
        c = this.prodCode.compareTo(that.prodCode);
        if ( c != 0 ) return c;
        c = Integer.compare(this.dlrLocNo, that.dlrLocNo);
        if ( c != 0 ) return c;
        c = this.lastPayDate.compareTo(that.lastPayDate);
        if ( c != 0 ) return c;
        c = this.modelNo.compareTo(that.modelNo);
        if ( c != 0 ) return c;
        c = Integer.compare(this.modelNoChgCnt, that.modelNoChgCnt);
        if ( c != 0 ) return c;
        c = this.serialNo.compareTo(that.serialNo);
        if ( c != 0 ) return c;
        c = Integer.compare(this.serialNoChgCnt, that.serialNoChgCnt);
        if ( c != 0 ) return c;
        c = this.maintText.compareTo(that.maintText);
        if ( c != 0 ) return c;
        c = this.lastAppliedDate.compareTo(that.lastAppliedDate);
        if ( c != 0 ) return c;
        c = Integer.compare(this.serviceChrgAmt, that.serviceChrgAmt);
        if ( c != 0 ) return c;
        c = this.itemColor.compareTo(that.itemColor);
        if ( c != 0 ) return c;
        c = Integer.compare(this.epdAddlRate, that.epdAddlRate);
        if ( c != 0 ) return c;
        c = Integer.compare(this.epdExclAmt, that.epdExclAmt);
        if ( c != 0 ) return c;
        c = this.skuId.compareTo(that.skuId);
        if ( c != 0 ) return c;
        c = Integer.compare(this.brandIdNo, that.brandIdNo);
        if ( c != 0 ) return c;
        c = this.lineTypeCode.compareTo(that.lineTypeCode);
        if ( c != 0 ) return c;
        c = Integer.compare(this.distNo, that.distNo);
        if ( c != 0 ) return c;
        c = Integer.compare(this.curMeBalDueAmt, that.curMeBalDueAmt);
        if ( c != 0 ) return c;
        c = Integer.compare(this.prvMeBalDueAmt, that.prvMeBalDueAmt);
        if ( c != 0 ) return c;
        c = this.lineDetailFl.compareTo(that.lineDetailFl);
        if ( c != 0 ) return c;
        c = this.lastPostDate.compareTo(that.lastPostDate);
        if ( c != 0 ) return c;
        c = this.dlrStockNo.compareTo(that.dlrStockNo);
        if ( c != 0 ) return c;
        c = Integer.compare(this.dlrChrgsPaidAmt, that.dlrChrgsPaidAmt);
        if ( c != 0 ) return c;
        c = Integer.compare(this.trustLnUniqueId, that.trustLnUniqueId);
        if ( c != 0 ) return c;
        c = this.lastInspectDt.compareTo(that.lastInspectDt);
        if ( c != 0 ) return c;
        c = this.modelNoYear.compareTo(that.modelNoYear);
        if ( c != 0 ) return c;
        c = this.itemMakeDs.compareTo(that.itemMakeDs);
        if ( c != 0 ) return c;
        c = this.auditTs.compareTo(that.auditTs);
        if ( c != 0 ) return c;
        c = this.auditLogonId.compareTo(that.auditLogonId);
        if ( c != 0 ) return c;
        c = this.auditProcessTx.compareTo(that.auditProcessTx);
        if ( c != 0 ) return c;
        c = this.auditDeleteFl.compareTo(that.auditDeleteFl);
        if ( c == 0 && !(that.canEqual(this) && this.canEqual(that)) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
    }
    
    private static final ExternalDecimalAsIntField DLR_NO = factory.getExternalDecimalAsIntField(5, true);
    private static final StringField TRUST_NO = factory.getStringField(12);
    private static final ExternalDecimalAsIntField TRUST_LINE_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final ExternalDecimalAsIntField BAL_DUE_AMT = factory.getExternalDecimalAsIntField(7, true);
    private static final ExternalDecimalAsIntField NET_LINE_AMT = factory.getExternalDecimalAsIntField(7, true);
    private static final StringField PROD_CODE = factory.getStringField(5);
    private static final ExternalDecimalAsIntField DLR_LOC_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final StringField LAST_PAY_DATE = factory.getStringField(8);
    private static final DateTimeFormatter LAST_PAY_DATE_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final StringField MODEL_NO = factory.getStringField(13);
    private static final ExternalDecimalAsIntField MODEL_NO_CHG_CNT = factory.getExternalDecimalAsIntField(3, true);
    private static final StringField SERIAL_NO = factory.getStringField(21);
    private static final ExternalDecimalAsIntField SERIAL_NO_CHG_CNT = factory.getExternalDecimalAsIntField(3, true);
    private static final StringField MAINT_TEXT = factory.getStringField(53);
    private static final StringField LAST_APPLIED_DATE = factory.getStringField(8);
    private static final DateTimeFormatter LAST_APPLIED_DATE_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final ExternalDecimalAsIntField SERVICE_CHRG_AMT = factory.getExternalDecimalAsIntField(6, true);
    private static final StringField ITEM_COLOR = factory.getStringField(11);
    private static final ExternalDecimalAsIntField EPD_ADDL_RATE = factory.getExternalDecimalAsIntField(5, true);
    private static final ExternalDecimalAsIntField EPD_EXCL_AMT = factory.getExternalDecimalAsIntField(7, true);
    private static final StringField SKU_ID = factory.getStringField(25);
    private static final ExternalDecimalAsIntField BRAND_ID_NO = factory.getExternalDecimalAsIntField(3, true);
    private static final StringField LINE_TYPE_CODE = factory.getStringField(3);
    private static final ExternalDecimalAsIntField DIST_NO = factory.getExternalDecimalAsIntField(5, true);
    private static final ExternalDecimalAsIntField CUR_ME_BAL_DUE_AMT = factory.getExternalDecimalAsIntField(7, true);
    private static final ExternalDecimalAsIntField PRV_ME_BAL_DUE_AMT = factory.getExternalDecimalAsIntField(7, true);
    private static final StringField LINE_DETAIL_FL = factory.getStringField(2);
    private static final StringField LAST_POST_DATE = factory.getStringField(8);
    private static final DateTimeFormatter LAST_POST_DATE_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final StringField DLR_STOCK_NO = factory.getStringField(21);
    private static final ExternalDecimalAsIntField DLR_CHRGS_PAID_AMT = factory.getExternalDecimalAsIntField(7, true);
    private static final ExternalDecimalAsIntField TRUST_LN_UNIQUE_ID = factory.getExternalDecimalAsIntField(9, true);
    private static final StringField LAST_INSPECT_DT = factory.getStringField(8);
    private static final DateTimeFormatter LAST_INSPECT_DT_FMT = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final StringField MODEL_NO_YEAR = factory.getStringField(5);
    private static final StringField ITEM_MAKE_DS = factory.getStringField(41);
    private static final StringField AUDIT_TS = factory.getStringField(14);
    private static final DateTimeFormatter AUDIT_TS_FMT = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    private static final StringField AUDIT_LOGON_ID = factory.getStringField(9);
    private static final StringField AUDIT_PROCESS_TX = factory.getStringField(63);
    private static final StringField AUDIT_DELETE_FL = factory.getStringField(2);
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code Vwmtrli} object
     * @param bytes  preallocated byte array to store the object serialization
     * @param offset offset in the byte array where serialization should begin
     * @return the byte array, updated with the newly added data formatted as in the {@code VWMTRLI} record
     * @see "VWMTRLI record at VWMTRLI.CPY:9"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        DLR_NO.putInt(dlrNo, bytes, offset);
        TRUST_NO.putString(trustNo, bytes, offset);
        TRUST_LINE_NO.putInt(trustLineNo, bytes, offset);
        BAL_DUE_AMT.putInt(balDueAmt, bytes, offset);
        NET_LINE_AMT.putInt(netLineAmt, bytes, offset);
        PROD_CODE.putString(prodCode, bytes, offset);
        DLR_LOC_NO.putInt(dlrLocNo, bytes, offset);
        LAST_PAY_DATE.putString(lastPayDate.toLocalDate().format(LAST_PAY_DATE_FMT), bytes, offset);
        MODEL_NO.putString(modelNo, bytes, offset);
        MODEL_NO_CHG_CNT.putInt(modelNoChgCnt, bytes, offset);
        SERIAL_NO.putString(serialNo, bytes, offset);
        SERIAL_NO_CHG_CNT.putInt(serialNoChgCnt, bytes, offset);
        MAINT_TEXT.putString(maintText, bytes, offset);
        LAST_APPLIED_DATE.putString(lastAppliedDate.toLocalDate().format(LAST_APPLIED_DATE_FMT), bytes, offset);
        SERVICE_CHRG_AMT.putInt(serviceChrgAmt, bytes, offset);
        ITEM_COLOR.putString(itemColor, bytes, offset);
        EPD_ADDL_RATE.putInt(epdAddlRate, bytes, offset);
        EPD_EXCL_AMT.putInt(epdExclAmt, bytes, offset);
        SKU_ID.putString(skuId, bytes, offset);
        BRAND_ID_NO.putInt(brandIdNo, bytes, offset);
        LINE_TYPE_CODE.putString(lineTypeCode, bytes, offset);
        DIST_NO.putInt(distNo, bytes, offset);
        CUR_ME_BAL_DUE_AMT.putInt(curMeBalDueAmt, bytes, offset);
        PRV_ME_BAL_DUE_AMT.putInt(prvMeBalDueAmt, bytes, offset);
        LINE_DETAIL_FL.putString(lineDetailFl, bytes, offset);
        LAST_POST_DATE.putString(lastPostDate.toLocalDate().format(LAST_POST_DATE_FMT), bytes, offset);
        DLR_STOCK_NO.putString(dlrStockNo, bytes, offset);
        DLR_CHRGS_PAID_AMT.putInt(dlrChrgsPaidAmt, bytes, offset);
        TRUST_LN_UNIQUE_ID.putInt(trustLnUniqueId, bytes, offset);
        LAST_INSPECT_DT.putString(lastInspectDt.toLocalDate().format(LAST_INSPECT_DT_FMT), bytes, offset);
        MODEL_NO_YEAR.putString(modelNoYear, bytes, offset);
        ITEM_MAKE_DS.putString(itemMakeDs, bytes, offset);
        AUDIT_TS.putString(auditTs.toLocalDate().format(AUDIT_TS_FMT), bytes, offset);
        AUDIT_LOGON_ID.putString(auditLogonId, bytes, offset);
        AUDIT_PROCESS_TX.putString(auditProcessTx, bytes, offset);
        AUDIT_DELETE_FL.putString(auditDeleteFl, bytes, offset);
        return bytes;
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code Vwmtrli} object,
     * starting at the beginning of the array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes(byte[] bytes) {
        return getBytes(bytes, 0);
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code Vwmtrli} object,
     * allocating a new array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes() {
        return getBytes(new byte[numBytes()]);
    }
    
    /**
     * Retrieves a COBOL-format string representation of the {@code Vwmtrli} object
     * @return the result of {@link #getBytes()} interpreted using the IBM-1047 EBCDIC encoding
     */
    public final String toByteString() {
        return new String(getBytes(), encoding);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array
     * @param bytes  byte array formatted as in the {@code VWMTRLI} record; will be space-padded to length if necessary
     * @param offset offset in the byte array where deserialization should begin
     * @see "VWMTRLI record at VWMTRLI.CPY:9"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        dlrNo = DLR_NO.getInt(bytes, offset);
        trustNo = TRUST_NO.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        trustLineNo = TRUST_LINE_NO.getInt(bytes, offset);
        balDueAmt = BAL_DUE_AMT.getInt(bytes, offset);
        netLineAmt = NET_LINE_AMT.getInt(bytes, offset);
        prodCode = PROD_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        dlrLocNo = DLR_LOC_NO.getInt(bytes, offset);
        lastPayDate = Date.valueOf(LocalDate.parse(LAST_PAY_DATE.getString(bytes, offset), LAST_PAY_DATE_FMT));
        modelNo = MODEL_NO.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        modelNoChgCnt = MODEL_NO_CHG_CNT.getInt(bytes, offset);
        serialNo = SERIAL_NO.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        serialNoChgCnt = SERIAL_NO_CHG_CNT.getInt(bytes, offset);
        maintText = MAINT_TEXT.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        lastAppliedDate = Date.valueOf(LocalDate.parse(LAST_APPLIED_DATE.getString(bytes, offset), LAST_APPLIED_DATE_FMT));
        serviceChrgAmt = SERVICE_CHRG_AMT.getInt(bytes, offset);
        itemColor = ITEM_COLOR.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        epdAddlRate = EPD_ADDL_RATE.getInt(bytes, offset);
        epdExclAmt = EPD_EXCL_AMT.getInt(bytes, offset);
        skuId = SKU_ID.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        brandIdNo = BRAND_ID_NO.getInt(bytes, offset);
        lineTypeCode = LINE_TYPE_CODE.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        distNo = DIST_NO.getInt(bytes, offset);
        curMeBalDueAmt = CUR_ME_BAL_DUE_AMT.getInt(bytes, offset);
        prvMeBalDueAmt = PRV_ME_BAL_DUE_AMT.getInt(bytes, offset);
        lineDetailFl = LINE_DETAIL_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        lastPostDate = Date.valueOf(LocalDate.parse(LAST_POST_DATE.getString(bytes, offset), LAST_POST_DATE_FMT));
        dlrStockNo = DLR_STOCK_NO.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        dlrChrgsPaidAmt = DLR_CHRGS_PAID_AMT.getInt(bytes, offset);
        trustLnUniqueId = TRUST_LN_UNIQUE_ID.getInt(bytes, offset);
        lastInspectDt = Date.valueOf(LocalDate.parse(LAST_INSPECT_DT.getString(bytes, offset), LAST_INSPECT_DT_FMT));
        modelNoYear = MODEL_NO_YEAR.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        itemMakeDs = ITEM_MAKE_DS.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        auditTs = Date.valueOf(LocalDate.parse(AUDIT_TS.getString(bytes, offset), AUDIT_TS_FMT));
        auditLogonId = AUDIT_LOGON_ID.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        auditProcessTx = AUDIT_PROCESS_TX.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        auditDeleteFl = AUDIT_DELETE_FL.getString(bytes, offset).replaceFirst("\\s+\\z", "");
    }
    
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array,
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(byte[] bytes) {
        setBytes(bytes, 0);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format string.
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(String bytes) {
        setBytes(bytes.getBytes(encoding));
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
