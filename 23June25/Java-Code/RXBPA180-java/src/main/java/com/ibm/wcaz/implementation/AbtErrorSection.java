package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class AbtErrorSection extends AbtPgmErrorData {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private String abtErrorSectionName = "";
    private String abtErrorSectionSub = "";
    
    /** Initialize fields to non-null default values */
    public AbtErrorSection() {}
    
    /** Initialize all fields to provided values */
    public AbtErrorSection(String abtErrorActivity, int abtErrorAbendCode, String abtErrorSectionName, String abtErrorSectionSub) {
        super(abtErrorActivity, abtErrorAbendCode);
        this.abtErrorSectionName = abtErrorSectionName;
        this.abtErrorSectionSub = abtErrorSectionSub;
    }
    
    @Override
    public AbtErrorSection clone() throws CloneNotSupportedException {
        AbtErrorSection cloned = (AbtErrorSection) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code AbtErrorSection} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected AbtErrorSection(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code AbtErrorSection} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected AbtErrorSection(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code AbtErrorSection} object
     * @see #setBytes(byte[], int)
     */
    public static AbtErrorSection fromBytes(byte[] bytes, int offset) {
        return new AbtErrorSection(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code AbtErrorSection} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static AbtErrorSection fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code AbtErrorSection} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static AbtErrorSection fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getAbtErrorSectionName() {
        return this.abtErrorSectionName;
    }
    
    public void setAbtErrorSectionName(String abtErrorSectionName) {
        this.abtErrorSectionName = abtErrorSectionName;
    }
    
    public String getAbtErrorSectionSub() {
        return this.abtErrorSectionSub;
    }
    
    public void setAbtErrorSectionSub(String abtErrorSectionSub) {
        this.abtErrorSectionSub = abtErrorSectionSub;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        super.reset();
        abtErrorSectionName = "";
        abtErrorSectionSub = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder(super.toString());
        s.append("{ abtErrorSectionName=\"");
        s.append(getAbtErrorSectionName());
        s.append("\"");
        s.append(", abtErrorSectionSub=\"");
        s.append(getAbtErrorSectionSub());
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(AbtErrorSection that) {
        return super.equals(that) &&
            this.abtErrorSectionName.equals(that.abtErrorSectionName) &&
            this.abtErrorSectionSub.equals(that.abtErrorSectionSub);
    }
    
    @Override
    public boolean equals(Object that) {
        return (that instanceof AbtErrorSection other) && other.canEqual(this) && this.equals(other);
    }
    
    @Override
    public boolean canEqual(Object that) {
        return that instanceof AbtErrorSection;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * super.hashCode();
        result = 31 * result + Objects.hashCode(abtErrorSectionName);
        result = 31 * result + Objects.hashCode(abtErrorSectionSub);
        return result;
    }
    
    /** Per-field lexicographic comparison; only returns equal if argument is the same class */
    public int compareTo(AbtErrorSection that) {
        int c = 0;
        c = super.compareTo(that);
        if ( c != 0 ) return c;
        c = this.abtErrorSectionName.compareTo(that.abtErrorSectionName);
        if ( c != 0 ) return c;
        c = this.abtErrorSectionSub.compareTo(that.abtErrorSectionSub);
        if ( c == 0 && !that.canEqual(this) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    @Override
    public int compareTo(AbtPgmErrorData that) {
        if (that instanceof AbtErrorSection other) {
            return this.compareTo(other);
        } else {
            return super.compareTo(that);
        }
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
        factory.incrementOffset(AbtPgmErrorData.SIZE);
    }
    
    private static final StringField ABT_ERROR_SECTION_NAME = factory.getStringField(5);
    private static final StringField ABT_ERROR_SECTION_SUB = factory.getStringField(3);
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * {@inheritDoc}.
     * Calls {@link AbtPgmErrorData#getBytes(byte[], int)} to include any relevant information from the parent class.
     * @see "ABT-ERROR-SECTION record at MXWW03.CPY:53"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        super.getBytes(bytes, offset);
        ABT_ERROR_SECTION_NAME.putString(abtErrorSectionName, bytes, offset);
        ABT_ERROR_SECTION_SUB.putString(abtErrorSectionSub, bytes, offset);
        return bytes;
    }
    
    /**
     * {@inheritDoc}.
     * Calls {@link AbtPgmErrorData#setBytes(byte[], int)} to set parent-class state.
     * @see "ABT-ERROR-SECTION record at MXWW03.CPY:53"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        super.setBytes(bytes, offset);
        abtErrorSectionName = ABT_ERROR_SECTION_NAME.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        abtErrorSectionSub = ABT_ERROR_SECTION_SUB.getString(bytes, offset).replaceFirst("\\s+\\z", "");
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
