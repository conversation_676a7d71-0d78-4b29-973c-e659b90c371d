package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class AbnormalTerminationArea implements Cloneable, Comparable<AbnormalTerminationArea> {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private AbtTestFacilityArea abtTestFacilityArea = new AbtTestFacilityArea();
    private AbtControlInfo abtControlInfo = new AbtControlInfo();
    private AbtPgmErrorData abtPgmErrorData = new AbtPgmErrorData();
    private AbtDataAccessInfo abtDataAccessInfo = new AbtDataAccessInfo();
    
    /** Initialize fields to non-null default values */
    public AbnormalTerminationArea() {
        initFiller();
    }
    
    /** Initialize all fields to provided values */
    public AbnormalTerminationArea(AbtTestFacilityArea abtTestFacilityArea, AbtControlInfo abtControlInfo, AbtPgmErrorData abtPgmErrorData, AbtDataAccessInfo abtDataAccessInfo) {
        this.abtTestFacilityArea = abtTestFacilityArea;
        this.abtControlInfo = abtControlInfo;
        this.abtPgmErrorData = abtPgmErrorData;
        this.abtDataAccessInfo = abtDataAccessInfo;
        initFiller();
    }
    
    @Override
    public AbnormalTerminationArea clone() throws CloneNotSupportedException {
        AbnormalTerminationArea cloned = (AbnormalTerminationArea) super.clone();
        cloned.abtTestFacilityArea = this.abtTestFacilityArea.clone();
        cloned.abtControlInfo = this.abtControlInfo.clone();
        cloned.abtPgmErrorData = this.abtPgmErrorData.clone();
        cloned.abtDataAccessInfo = this.abtDataAccessInfo.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code AbnormalTerminationArea} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected AbnormalTerminationArea(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code AbnormalTerminationArea} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected AbnormalTerminationArea(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code AbnormalTerminationArea} object
     * @see #setBytes(byte[], int)
     */
    public static AbnormalTerminationArea fromBytes(byte[] bytes, int offset) {
        return new AbnormalTerminationArea(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code AbnormalTerminationArea} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static AbnormalTerminationArea fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code AbnormalTerminationArea} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static AbnormalTerminationArea fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public AbtTestFacilityArea getAbtTestFacilityArea() {
        return this.abtTestFacilityArea;
    }
    
    public void setAbtTestFacilityArea(AbtTestFacilityArea abtTestFacilityArea) {
        this.abtTestFacilityArea = abtTestFacilityArea;
    }
    
    public AbtControlInfo getAbtControlInfo() {
        return this.abtControlInfo;
    }
    
    public void setAbtControlInfo(AbtControlInfo abtControlInfo) {
        this.abtControlInfo = abtControlInfo;
    }
    
    public AbtPgmErrorData getAbtPgmErrorData() {
        return this.abtPgmErrorData;
    }
    
    public void setAbtPgmErrorData(AbtPgmErrorData abtPgmErrorData) {
        this.abtPgmErrorData = abtPgmErrorData;
    }
    
    public AbtDataAccessInfo getAbtDataAccessInfo() {
        return this.abtDataAccessInfo;
    }
    
    public void setAbtDataAccessInfo(AbtDataAccessInfo abtDataAccessInfo) {
        this.abtDataAccessInfo = abtDataAccessInfo;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        abtTestFacilityArea.reset();
        abtControlInfo.reset();
        abtPgmErrorData.reset();
        abtDataAccessInfo.reset();
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder();
        s.append("{ filler28=\"");
        s.append(new String(filler28, encoding));
        s.append("\"");
        s.append(", abtTestFacilityArea=\"");
        s.append(getAbtTestFacilityArea());
        s.append("\"");
        s.append(", abtControlInfo=\"");
        s.append(getAbtControlInfo());
        s.append("\"");
        s.append(", abtPgmErrorData=\"");
        s.append(getAbtPgmErrorData());
        s.append("\"");
        s.append(", abtDataAccessInfo=\"");
        s.append(getAbtDataAccessInfo());
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(AbnormalTerminationArea that) {
        return Arrays.equals(this.filler28, that.filler28) &&
            this.abtTestFacilityArea.equals(that.abtTestFacilityArea) &&
            this.abtControlInfo.equals(that.abtControlInfo) &&
            this.abtPgmErrorData.equals(that.abtPgmErrorData) &&
            this.abtDataAccessInfo.equals(that.abtDataAccessInfo);
    }
    
    /** Per-field equality comparison; only returns equal if argument is the same class */
    @Override
    public boolean equals(Object that) {
        return (that instanceof AbnormalTerminationArea other) && other.canEqual(this) && this.equals(other);
    }
    
    /** Does {@code that} have the same runtime type as {@code this}? */
    public boolean canEqual(Object that) {
        return that instanceof AbnormalTerminationArea;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * result + Arrays.hashCode(filler28);
        result = 31 * result + Objects.hashCode(abtTestFacilityArea);
        result = 31 * result + Objects.hashCode(abtControlInfo);
        result = 31 * result + Objects.hashCode(abtPgmErrorData);
        result = 31 * result + Objects.hashCode(abtDataAccessInfo);
        return result;
    }
    
    @Override
    public int compareTo(AbnormalTerminationArea that) {
        int c = 0;
        c = Arrays.compare(this.filler28, that.filler28);
        if ( c != 0 ) return c;
        c = this.abtTestFacilityArea.compareTo(that.abtTestFacilityArea);
        if ( c != 0 ) return c;
        c = this.abtControlInfo.compareTo(that.abtControlInfo);
        if ( c != 0 ) return c;
        c = this.abtPgmErrorData.compareTo(that.abtPgmErrorData);
        if ( c != 0 ) return c;
        c = this.abtDataAccessInfo.compareTo(that.abtDataAccessInfo);
        if ( c == 0 && !(that.canEqual(this) && this.canEqual(that)) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
    }
    
    private static final ByteArrayField FILLER_28 = factory.getByteArrayField(8);
    private byte[] filler28 = new byte[8];
    private static final ByteArrayField ABT_TEST_FACILITY_AREA = factory.getByteArrayField(AbtTestFacilityArea.SIZE);
    private static final ByteArrayField ABT_CONTROL_INFO = factory.getByteArrayField(AbtControlInfo.SIZE);
    private static final ByteArrayField ABT_PGM_ERROR_DATA = factory.getByteArrayField(AbtPgmErrorData.SIZE);
    private static final ByteArrayField ABT_DATA_ACCESS_INFO = factory.getByteArrayField(AbtDataAccessInfo.SIZE);
    private void initFiller() {
        new StringField(0, 8).putString("ABT AREA", filler28);
    }
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code AbnormalTerminationArea} object
     * @param bytes  preallocated byte array to store the object serialization
     * @param offset offset in the byte array where serialization should begin
     * @return the byte array, updated with the newly added data formatted as in the {@code ABNORMAL-TERMINATION-AREA} record
     * @see "ABNORMAL-TERMINATION-AREA record at MXWW03.CPY:5"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        FILLER_28.putByteArray(filler28, bytes, offset);
        abtTestFacilityArea.getBytes(bytes, ABT_TEST_FACILITY_AREA.getOffset() + offset);
        abtControlInfo.getBytes(bytes, ABT_CONTROL_INFO.getOffset() + offset);
        abtPgmErrorData.getBytes(bytes, ABT_PGM_ERROR_DATA.getOffset() + offset);
        abtDataAccessInfo.getBytes(bytes, ABT_DATA_ACCESS_INFO.getOffset() + offset);
        return bytes;
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code AbnormalTerminationArea} object,
     * starting at the beginning of the array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes(byte[] bytes) {
        return getBytes(bytes, 0);
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code AbnormalTerminationArea} object,
     * allocating a new array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes() {
        return getBytes(new byte[numBytes()]);
    }
    
    /**
     * Retrieves a COBOL-format string representation of the {@code AbnormalTerminationArea} object
     * @return the result of {@link #getBytes()} interpreted using the IBM-1047 EBCDIC encoding
     */
    public final String toByteString() {
        return new String(getBytes(), encoding);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array
     * @param bytes  byte array formatted as in the {@code ABNORMAL-TERMINATION-AREA} record; will be space-padded to length if necessary
     * @param offset offset in the byte array where deserialization should begin
     * @see "ABNORMAL-TERMINATION-AREA record at MXWW03.CPY:5"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        FILLER_28.getByteArray(bytes, offset);
        abtTestFacilityArea.setBytes(bytes, ABT_TEST_FACILITY_AREA.getOffset() + offset);
        abtControlInfo.setBytes(bytes, ABT_CONTROL_INFO.getOffset() + offset);
        abtPgmErrorData.setBytes(bytes, ABT_PGM_ERROR_DATA.getOffset() + offset);
        abtDataAccessInfo.setBytes(bytes, ABT_DATA_ACCESS_INFO.getOffset() + offset);
    }
    
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array,
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(byte[] bytes) {
        setBytes(bytes, 0);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format string.
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(String bytes) {
        setBytes(bytes.getBytes(encoding));
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
