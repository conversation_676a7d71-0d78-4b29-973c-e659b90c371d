package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.BinaryAsIntField;
import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class BatchErrorArea implements Cloneable, Comparable<BatchErrorArea> {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private int batchErrorKeyL = 0;
    private int batchErrorCmntL = 0;
    private char batchAbendControl = ' ';
    private String batchErrorKey1 = "";
    private String batchErrorKey2 = "";
    private String batchErrorKey3 = "";
    private String batchErrorKey4 = "";
    private String batchErrorCmnt1 = "";
    private String batchErrorCmnt2 = "";
    private String batchErrorCmnt3 = "";
    private String batchErrorCmnt4 = "";
    
    /** Initialize fields to non-null default values */
    public BatchErrorArea() {}
    
    /** Initialize all fields to provided values */
    public BatchErrorArea(int batchErrorKeyL, int batchErrorCmntL, char batchAbendControl, String batchErrorKey1, String batchErrorKey2, String batchErrorKey3, String batchErrorKey4, String batchErrorCmnt1, String batchErrorCmnt2, String batchErrorCmnt3, String batchErrorCmnt4) {
        this.batchErrorKeyL = batchErrorKeyL;
        this.batchErrorCmntL = batchErrorCmntL;
        this.batchAbendControl = batchAbendControl;
        this.batchErrorKey1 = batchErrorKey1;
        this.batchErrorKey2 = batchErrorKey2;
        this.batchErrorKey3 = batchErrorKey3;
        this.batchErrorKey4 = batchErrorKey4;
        this.batchErrorCmnt1 = batchErrorCmnt1;
        this.batchErrorCmnt2 = batchErrorCmnt2;
        this.batchErrorCmnt3 = batchErrorCmnt3;
        this.batchErrorCmnt4 = batchErrorCmnt4;
    }
    
    @Override
    public BatchErrorArea clone() throws CloneNotSupportedException {
        BatchErrorArea cloned = (BatchErrorArea) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code BatchErrorArea} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected BatchErrorArea(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code BatchErrorArea} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected BatchErrorArea(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code BatchErrorArea} object
     * @see #setBytes(byte[], int)
     */
    public static BatchErrorArea fromBytes(byte[] bytes, int offset) {
        return new BatchErrorArea(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code BatchErrorArea} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static BatchErrorArea fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code BatchErrorArea} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static BatchErrorArea fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public int getBatchErrorKeyL() {
        return this.batchErrorKeyL;
    }
    
    public void setBatchErrorKeyL(int batchErrorKeyL) {
        this.batchErrorKeyL = batchErrorKeyL;
    }
    
    public int getBatchErrorCmntL() {
        return this.batchErrorCmntL;
    }
    
    public void setBatchErrorCmntL(int batchErrorCmntL) {
        this.batchErrorCmntL = batchErrorCmntL;
    }
    
    public char getBatchAbendControl() {
        return this.batchAbendControl;
    }
    
    public void setBatchAbendControl(char batchAbendControl) {
        this.batchAbendControl = batchAbendControl;
    }
    
    public String getBatchErrorKey1() {
        return this.batchErrorKey1;
    }
    
    public void setBatchErrorKey1(String batchErrorKey1) {
        this.batchErrorKey1 = batchErrorKey1;
    }
    
    public String getBatchErrorKey2() {
        return this.batchErrorKey2;
    }
    
    public void setBatchErrorKey2(String batchErrorKey2) {
        this.batchErrorKey2 = batchErrorKey2;
    }
    
    public String getBatchErrorKey3() {
        return this.batchErrorKey3;
    }
    
    public void setBatchErrorKey3(String batchErrorKey3) {
        this.batchErrorKey3 = batchErrorKey3;
    }
    
    public String getBatchErrorKey4() {
        return this.batchErrorKey4;
    }
    
    public void setBatchErrorKey4(String batchErrorKey4) {
        this.batchErrorKey4 = batchErrorKey4;
    }
    
    public String getBatchErrorCmnt1() {
        return this.batchErrorCmnt1;
    }
    
    public void setBatchErrorCmnt1(String batchErrorCmnt1) {
        this.batchErrorCmnt1 = batchErrorCmnt1;
    }
    
    public String getBatchErrorCmnt2() {
        return this.batchErrorCmnt2;
    }
    
    public void setBatchErrorCmnt2(String batchErrorCmnt2) {
        this.batchErrorCmnt2 = batchErrorCmnt2;
    }
    
    public String getBatchErrorCmnt3() {
        return this.batchErrorCmnt3;
    }
    
    public void setBatchErrorCmnt3(String batchErrorCmnt3) {
        this.batchErrorCmnt3 = batchErrorCmnt3;
    }
    
    public String getBatchErrorCmnt4() {
        return this.batchErrorCmnt4;
    }
    
    public void setBatchErrorCmnt4(String batchErrorCmnt4) {
        this.batchErrorCmnt4 = batchErrorCmnt4;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        batchErrorKeyL = 0;
        batchErrorCmntL = 0;
        batchAbendControl = ' ';
        batchErrorKey1 = "";
        batchErrorKey2 = "";
        batchErrorKey3 = "";
        batchErrorKey4 = "";
        batchErrorCmnt1 = "";
        batchErrorCmnt2 = "";
        batchErrorCmnt3 = "";
        batchErrorCmnt4 = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder();
        s.append("{ batchErrorKeyL=\"");
        s.append(getBatchErrorKeyL());
        s.append("\"");
        s.append(", batchErrorCmntL=\"");
        s.append(getBatchErrorCmntL());
        s.append("\"");
        s.append(", batchAbendControl=\"");
        s.append(getBatchAbendControl());
        s.append("\"");
        s.append(", batchErrorKey1=\"");
        s.append(getBatchErrorKey1());
        s.append("\"");
        s.append(", batchErrorKey2=\"");
        s.append(getBatchErrorKey2());
        s.append("\"");
        s.append(", batchErrorKey3=\"");
        s.append(getBatchErrorKey3());
        s.append("\"");
        s.append(", batchErrorKey4=\"");
        s.append(getBatchErrorKey4());
        s.append("\"");
        s.append(", batchErrorCmnt1=\"");
        s.append(getBatchErrorCmnt1());
        s.append("\"");
        s.append(", batchErrorCmnt2=\"");
        s.append(getBatchErrorCmnt2());
        s.append("\"");
        s.append(", batchErrorCmnt3=\"");
        s.append(getBatchErrorCmnt3());
        s.append("\"");
        s.append(", batchErrorCmnt4=\"");
        s.append(getBatchErrorCmnt4());
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(BatchErrorArea that) {
        return this.batchErrorKeyL == that.batchErrorKeyL &&
            this.batchErrorCmntL == that.batchErrorCmntL &&
            this.batchAbendControl == that.batchAbendControl &&
            this.batchErrorKey1.equals(that.batchErrorKey1) &&
            this.batchErrorKey2.equals(that.batchErrorKey2) &&
            this.batchErrorKey3.equals(that.batchErrorKey3) &&
            this.batchErrorKey4.equals(that.batchErrorKey4) &&
            this.batchErrorCmnt1.equals(that.batchErrorCmnt1) &&
            this.batchErrorCmnt2.equals(that.batchErrorCmnt2) &&
            this.batchErrorCmnt3.equals(that.batchErrorCmnt3) &&
            this.batchErrorCmnt4.equals(that.batchErrorCmnt4);
    }
    
    /** Per-field equality comparison; only returns equal if argument is the same class */
    @Override
    public boolean equals(Object that) {
        return (that instanceof BatchErrorArea other) && other.canEqual(this) && this.equals(other);
    }
    
    /** Does {@code that} have the same runtime type as {@code this}? */
    public boolean canEqual(Object that) {
        return that instanceof BatchErrorArea;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * result + Integer.hashCode(batchErrorKeyL);
        result = 31 * result + Integer.hashCode(batchErrorCmntL);
        result = 31 * result + Character.hashCode(batchAbendControl);
        result = 31 * result + Objects.hashCode(batchErrorKey1);
        result = 31 * result + Objects.hashCode(batchErrorKey2);
        result = 31 * result + Objects.hashCode(batchErrorKey3);
        result = 31 * result + Objects.hashCode(batchErrorKey4);
        result = 31 * result + Objects.hashCode(batchErrorCmnt1);
        result = 31 * result + Objects.hashCode(batchErrorCmnt2);
        result = 31 * result + Objects.hashCode(batchErrorCmnt3);
        result = 31 * result + Objects.hashCode(batchErrorCmnt4);
        return result;
    }
    
    @Override
    public int compareTo(BatchErrorArea that) {
        int c = 0;
        c = Integer.compare(this.batchErrorKeyL, that.batchErrorKeyL);
        if ( c != 0 ) return c;
        c = Integer.compare(this.batchErrorCmntL, that.batchErrorCmntL);
        if ( c != 0 ) return c;
        c = Character.compare(this.batchAbendControl, that.batchAbendControl);
        if ( c != 0 ) return c;
        c = this.batchErrorKey1.compareTo(that.batchErrorKey1);
        if ( c != 0 ) return c;
        c = this.batchErrorKey2.compareTo(that.batchErrorKey2);
        if ( c != 0 ) return c;
        c = this.batchErrorKey3.compareTo(that.batchErrorKey3);
        if ( c != 0 ) return c;
        c = this.batchErrorKey4.compareTo(that.batchErrorKey4);
        if ( c != 0 ) return c;
        c = this.batchErrorCmnt1.compareTo(that.batchErrorCmnt1);
        if ( c != 0 ) return c;
        c = this.batchErrorCmnt2.compareTo(that.batchErrorCmnt2);
        if ( c != 0 ) return c;
        c = this.batchErrorCmnt3.compareTo(that.batchErrorCmnt3);
        if ( c != 0 ) return c;
        c = this.batchErrorCmnt4.compareTo(that.batchErrorCmnt4);
        if ( c == 0 && !(that.canEqual(this) && this.canEqual(that)) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
    }
    
    private static final BinaryAsIntField BATCH_ERROR_KEY_L = factory.getBinaryAsIntField(4, true);
    private static final BinaryAsIntField BATCH_ERROR_CMNT_L = factory.getBinaryAsIntField(4, true);
    private static final StringField BATCH_ABEND_CONTROL = factory.getStringField(1);
    private static final StringField BATCH_ERROR_KEY_1 = factory.getStringField(72);
    private static final StringField BATCH_ERROR_KEY_2 = factory.getStringField(72);
    private static final StringField BATCH_ERROR_KEY_3 = factory.getStringField(72);
    private static final StringField BATCH_ERROR_KEY_4 = factory.getStringField(38);
    private static final StringField BATCH_ERROR_CMNT_1 = factory.getStringField(72);
    private static final StringField BATCH_ERROR_CMNT_2 = factory.getStringField(72);
    private static final StringField BATCH_ERROR_CMNT_3 = factory.getStringField(72);
    private static final StringField BATCH_ERROR_CMNT_4 = factory.getStringField(38);
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code BatchErrorArea} object
     * @param bytes  preallocated byte array to store the object serialization
     * @param offset offset in the byte array where serialization should begin
     * @return the byte array, updated with the newly added data formatted as in the {@code BATCH-ERROR-AREA} record
     * @see "BATCH-ERROR-AREA record at MXWW03.CPY:167"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        BATCH_ERROR_KEY_L.putInt(batchErrorKeyL, bytes, offset);
        BATCH_ERROR_CMNT_L.putInt(batchErrorCmntL, bytes, offset);
        BATCH_ABEND_CONTROL.putString(Character.toString(batchAbendControl), bytes, offset);
        BATCH_ERROR_KEY_1.putString(batchErrorKey1, bytes, offset);
        BATCH_ERROR_KEY_2.putString(batchErrorKey2, bytes, offset);
        BATCH_ERROR_KEY_3.putString(batchErrorKey3, bytes, offset);
        BATCH_ERROR_KEY_4.putString(batchErrorKey4, bytes, offset);
        BATCH_ERROR_CMNT_1.putString(batchErrorCmnt1, bytes, offset);
        BATCH_ERROR_CMNT_2.putString(batchErrorCmnt2, bytes, offset);
        BATCH_ERROR_CMNT_3.putString(batchErrorCmnt3, bytes, offset);
        BATCH_ERROR_CMNT_4.putString(batchErrorCmnt4, bytes, offset);
        return bytes;
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code BatchErrorArea} object,
     * starting at the beginning of the array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes(byte[] bytes) {
        return getBytes(bytes, 0);
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code BatchErrorArea} object,
     * allocating a new array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes() {
        return getBytes(new byte[numBytes()]);
    }
    
    /**
     * Retrieves a COBOL-format string representation of the {@code BatchErrorArea} object
     * @return the result of {@link #getBytes()} interpreted using the IBM-1047 EBCDIC encoding
     */
    public final String toByteString() {
        return new String(getBytes(), encoding);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array
     * @param bytes  byte array formatted as in the {@code BATCH-ERROR-AREA} record; will be space-padded to length if necessary
     * @param offset offset in the byte array where deserialization should begin
     * @see "BATCH-ERROR-AREA record at MXWW03.CPY:167"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        batchErrorKeyL = BATCH_ERROR_KEY_L.getInt(bytes, offset);
        batchErrorCmntL = BATCH_ERROR_CMNT_L.getInt(bytes, offset);
        batchAbendControl = BATCH_ABEND_CONTROL.getString(bytes, offset).charAt(0);
        batchErrorKey1 = BATCH_ERROR_KEY_1.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        batchErrorKey2 = BATCH_ERROR_KEY_2.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        batchErrorKey3 = BATCH_ERROR_KEY_3.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        batchErrorKey4 = BATCH_ERROR_KEY_4.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        batchErrorCmnt1 = BATCH_ERROR_CMNT_1.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        batchErrorCmnt2 = BATCH_ERROR_CMNT_2.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        batchErrorCmnt3 = BATCH_ERROR_CMNT_3.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        batchErrorCmnt4 = BATCH_ERROR_CMNT_4.getString(bytes, offset).replaceFirst("\\s+\\z", "");
    }
    
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array,
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(byte[] bytes) {
        setBytes(bytes, 0);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format string.
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(String bytes) {
        setBytes(bytes.getBytes(encoding));
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
