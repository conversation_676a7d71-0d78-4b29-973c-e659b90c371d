package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.ByteArrayField;
import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class AbtDataAccessInfo implements Cloneable, Comparable<AbtDataAccessInfo> {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private static final String ABT_DA_OK_VALUE = "OK ";
    private static final String ABT_DA_DUPLICATE_VALUE = "DUP";
    private static final String ABT_DA_NOTAVAIL_VALUE = "NAV";
    private static final String ABT_DA_NOTFOUND_VALUE = "NFD";
    private static final String ABT_DA_ENDFILE_VALUE_1 = "EOF";
    private static final String ABT_DA_ENDFILE_VALUE_2 = "NFD";
    private static final String ABT_DA_LOGICERR_VALUE = "LOG";
    private static final String ABT_DA_SECURITY_VALUE = "SEC";
    private static final String ABT_DA_DBMERROR_VALUE = "DBM";
    private static final String ABT_DA_ANYERROR_VALUE_1 = "DUP";
    private static final String ABT_DA_ANYERROR_VALUE_2 = "NAV";
    private static final String ABT_DA_ANYERROR_VALUE_3 = "NFD";
    private static final String ABT_DA_ANYERROR_VALUE_4 = "EOF";
    private static final String ABT_DA_ANYERROR_VALUE_5 = "LOG";
    private static final String ABT_DA_ANYERROR_VALUE_6 = "SEC";
    private static final String ABT_DA_ANYERROR_VALUE_7 = "DBM";
    
    private String abtU100Sub = "";
    private String abtDaAccessName = "";
    private String abtDaGenericStatus = "";
    
    /** Initialize fields to non-null default values */
    public AbtDataAccessInfo() {
        initFiller();
    }
    
    /** Initialize all fields to provided values */
    public AbtDataAccessInfo(String abtU100Sub, String abtDaAccessName, String abtDaGenericStatus) {
        this.abtU100Sub = abtU100Sub;
        this.abtDaAccessName = abtDaAccessName;
        this.abtDaGenericStatus = abtDaGenericStatus;
        initFiller();
    }
    
    @Override
    public AbtDataAccessInfo clone() throws CloneNotSupportedException {
        AbtDataAccessInfo cloned = (AbtDataAccessInfo) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code AbtDataAccessInfo} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected AbtDataAccessInfo(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code AbtDataAccessInfo} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected AbtDataAccessInfo(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code AbtDataAccessInfo} object
     * @see #setBytes(byte[], int)
     */
    public static AbtDataAccessInfo fromBytes(byte[] bytes, int offset) {
        return new AbtDataAccessInfo(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code AbtDataAccessInfo} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static AbtDataAccessInfo fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code AbtDataAccessInfo} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static AbtDataAccessInfo fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getAbtU100Sub() {
        return this.abtU100Sub;
    }
    
    public void setAbtU100Sub(String abtU100Sub) {
        this.abtU100Sub = abtU100Sub;
    }
    
    public String getAbtDaAccessName() {
        return this.abtDaAccessName;
    }
    
    public void setAbtDaAccessName(String abtDaAccessName) {
        this.abtDaAccessName = abtDaAccessName;
    }
    
    public String getAbtDaGenericStatus() {
        return this.abtDaGenericStatus;
    }
    
    public void setAbtDaGenericStatus(String abtDaGenericStatus) {
        this.abtDaGenericStatus = abtDaGenericStatus;
    }
    
    public boolean isAbtDaOk() {
        return abtDaGenericStatus.equals(ABT_DA_OK_VALUE);
    }
    
    public void setAbtDaOk() {
        abtDaGenericStatus = ABT_DA_OK_VALUE;
    }
    
    public boolean isAbtDaDuplicate() {
        return abtDaGenericStatus.equals(ABT_DA_DUPLICATE_VALUE);
    }
    
    public void setAbtDaDuplicate() {
        abtDaGenericStatus = ABT_DA_DUPLICATE_VALUE;
    }
    
    public boolean isAbtDaNotavail() {
        return abtDaGenericStatus.equals(ABT_DA_NOTAVAIL_VALUE);
    }
    
    public void setAbtDaNotavail() {
        abtDaGenericStatus = ABT_DA_NOTAVAIL_VALUE;
    }
    
    public boolean isAbtDaNotfound() {
        return abtDaGenericStatus.equals(ABT_DA_NOTFOUND_VALUE);
    }
    
    public void setAbtDaNotfound() {
        abtDaGenericStatus = ABT_DA_NOTFOUND_VALUE;
    }
    
    public boolean isAbtDaEndfile() {
        return
            (abtDaGenericStatus.equals(ABT_DA_ENDFILE_VALUE_1))
            || (abtDaGenericStatus.equals(ABT_DA_ENDFILE_VALUE_2));
    }
    
    public void setAbtDaEndfile() {
        abtDaGenericStatus = ABT_DA_ENDFILE_VALUE_1;
    }
    
    public boolean isAbtDaLogicerr() {
        return abtDaGenericStatus.equals(ABT_DA_LOGICERR_VALUE);
    }
    
    public void setAbtDaLogicerr() {
        abtDaGenericStatus = ABT_DA_LOGICERR_VALUE;
    }
    
    public boolean isAbtDaSecurity() {
        return abtDaGenericStatus.equals(ABT_DA_SECURITY_VALUE);
    }
    
    public void setAbtDaSecurity() {
        abtDaGenericStatus = ABT_DA_SECURITY_VALUE;
    }
    
    public boolean isAbtDaDbmerror() {
        return abtDaGenericStatus.equals(ABT_DA_DBMERROR_VALUE);
    }
    
    public void setAbtDaDbmerror() {
        abtDaGenericStatus = ABT_DA_DBMERROR_VALUE;
    }
    
    public boolean isAbtDaAnyerror() {
        return
            (abtDaGenericStatus.equals(ABT_DA_ANYERROR_VALUE_1))
            || (abtDaGenericStatus.equals(ABT_DA_ANYERROR_VALUE_2))
            || (abtDaGenericStatus.equals(ABT_DA_ANYERROR_VALUE_3))
            || (abtDaGenericStatus.equals(ABT_DA_ANYERROR_VALUE_4))
            || (abtDaGenericStatus.equals(ABT_DA_ANYERROR_VALUE_5))
            || (abtDaGenericStatus.equals(ABT_DA_ANYERROR_VALUE_6))
            || (abtDaGenericStatus.equals(ABT_DA_ANYERROR_VALUE_7));
    }
    
    public void setAbtDaAnyerror() {
        abtDaGenericStatus = ABT_DA_ANYERROR_VALUE_1;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        abtU100Sub = "";
        abtDaAccessName = "";
        abtDaGenericStatus = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder();
        s.append("{ abtU100Sub=\"");
        s.append(getAbtU100Sub());
        s.append("\"");
        s.append(", filler33=\"");
        s.append(new String(filler33, encoding));
        s.append("\"");
        s.append(", abtDaAccessName=\"");
        s.append(getAbtDaAccessName());
        s.append("\"");
        s.append(", abtDaGenericStatus=\"");
        s.append(getAbtDaGenericStatus());
        s.append("\"");
        s.append(", filler34=\"");
        s.append(new String(filler34, encoding));
        s.append("\"");
        s.append(", filler43=\"");
        s.append(new String(filler43, encoding));
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(AbtDataAccessInfo that) {
        return this.abtU100Sub.equals(that.abtU100Sub) &&
            Arrays.equals(this.filler33, that.filler33) &&
            this.abtDaAccessName.equals(that.abtDaAccessName) &&
            this.abtDaGenericStatus.equals(that.abtDaGenericStatus) &&
            Arrays.equals(this.filler34, that.filler34) &&
            Arrays.equals(this.filler43, that.filler43);
    }
    
    /** Per-field equality comparison; only returns equal if argument is the same class */
    @Override
    public boolean equals(Object that) {
        return (that instanceof AbtDataAccessInfo other) && other.canEqual(this) && this.equals(other);
    }
    
    /** Does {@code that} have the same runtime type as {@code this}? */
    public boolean canEqual(Object that) {
        return that instanceof AbtDataAccessInfo;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * result + Objects.hashCode(abtU100Sub);
        result = 31 * result + Arrays.hashCode(filler33);
        result = 31 * result + Objects.hashCode(abtDaAccessName);
        result = 31 * result + Objects.hashCode(abtDaGenericStatus);
        result = 31 * result + Arrays.hashCode(filler34);
        result = 31 * result + Arrays.hashCode(filler43);
        return result;
    }
    
    @Override
    public int compareTo(AbtDataAccessInfo that) {
        int c = 0;
        c = this.abtU100Sub.compareTo(that.abtU100Sub);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler33, that.filler33);
        if ( c != 0 ) return c;
        c = this.abtDaAccessName.compareTo(that.abtDaAccessName);
        if ( c != 0 ) return c;
        c = this.abtDaGenericStatus.compareTo(that.abtDaGenericStatus);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler34, that.filler34);
        if ( c != 0 ) return c;
        c = Arrays.compare(this.filler43, that.filler43);
        if ( c == 0 && !(that.canEqual(this) && this.canEqual(that)) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
    }
    
    private static final StringField ABT_U_100_SUB = factory.getStringField(3);
    private static final ByteArrayField FILLER_33 = factory.getByteArrayField(1);
    private byte[] filler33 = new byte[1];
    private static final StringField ABT_DA_ACCESS_NAME = factory.getStringField(8);
    private static final StringField ABT_DA_GENERIC_STATUS = factory.getStringField(3);
    private static final ByteArrayField FILLER_34 = factory.getByteArrayField(1);
    private byte[] filler34 = new byte[1];
    private static final ByteArrayField FILLER_43 = factory.getByteArrayField(16);
    private byte[] filler43 = new byte[16];
    private void initFiller() {
        new StringField(0, 1).putString(Character.toString('\0'), filler33);
        new StringField(0, 1).putString(Character.toString('\0'), filler34);
    }
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code AbtDataAccessInfo} object
     * @param bytes  preallocated byte array to store the object serialization
     * @param offset offset in the byte array where serialization should begin
     * @return the byte array, updated with the newly added data formatted as in the {@code ABT-DATA-ACCESS-INFO} record
     * @see "ABT-DATA-ACCESS-INFO record at MXWW03.CPY:72"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        ABT_U_100_SUB.putString(abtU100Sub, bytes, offset);
        FILLER_33.putByteArray(filler33, bytes, offset);
        ABT_DA_ACCESS_NAME.putString(abtDaAccessName, bytes, offset);
        ABT_DA_GENERIC_STATUS.putString(abtDaGenericStatus, bytes, offset);
        FILLER_34.putByteArray(filler34, bytes, offset);
        FILLER_43.putByteArray(filler43, bytes, offset);
        return bytes;
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code AbtDataAccessInfo} object,
     * starting at the beginning of the array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes(byte[] bytes) {
        return getBytes(bytes, 0);
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code AbtDataAccessInfo} object,
     * allocating a new array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes() {
        return getBytes(new byte[numBytes()]);
    }
    
    /**
     * Retrieves a COBOL-format string representation of the {@code AbtDataAccessInfo} object
     * @return the result of {@link #getBytes()} interpreted using the IBM-1047 EBCDIC encoding
     */
    public final String toByteString() {
        return new String(getBytes(), encoding);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array
     * @param bytes  byte array formatted as in the {@code ABT-DATA-ACCESS-INFO} record; will be space-padded to length if necessary
     * @param offset offset in the byte array where deserialization should begin
     * @see "ABT-DATA-ACCESS-INFO record at MXWW03.CPY:72"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        abtU100Sub = ABT_U_100_SUB.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_33.getByteArray(bytes, offset);
        abtDaAccessName = ABT_DA_ACCESS_NAME.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        abtDaGenericStatus = ABT_DA_GENERIC_STATUS.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        FILLER_34.getByteArray(bytes, offset);
        FILLER_43.getByteArray(bytes, offset);
    }
    
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array,
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(byte[] bytes) {
        setBytes(bytes, 0);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format string.
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(String bytes) {
        setBytes(bytes.getBytes(encoding));
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
