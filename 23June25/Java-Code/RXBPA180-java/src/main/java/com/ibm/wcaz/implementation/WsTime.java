package com.ibm.wcaz.implementation;

import com.ibm.jzos.fields.CobolDatatypeFactory;
import com.ibm.jzos.fields.StringField;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Objects;

public class WsTime implements Cloneable, Comparable<WsTime> {
    private static final Charset encoding = Charset.forName("IBM-1047");
    
    private String wsHour = "";
    private char wsTmSep = ':';
    private String wsMin = "";
    
    /** Initialize fields to non-null default values */
    public WsTime() {}
    
    /** Initialize all fields to provided values */
    public WsTime(String wsHour, char wsTmSep, String wsMin) {
        this.wsHour = wsHour;
        this.wsTmSep = wsTmSep;
        this.wsMin = wsMin;
    }
    
    @Override
    public WsTime clone() throws CloneNotSupportedException {
        WsTime cloned = (WsTime) super.clone();
        return cloned;
    }
    
    /**
     * Initialize {@code WsTime} using a COBOL-formatted byte array
     * @see #setBytes(byte[], int)
     */
    protected WsTime(byte[] bytes, int offset) {
        setBytes(bytes, offset);
    }
    
    /**
     * Initialize {@code WsTime} using a COBOL-formatted byte array
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    protected WsTime(byte[] bytes) {
        this(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code WsTime} object
     * @see #setBytes(byte[], int)
     */
    public static WsTime fromBytes(byte[] bytes, int offset) {
        return new WsTime(bytes, offset);
    }
    
    /**
     * Deserialize the COBOL-formatted byte array into a new {@code WsTime} object
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public static WsTime fromBytes(byte[] bytes) {
        return fromBytes(bytes, 0);
    }
    
    /**
     * Deserialize the COBOL-formatted string into a new {@code WsTime} object
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public static WsTime fromBytes(String bytes) {
        return fromBytes(bytes.getBytes(encoding));
    }
    
    public String getWsHour() {
        return this.wsHour;
    }
    
    public void setWsHour(String wsHour) {
        this.wsHour = wsHour;
    }
    
    public char getWsTmSep() {
        return this.wsTmSep;
    }
    
    public void setWsTmSep(char wsTmSep) {
        this.wsTmSep = wsTmSep;
    }
    
    public String getWsMin() {
        return this.wsMin;
    }
    
    public void setWsMin(String wsMin) {
        this.wsMin = wsMin;
    }
    
    /**
     * Set all fields to an empty value.
     * The empty value is trimmed spaces for {@code String} and {@code char} fields and zero for numeric fields, recursively applied to classes and arrays.
     */
    public void reset() {
        wsHour = "";
        wsTmSep = ' ';
        wsMin = "";
    }
    
    public String toString() {
        StringBuilder s = new StringBuilder();
        s.append("{ wsHour=\"");
        s.append(getWsHour());
        s.append("\"");
        s.append(", wsTmSep=\"");
        s.append(getWsTmSep());
        s.append("\"");
        s.append(", wsMin=\"");
        s.append(getWsMin());
        s.append("\"");
        s.append("}");
        return s.toString();
    }
    
    private boolean equals(WsTime that) {
        return this.wsHour.equals(that.wsHour) &&
            this.wsTmSep == that.wsTmSep &&
            this.wsMin.equals(that.wsMin);
    }
    
    /** Per-field equality comparison; only returns equal if argument is the same class */
    @Override
    public boolean equals(Object that) {
        return (that instanceof WsTime other) && other.canEqual(this) && this.equals(other);
    }
    
    /** Does {@code that} have the same runtime type as {@code this}? */
    public boolean canEqual(Object that) {
        return that instanceof WsTime;
    }
    
    @Override
    public int hashCode() {
        int result = 17;
        result = 31 * result + Objects.hashCode(wsHour);
        result = 31 * result + Character.hashCode(wsTmSep);
        result = 31 * result + Objects.hashCode(wsMin);
        return result;
    }
    
    @Override
    public int compareTo(WsTime that) {
        int c = 0;
        c = this.wsHour.compareTo(that.wsHour);
        if ( c != 0 ) return c;
        c = Character.compare(this.wsTmSep, that.wsTmSep);
        if ( c != 0 ) return c;
        c = this.wsMin.compareTo(that.wsMin);
        if ( c == 0 && !(that.canEqual(this) && this.canEqual(that)) ) c = this.getClass().getTypeName().compareTo(that.getClass().getTypeName());
        return c;
    }
    
    // Start of COBOL-compatible binary serialization metadata
    private static CobolDatatypeFactory factory = new CobolDatatypeFactory();
    static {
        factory.setStringTrimDefault(false);
        factory.setStringEncoding(encoding.name());
    }
    
    private static final StringField WS_HOUR = factory.getStringField(2);
    private static final StringField WS_TM_SEP = factory.getStringField(1);
    private static final StringField WS_MIN = factory.getStringField(2);
    public static final int SIZE = factory.getOffset();
    // End of COBOL-compatible binary serialization metadata
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code WsTime} object
     * @param bytes  preallocated byte array to store the object serialization
     * @param offset offset in the byte array where serialization should begin
     * @return the byte array, updated with the newly added data formatted as in the {@code WS-TIME} record
     * @see "WS-TIME record at RXBPA180.cbl:140"
     */
    public byte[] getBytes(byte[] bytes, int offset) {
        WS_HOUR.putString(wsHour, bytes, offset);
        WS_TM_SEP.putString(Character.toString(wsTmSep), bytes, offset);
        WS_MIN.putString(wsMin, bytes, offset);
        return bytes;
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code WsTime} object,
     * starting at the beginning of the array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes(byte[] bytes) {
        return getBytes(bytes, 0);
    }
    
    /**
     * Retrieves a COBOL-format byte array representation of the {@code WsTime} object,
     * allocating a new array
     * @see #getBytes(byte[], int)
     */
    public final byte[] getBytes() {
        return getBytes(new byte[numBytes()]);
    }
    
    /**
     * Retrieves a COBOL-format string representation of the {@code WsTime} object
     * @return the result of {@link #getBytes()} interpreted using the IBM-1047 EBCDIC encoding
     */
    public final String toByteString() {
        return new String(getBytes(), encoding);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array
     * @param bytes  byte array formatted as in the {@code WS-TIME} record; will be space-padded to length if necessary
     * @param offset offset in the byte array where deserialization should begin
     * @see "WS-TIME record at RXBPA180.cbl:140"
     */
    public void setBytes(byte[] bytes, int offset) {
        if (bytes.length < SIZE + offset) {
            byte[] newBytes = Arrays.copyOf(bytes, SIZE + offset);
            Arrays.fill(newBytes, bytes.length, SIZE + offset, (byte)0x40 /*default EBCDIC space character*/);
            bytes = newBytes;
        }
        wsHour = WS_HOUR.getString(bytes, offset).replaceFirst("\\s+\\z", "");
        wsTmSep = WS_TM_SEP.getString(bytes, offset).charAt(0);
        wsMin = WS_MIN.getString(bytes, offset).replaceFirst("\\s+\\z", "");
    }
    
    
    /**
     * Updates the fields of this instance from a COBOL-format byte array,
     * starting at the beginning of the array
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(byte[] bytes) {
        setBytes(bytes, 0);
    }
    
    /**
     * Updates the fields of this instance from a COBOL-format string.
     * The string is first converted to a byte array using the IBM-1047 EBCDIC encoding.
     * @see #setBytes(byte[], int)
     */
    public final void setBytes(String bytes) {
        setBytes(bytes.getBytes(encoding));
    }
    
    /** The number of bytes required for a COBOL-format byte array representing this object */
    public int numBytes() {
        return SIZE;
    }
    
}
